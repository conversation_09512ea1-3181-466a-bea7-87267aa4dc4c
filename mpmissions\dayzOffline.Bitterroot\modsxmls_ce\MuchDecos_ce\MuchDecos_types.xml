<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<types>
	<type name="MD_Workbench_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Workbench">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodTable_Indoor_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodTable_Indoor">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileLarge_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileLarge">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileSmall_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileSmall">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileMossy_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodPileMossy">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodReserve_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodReserve">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_DogHouse_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_DogHouse">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_FeedRack_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_FeedRack">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BarbedWireFence_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BarbedWireFence">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BeigeCouch_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BeigeCouch">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WhiteCouch_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WhiteCouch">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LeatherSofaOld_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LeatherSofaOld">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LeatherSofaNew_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LeatherSofaNew">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LobbyTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LobbyTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SquareTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SquareTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WornTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WornTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Scarecrow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GraveCross_One">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GraveCross_Two">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GraveCross_Three">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GraveCross_Four">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GraveCross_Five">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodBlock">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_HayStack">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampBench">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LogBench">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WornBench">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_EvergreenFlowerPot">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SnakePlantFlowerPot">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Sink_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Sink">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PostBox_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PostBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PostBoxWall_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PostBoxWall">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BlueBench_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BlueBench">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Sunshade_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Sunshade">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_BE_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_BE">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_BN_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_BN">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_RE_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_RE">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_RN_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_RN">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_SE_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_SE">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CamonetShelter_SN_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CamonetShelter_SN">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Double_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Double">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Folding_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Folding">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Wood_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bed_Wood">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BrownWardrobe_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_BrownWardrobe">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GreenWardrobe_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GreenWardrobe">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_StudentChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_StudentChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_RedChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_RedChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LobbyChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LobbyChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TentChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TentChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodenChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WoodenChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PaddedWoodenChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_PaddedWoodenChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenChair_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenChair">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeRailing_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeRailing">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeFenceSmall_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeFenceSmall">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeFenceLarge_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_PipeFenceLarge">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_WoodPole_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_WoodPole">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_WoodPole_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_WoodPole">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Vineyard_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Vineyard">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Forest_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Forest">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_Forest_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_Forest">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_WoodGrid_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_WoodGrid">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_WoodGrid_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_WoodGrid">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_MetalGrid_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_MetalGrid">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_MetalGrid_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_MetalGrid">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Field_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Field">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_Field_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_End_Field">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Gate_Forest_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Fence_Gate_Forest">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Gate_TallSliding_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Gate_TallSliding">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Gate_ShortSliding_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Gate_ShortSliding">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_MetalFence1_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_MetalFence1">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_MetalFence2_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_MetalFence2">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_TinFence1_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_TinFence1">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_TinFence2_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Double_Gate_TinFence2">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_NewsStand1_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_NewsStand1">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_NewsStand2_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_NewsStand2">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_FastFoodStand_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_FastFoodStand">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GroceryStand_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_GroceryStand">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampHouse_White_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CampHouse_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampHouse_Brown_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CampHouse_Brown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampHouse_Red_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_CampHouse_Red">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Caravan_WGreen_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Caravan_WGreen">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Caravan_WBrown_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Caravan_WBrown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Caravan_MGreen_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Caravan_MGreen">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Caravan_MRust_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Caravan_MRust">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Polytunnel_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Polytunnel">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Greenhouse_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Greenhouse">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_M1_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_M1">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_M3_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_M3">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_M4_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_M4">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_W1_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_W1">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_W2_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_W2">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_W3_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_W3">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_W4_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_W4">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Shed_W5_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Land_MD_Shed_W5">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_LightBlue_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_LightBlue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_DarkBlue_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_DarkBlue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_ThinBlue_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_ThinBlue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_SmallMetal_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_SmallMetal">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_LargeMetal_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_SlumShelter_LargeMetal">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_StudentDesk_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_StudentDesk">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TeacherDesk_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TeacherDesk">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LabDesk_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_LabDesk">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampTable_Small_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampTable_Small">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampTable_Long_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_CampTable_Long">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TableDrawer_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TableDrawer">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_ConferenceTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_ConferenceTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeDesk_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OfficeDesk">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenCheckeredTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenCheckeredTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_KitchenTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_MarketTable_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_MarketTable">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Medical_Tent_Large_Kit">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Medical_Tent_Large">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Box_C">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TrashCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_GreenTrashCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_StaticTrashCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Small_WoodBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Large_WoodBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Flat_WoodBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Bucket">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_WhiteFridge">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_DoubleFridge">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_DarkWoodenCrate">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_DeskGlobe">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TV">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OldRadio">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Radio">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Scale">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Cashier">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Clock">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_TV">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_OldRadio">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Radio">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Scale">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Cashier">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Clock">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_ClothBag">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="MD_Canister">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
</types>
