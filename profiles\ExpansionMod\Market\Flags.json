{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_FLAGS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "flag_chernarus", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_chedaki", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_napa", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_cdf", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_livonia", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_altis", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_s<PERSON><PERSON>ni", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_n<PERSON><PERSON>ni", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_dayz", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_livoniaarmy", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_white", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_bohemia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_apa", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_uec", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_pirates", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_cannibals", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_bear", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_wolf", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_babydeer", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_rooster", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_livoniapolice", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_cmc", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_tec", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_chel", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_zenit", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_hunterz", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_brainz", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_refuge", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_rsta", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_snake", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_crook", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_rex", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flag_zagorky", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_expansion", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_white", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_australia", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_canada", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_chernarus", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_france", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_germany", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_latvia", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_luxembourg", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_mexico", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_netherlands", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_newzealand", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_norway", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_poland", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_russia", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_uk", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_usa", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_scotland", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_finland", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_sweden", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_spain", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_denmark", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_brazil", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_portugal", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_belgium", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_livonia", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_japan", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_china", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_southkorea", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_un", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_nato", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_pirate", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_chedaki", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_napa", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_cdf", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_nuevorico", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_borduria", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_biohazard", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_anyone<PERSON><PERSON>no", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_ireland", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_wales", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_switzerland", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_srilanka", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_southafrica", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_sicily", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_offwithhead", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_gibraltar", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_czechia", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_fari", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_dayzwhite", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_dayzblack", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_doubleaxe", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_grenade", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_red", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_blue", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_green", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_yellow", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_orange", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_pink", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_purple", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_flag_rainbow", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_AnyoneinCherno", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Argentina", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Australia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Belgium", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Biohazard", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Blue", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Borduriens", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Brazil", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Canada", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Chernarus", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_China", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Croatia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Czech", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Denmark", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_DontTreadOnMe", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Finland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_France", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Germany", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Green", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Hungary", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Iceland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Ireland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Israel", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Italy", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Japan", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Livonia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Luxembourg", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Mexico", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_NATO", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Netherlands", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_NewZealand", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Norway", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Orange", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Pirates", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Poland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Portugal", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Purple", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Red", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Russia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Scotland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Slovakia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Slovenia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_SouthAfrica", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Spain", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Sweden", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Switzerland", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Syldavia", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Turkey", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_UK", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_UN", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_USA", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Ukraine", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Wales", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_White", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Yellow", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_CDF", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_DayZBlack", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_DayZWhite", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_DoubleAxe", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Expansion", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_NAPA", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_NuevoRico", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_OffWithHead", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Pink", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Pirate", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Rainbow", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Scottish", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Sicily", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Skilanka", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_SouthKorea", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Srilanka", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Expansion_Flag_Swedish", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}