{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_BACKPACKS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "childbag_red", "MaxPriceThreshold": 1280, "MinPriceThreshold": 765, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["childbag_blue", "childbag_green"]}, {"ClassName": "childbag_blue", "MaxPriceThreshold": 1280, "MinPriceThreshold": 765, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "childbag_green", "MaxPriceThreshold": 1280, "MinPriceThreshold": 765, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "drybag_orange", "MaxPriceThreshold": 3385, "MinPriceThreshold": 2030, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["drybag_yellow", "drybag_blue", "drybag_black", "drybag_red", "drybag_green"]}, {"ClassName": "drybag_black", "MaxPriceThreshold": 3415, "MinPriceThreshold": 2050, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "drybag_green", "MaxPriceThreshold": 3445, "MinPriceThreshold": 2065, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "taloonbag_blue", "MaxPriceThreshold": 2100, "MinPriceThreshold": 1260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["taloonbag_orange", "taloonbag_violet", "taloonbag_green"]}, {"ClassName": "taloonbag_orange", "MaxPriceThreshold": 1485, "MinPriceThreshold": 890, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "taloonbag_green", "MaxPriceThreshold": 1485, "MinPriceThreshold": 890, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "smershbag", "MaxPriceThreshold": 5695, "MinPriceThreshold": 3415, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "assaultbag_black", "MaxPriceThreshold": 10330, "MinPriceThreshold": 6195, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["assaultbag_green", "assaultbag_ttsko", "assaultbag_winter"]}, {"ClassName": "assaultbag_winter", "MaxPriceThreshold": 1980, "MinPriceThreshold": 1190, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingbag", "MaxPriceThreshold": 6100, "MinPriceThreshold": 3660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["huntingbag_hannah"]}, {"ClassName": "waterproofbag_green", "MaxPriceThreshold": 605, "MinPriceThreshold": 365, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["waterproofbag_orange", "waterproofbag_yellow"]}, {"ClassName": "tortillabag", "MaxPriceThreshold": 1945, "MinPriceThreshold": 1170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansiondeserttortilla", "tortillabag_winter"]}, {"ClassName": "coyotebag_brown", "MaxPriceThreshold": 13030, "MinPriceThreshold": 7820, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["coyotebag_green", "coyotebag_winter", "expansioncoyoteblack"]}, {"ClassName": "coyotebag_winter", "MaxPriceThreshold": 1980, "MinPriceThreshold": 1190, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mountainbag_red", "MaxPriceThreshold": 1705, "MinPriceThreshold": 1025, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["mountainbag_blue", "mountainbag_orange", "mountainbag_green"]}, {"ClassName": "alicebag_green", "MaxPriceThreshold": 5840, "MinPriceThreshold": 3505, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["alicebag_black", "alicebag_camo"]}, {"ClassName": "alicebag_black", "MaxPriceThreshold": 5840, "MinPriceThreshold": 3505, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "alicebag_camo", "MaxPriceThreshold": 5840, "MinPriceThreshold": 3505, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "slingbag_black", "MaxPriceThreshold": 1545, "MinPriceThreshold": 925, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["slingbag_brown", "slingbag_gray"]}, {"ClassName": "duffelbagsmall_camo", "MaxPriceThreshold": 8980, "MinPriceThreshold": 5390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["duffelbagsmall_green", "duffelbagsmall_medical"]}, {"ClassName": "canvasbag_medical", "MaxPriceThreshold": 5540, "MinPriceThreshold": 3325, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["canvasbag_olive"]}, {"ClassName": "armypouch_beige", "MaxPriceThreshold": 4305, "MinPriceThreshold": 2580, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["armypouch_black", "armypouch_camo", "armypouch_green"]}]}