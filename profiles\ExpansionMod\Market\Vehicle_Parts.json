{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_VEHICLE_PARTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "headlighth7_box", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "headlighth7", "MaxPriceThreshold": 985, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "carradiator", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tirerepairkit", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sparkplug", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "glowplug", "MaxPriceThreshold": 1135, "MinPriceThreshold": 680, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionigniterplug", "MaxPriceThreshold": 1135, "MinPriceThreshold": 680, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionhydraulichoses", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hatchbackhood", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchbackhood_blue", "hatchbackhood_bluerust", "hatchbackhood_greenrust", "hatchbackhood_white", "hatchbackhood_whiterust"]}, {"ClassName": "hatchbacktrunk", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchbacktrunk_blue", "hatchbacktrunk_bluerust", "hatchbacktrunk_greenrust", "hatchbacktrunk_white", "hatchbacktrunk_whiterust"]}, {"ClassName": "hatchbackdoors_driver", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchbackdoors_driver_blue", "hatchbackdoors_driver_bluerust", "hatchbackdoors_driver_greenrust", "hatchbackdoors_driver_white", "hatchbackdoors_driver_whiterust"]}, {"ClassName": "hatchbackdoors_codriver", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchbackdoors_codriver_blue", "hatchbackdoors_codriver_bluerust", "hatchbackdoors_codriver_greenrust", "hatchbackdoors_codriver_white", "hatchbackdoors_codriver_whiterust"]}, {"ClassName": "hatchbackwheel", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hatchback_02_hood", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_hood_black", "hatchback_02_hood_blackrust", "hatchback_02_hood_redrust", "hatchback_02_hood_blue", "hatchback_02_hood_bluerust"]}, {"ClassName": "hatchback_02_trunk", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_trunk_black", "hatchback_02_trunk_blackrust", "hatchback_02_trunk_redrust", "hatchback_02_trunk_blue", "hatchback_02_trunk_bluerust"]}, {"ClassName": "hatchback_02_door_1_1", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_door_1_1_black", "hatchback_02_door_1_1_blackrust", "hatchback_02_door_1_1_redrust", "hatchback_02_door_1_1_blue", "hatchback_02_door_1_1_bluerust"]}, {"ClassName": "hatchback_02_door_1_2", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_door_1_2_black", "hatchback_02_door_1_2_blackrust", "hatchback_02_door_1_2_redrust", "hatchback_02_door_1_2_blue", "hatchback_02_door_1_2_bluerust"]}, {"ClassName": "hatchback_02_door_2_1", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_door_2_1_black", "hatchback_02_door_2_1_blackrust", "hatchback_02_door_2_1_redrust", "hatchback_02_door_2_1_blue", "hatchback_02_door_2_1_bluerust"]}, {"ClassName": "hatchback_02_door_2_2", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hatchback_02_door_2_2_black", "hatchback_02_door_2_2_blackrust", "hatchback_02_door_2_2_redrust", "hatchback_02_door_2_2_blue", "hatchback_02_door_2_2_bluerust"]}, {"ClassName": "hatchback_02_wheel", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "civsedanhood", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civsedanhood_wine", "civsedanhood_winerust", "civsedanhood_whiterust", "civsedanhood_black", "civsedanhood_blackrust"]}, {"ClassName": "civsedantrunk", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civsedantrunk_wine", "civsedantrunk_winerust", "civsedantrunk_whiterust", "civsedant<PERSON>_black", "civsedantrunk_blackrust"]}, {"ClassName": "civ<PERSON><PERSON><PERSON><PERSON>_driver", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civ<PERSON><PERSON><PERSON><PERSON>_driver_wine", "civ<PERSON><PERSON><PERSON><PERSON>_driver_winerust", "civ<PERSON><PERSON><PERSON><PERSON>_driver_whiterust", "civ<PERSON><PERSON><PERSON><PERSON>_driver_black", "civ<PERSON><PERSON><PERSON><PERSON>_driver_blackrust"]}, {"ClassName": "civsedandoors_codriver", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civsedandoors_codriver_wine", "civsedandoors_codriver_winerust", "civsedand<PERSON>s_codriver_whiterust", "civ<PERSON><PERSON><PERSON><PERSON>_codriver_black", "civsedand<PERSON><PERSON>_codriver_blackrust"]}, {"ClassName": "civsedandoors_backleft", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civsedandoors_backleft_wine", "civsedandoors_backleft_winerust", "civsedand<PERSON>s_backleft_whiterust", "civsedand<PERSON><PERSON>_backleft_black", "civsedand<PERSON>s_backleft_blackrust"]}, {"ClassName": "civ<PERSON><PERSON><PERSON><PERSON>_backright", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["civsedand<PERSON>s_backright_wine", "civsedand<PERSON>s_backright_winerust", "civ<PERSON><PERSON><PERSON><PERSON>_backright_whiterust", "civ<PERSON><PERSON><PERSON><PERSON>_backright_black", "civ<PERSON><PERSON><PERSON><PERSON>_backright_blackrust"]}, {"ClassName": "civsedanwheel", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sedan_02_hood", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_hood_red", "sedan_02_hood_redrust", "sedan_02_hood_yellowrust", "sedan_02_hood_grey", "sedan_02_hood_greyrust"]}, {"ClassName": "sedan_02_trunk", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_trunk_red", "sedan_02_trunk_redrust", "sedan_02_trunk_yellowrust", "sedan_02_trunk_grey", "sedan_02_trunk_greyrust"]}, {"ClassName": "sedan_02_door_1_1", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_door_1_1_red", "sedan_02_door_1_1_redrust", "sedan_02_door_1_1_yellowrust", "sedan_02_door_1_1_grey", "sedan_02_door_1_1_greyrust"]}, {"ClassName": "sedan_02_door_1_2", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_door_1_2_red", "sedan_02_door_1_2_redrust", "sedan_02_door_1_2_yellowrust", "sedan_02_door_1_2_grey", "sedan_02_door_1_2_greyrust"]}, {"ClassName": "sedan_02_door_2_1", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_door_2_1_red", "sedan_02_door_2_1_redrust", "sedan_02_door_2_1_yellowrust", "sedan_02_door_2_1_grey", "sedan_02_door_2_1_greyrust"]}, {"ClassName": "sedan_02_door_2_2", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sedan_02_door_2_2_red", "sedan_02_door_2_2_redrust", "sedan_02_door_2_2_yellowrust", "sedan_02_door_2_2_grey", "sedan_02_door_2_2_greyrust"]}, {"ClassName": "sedan_02_wheel", "MaxPriceThreshold": 220, "MinPriceThreshold": 130, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "truck_01_hood", "MaxPriceThreshold": 645, "MinPriceThreshold": 385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["truck_01_hood_blue", "truck_01_hood_orange", "truck_01_hood_orangerust", "truck_01_hood_bluerust", "truck_01_hood_greenrust"]}, {"ClassName": "truck_01_door_1_1", "MaxPriceThreshold": 645, "MinPriceThreshold": 385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["truck_01_door_1_1_blue", "truck_01_door_1_1_orange", "truck_01_door_1_1_orangerust", "truck_01_door_1_1_bluerust", "truck_01_door_1_1_greenrust"]}, {"ClassName": "truck_01_door_2_1", "MaxPriceThreshold": 645, "MinPriceThreshold": 385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["truck_01_door_2_1_blue", "truck_01_door_2_1_orange", "truck_01_door_2_1_orangerust", "truck_01_door_2_1_bluerust", "truck_01_door_2_1_greenrust"]}, {"ClassName": "truck_01_wheel", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "truck_01_wheeldouble", "MaxPriceThreshold": 220, "MinPriceThreshold": 130, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "offroad_02_hood", "MaxPriceThreshold": 480, "MinPriceThreshold": 290, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "offroad_02_trunk", "MaxPriceThreshold": 3600, "MinPriceThreshold": 2160, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["offroad_02_trunk_rust"]}, {"ClassName": "offroad_02_door_1_1", "MaxPriceThreshold": 3600, "MinPriceThreshold": 2160, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["offroad_02_door_1_1_rust"]}, {"ClassName": "offroad_02_door_1_2", "MaxPriceThreshold": 3600, "MinPriceThreshold": 2160, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["offroad_02_door_1_2_rust"]}, {"ClassName": "offroad_02_door_2_1", "MaxPriceThreshold": 3600, "MinPriceThreshold": 2160, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["offroad_02_door_2_1_rust"]}, {"ClassName": "offroad_02_door_2_2", "MaxPriceThreshold": 3600, "MinPriceThreshold": 2160, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["offroad_02_door_2_2_rust"]}, {"ClassName": "offroad_02_wheel", "MaxPriceThreshold": 11410, "MinPriceThreshold": 6845, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontractordoorsdriver", "MaxPriceThreshold": 800, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontractordoorscodriver", "MaxPriceThreshold": 800, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontractorbackwheel", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontractorfrontwheel", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazdoorhood", "MaxPriceThreshold": 800, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazdoordriver", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazdoorcodriver", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazdoorcargo1", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazdoorcargo2", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuazwheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_hood", "MaxPriceThreshold": 800, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_driverdoor", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_codriverdoor", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_left", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_right", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_trunk", "MaxPriceThreshold": 800, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_landrover_wheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionvodnikdoordriver", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionvodnikdoorcodriver", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionvodnikwheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionmerlinbackwheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionmerlinfrontwheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuh1hdoor_1_1", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuh1hdoor_1_2", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuh1hdoor_2_1", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionuh1hdoor_2_2", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_mh6_door_1_1", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansion_mh6_door_1_1_black"]}, {"ClassName": "expansion_mh6_door_1_2", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansion_mh6_door_1_2_black"]}, {"ClassName": "expansion_mh6_door_2_1", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansion_mh6_door_2_1_black"]}, {"ClassName": "expansion_mh6_door_2_2", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansion_mh6_door_2_2_black"]}, {"ClassName": "expansionbuswheel", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionbuswheeldouble", "MaxPriceThreshold": 1200, "MinPriceThreshold": 600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncarkey", "MaxPriceThreshold": 500, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionkeychain_red", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionkeychain_white", "expansionkeychain_black", "expansionkeychain_blue", "expansionkeychain_green", "expansionkeychain_grey", "expansionkeychain_orange", "expansionkeychain_pink", "expansionkeychain_purple", "expansionkeychain_yellow"]}]}