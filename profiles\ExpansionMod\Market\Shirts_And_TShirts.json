{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_SHIRTS & #STR_EXPANSION_MARKET_CATEGORY_TSHIRTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "tshirt_blue", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["tshirt_orangewhitestripes", "tshirt_red", "tshirt_redblackstripes", "tshirt_beige", "tshirt_grey", "tshirt_black", "tshirt_green", "tshirt_white"]}, {"ClassName": "tshirt_orangewhitestripes", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tshirt_red", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tshirt_redblackstripes", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tshirt_beige", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tshirt_grey", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tshirt_black", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["tshirt_10thanniversary"]}, {"ClassName": "tshirt_green", "MaxPriceThreshold": 740, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontee", "MaxPriceThreshold": 950, "MinPriceThreshold": 550, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "telnyashkashirt", "MaxPriceThreshold": 355, "MinPriceThreshold": 210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shirt_bluecheck", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["shirt_bluecheckbright", "shirt_redcheck", "shirt_whitecheck", "shirt_planeblack", "shirt_greencheck"]}, {"ClassName": "shirt_bluecheckbright", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shirt_redcheck", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shirt_whitecheck", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shirt_planeblack", "MaxPriceThreshold": 1575, "MinPriceThreshold": 945, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shirt_greencheck", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "medicals<PERSON><PERSON><PERSON>shirt_blue", "MaxPriceThreshold": 1080, "MinPriceThreshold": 650, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_green", "medicals<PERSON><PERSON><PERSON><PERSON>_white"]}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_green", "MaxPriceThreshold": 1080, "MinPriceThreshold": 650, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "medicals<PERSON><PERSON><PERSON><PERSON>_white", "MaxPriceThreshold": 1080, "MinPriceThreshold": 650, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "chernarussportshirt", "MaxPriceThreshold": 390, "MinPriceThreshold": 235, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tacticalshirt_grey", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["tacticalshirt_tan", "tacticalshirt_black", "tacticalshirt_olive"]}, {"ClassName": "tacticalshirt_tan", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tacticalshirt_black", "MaxPriceThreshold": 11545, "MinPriceThreshold": 6925, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tacticalshirt_olive", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}