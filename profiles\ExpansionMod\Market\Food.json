{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_FOOD", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "zagorky", "MaxPriceThreshold": 2045, "MinPriceThreshold": 1225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "zagorkychocolate", "MaxPriceThreshold": 1300, "MinPriceThreshold": 780, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON>agorky<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 665, "MinPriceThreshold": 400, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "powderedmilk", "MaxPriceThreshold": 1565, "MinPriceThreshold": 940, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "boxcerealcrunchin", "MaxPriceThreshold": 1065, "MinPriceThreshold": 640, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rice", "MaxPriceThreshold": 930, "MinPriceThreshold": 560, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "marmalade", "MaxPriceThreshold": 1130, "MinPriceThreshold": 675, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "honey", "MaxPriceThreshold": 1790, "MinPriceThreshold": 1075, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "saltysticks", "MaxPriceThreshold": 1165, "MinPriceThreshold": 700, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "crackers", "MaxPriceThreshold": 1165, "MinPriceThreshold": 700, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "chips", "MaxPriceThreshold": 1320, "MinPriceThreshold": 795, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pajka", "MaxPriceThreshold": 1165, "MinPriceThreshold": 700, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pate", "MaxPriceThreshold": 1165, "MinPriceThreshold": 700, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "brisketspread", "MaxPriceThreshold": 1320, "MinPriceThreshold": 795, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sardinescan", "MaxPriceThreshold": 1275, "MinPriceThreshold": 765, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tunacan", "MaxPriceThreshold": 1840, "MinPriceThreshold": 1105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "dogfoodcan", "MaxPriceThreshold": 1715, "MinPriceThreshold": 1030, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "catfoodcan", "MaxPriceThreshold": 1790, "MinPriceThreshold": 1075, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "porkcan", "MaxPriceThreshold": 1715, "MinPriceThreshold": 1030, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "lunchmeat", "MaxPriceThreshold": 1585, "MinPriceThreshold": 950, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "unknownfoodcan", "MaxPriceThreshold": 520, "MinPriceThreshold": 315, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "peachescan", "MaxPriceThreshold": 1550, "MinPriceThreshold": 930, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "spaghettican", "MaxPriceThreshold": 1790, "MinPriceThreshold": 1075, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bakedbeanscan", "MaxPriceThreshold": 1560, "MinPriceThreshold": 935, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tacticalbaconcan", "MaxPriceThreshold": 2070, "MinPriceThreshold": 1240, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "crabcan", "MaxPriceThreshold": 290, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionbread1", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionbread2", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionbread3", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncheese1", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncheese2", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncheese3", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncheese4", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}