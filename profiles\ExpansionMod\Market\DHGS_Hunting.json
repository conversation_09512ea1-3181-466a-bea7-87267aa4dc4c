{"m_Version": 12, "DisplayName": "DHGS_Hunting", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "D<PERSON>_<PERSON>_Bear_400lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_410LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_420LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_430LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_440LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_450LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_460LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_470LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_480LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_490LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_500LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_510LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_520LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_530LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_540LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_550LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_560LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_570LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_580LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Bear_590LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Bear_600LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Bear_Polar", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Bear_Black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_150LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_160LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_170LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_180LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_190LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_200LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_210LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Boar_220LB", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Doe_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Doe_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Doe_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Doe_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON>_Doe_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_260lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_270lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_280lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_290lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_300lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_310lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_320lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_330lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_340lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_350lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_360lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Hind_370lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_120lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roe<PERSON>ck_130lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_140lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_150lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roe<PERSON>ck_160lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roe<PERSON><PERSON>_170lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_Roebuck_180lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_300lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Stag_310lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_320lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_330lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_340lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_350lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_360lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_370lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_380lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_390lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_400lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_410lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Stag_420lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Skull_Stag_430lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON>_Stag_440lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_120lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_130lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_140lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_M_150lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_F_60lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_F_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_F_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_F_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>_<PERSON>_F_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_400lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_410lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_420lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_430lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_440lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_450lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_460lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_470lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_480lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_490lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_500lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_510lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_520lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_530lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_540lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_550lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_560lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_570lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_580lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_590lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_600lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_Black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BearPelt_Polar", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_150lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_160lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_170lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_180lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_190lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_200lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_210lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_BoarPelt_220lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DoePelt_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DoePelt_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DoePelt_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DoePelt_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DoePelt_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_260lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_270lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_280lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_290lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_300lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_310lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_320lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_330lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_340lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_350lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_360lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_HindPelt_370lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Pisau", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RamboKnife", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Buck120", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_120lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_130lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_140lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_150lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_160lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_170lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_RoeBuckPelt_180lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_300lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_310lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_320lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_330lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_340lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_350lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_360lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_370lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_380lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_390lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_400lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_410lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_420lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_430lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_DeerPelt_440lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Standart", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Kitty", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Sand", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Chaotic", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TacticKnife_Gradient", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_TaxidermyKit", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_M_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_M_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "D<PERSON>_<PERSON><PERSON>t_M_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_M_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_M_110lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_M_120lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON><PERSON>_M_130lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_M_140lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_M_150lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_F_60lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_F_70lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_F_80lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_<PERSON><PERSON>t_F_90lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "DH_Wolf<PERSON>elt_F_100lb", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SkullMount_Kit", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SkullMount", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}