{"ConfigVersion": 22, "ID": 22, "Type": 1, "Title": "Quest chain example [Part 1]", "Descriptions": ["This is a simple quest chain example quest where the player can process thrue a quests without visiting a quest NPC.", "Go to location A.", "You reached location A and now you can accept the next quest when you complete this one!"], "ObjectiveText": "Go to location A", "FollowUpQuest": 23, "Repeatable": 0, "IsDailyQuest": 0, "IsWeeklyQuest": 0, "CancelQuestOnPlayerDeath": 0, "Autocomplete": 0, "IsGroupQuest": 0, "ObjectSetFileName": "", "QuestItems": [], "Rewards": [], "NeedToSelectReward": 0, "RandomReward": 0, "RandomRewardAmount": -1, "RewardsForGroupOwnerOnly": 1, "RewardBehavior": 0, "QuestGiverIDs": [1], "QuestTurnInIDs": [], "IsAchievement": 0, "Objectives": [{"ConfigVersion": 28, "ID": 5, "ObjectiveType": 3}], "QuestColor": 0, "ReputationReward": 0, "ReputationRequirement": -1, "PreQuestIDs": [], "RequiredFaction": "", "FactionReward": "", "PlayerNeedQuestItems": 1, "DeleteQuestItems": 1, "SequentialObjectives": 1, "FactionReputationRequirements": {}, "FactionReputationRewards": {}, "SuppressQuestLogOnCompetion": 0, "Active": 1}