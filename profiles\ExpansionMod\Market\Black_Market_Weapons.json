{"m_Version": 12, "DisplayName": "Black_Market_Weapons", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "SNAFUAlligator", "MaxPriceThreshold": 1600000, "MinPriceThreshold": 1500000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M200", "MaxPriceThreshold": 569000, "MinPriceThreshold": 568000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["GCGN_M200_Tan"]}, {"ClassName": "GCGN_M700", "MaxPriceThreshold": 490000, "MinPriceThreshold": 490000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M82_Barret", "MaxPriceThreshold": 480000, "MinPriceThreshold": 480000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAX50_GUN", "MaxPriceThreshold": 570000, "MinPriceThreshold": 570000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUGM6Lynx", "MaxPriceThreshold": 450000, "MinPriceThreshold": 450000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUGevar_Grey", "MaxPriceThreshold": 360000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["SNAFUGevar_Black", "SNAFUGevar_Green", "SNAFUGevar_Tan"]}, {"ClassName": "SNAFUKivaari_Black_GUN", "MaxPriceThreshold": 470000, "MinPriceThreshold": 470000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["SNAFUKivaari_Tan_GUN", "SNAFUKivaari_Grey_GUN", "SNAFUKivaari_Snow_GUN"]}, {"ClassName": "SNAFUM24_GUN", "MaxPriceThreshold": 450000, "MinPriceThreshold": 450000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUM98B_Atac_GUN", "MaxPriceThreshold": 440000, "MinPriceThreshold": 440000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["SNAFUM98B_Digi_GUN", "SNAFUM98B_Ger_GUN", "SNAFUM98B_Mc_GUN", "SNAFUM98B_Snow_GUN", "SNAFUM98B_Wood_GUN"]}, {"ClassName": "SNAFUT5000_Dkmcc_GUN", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["SNAFUT5000_Snow_GUN", "SNAFUT5000_Wood_GUN"]}, {"ClassName": "SNAFU_AS50_GUN", "MaxPriceThreshold": 550000, "MinPriceThreshold": 550000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M1918A2", "MaxPriceThreshold": 380000, "MinPriceThreshold": 380000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M200Green", "MaxPriceThreshold": 370000, "MinPriceThreshold": 370000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["SNAFU_M200SNOW", "SNAFUM1A_Tan"]}, {"ClassName": "SNAFUVSSK", "MaxPriceThreshold": 360000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_Anzio20", "MaxPriceThreshold": 680000, "MinPriceThreshold": 680000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_AEK999", "MaxPriceThreshold": 555000, "MinPriceThreshold": 555000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_M240", "MaxPriceThreshold": 560000, "MinPriceThreshold": 560000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_M249", "MaxPriceThreshold": 460000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_PKM", "MaxPriceThreshold": 380000, "MinPriceThreshold": 380000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_PKP", "MaxPriceThreshold": 380000, "MinPriceThreshold": 380000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TF_MAR10", "MaxPriceThreshold": 450000, "MinPriceThreshold": 450000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M200_Black", "MaxPriceThreshold": 370000, "MinPriceThreshold": 370000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M200_Grey", "MaxPriceThreshold": 370000, "MinPriceThreshold": 370000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M200_White", "MaxPriceThreshold": 320000, "MinPriceThreshold": 320000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M200_7Rnd", "MaxPriceThreshold": 327000, "MinPriceThreshold": 327000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M82_Grey", "MaxPriceThreshold": 360000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M82_Black", "MaxPriceThreshold": 365000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M82_Green", "MaxPriceThreshold": 365000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M82_Tan", "MaxPriceThreshold": 365000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M82_10Rnd", "MaxPriceThreshold": 350020, "MinPriceThreshold": 340200, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AX50_Black", "MaxPriceThreshold": 360000, "MinPriceThreshold": 360000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AX50_Grey", "MaxPriceThreshold": 355000, "MinPriceThreshold": 355000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AX50_Tan", "MaxPriceThreshold": 355000, "MinPriceThreshold": 355000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AX50_7Rnd", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Lynx_Black", "MaxPriceThreshold": 365000, "MinPriceThreshold": 365000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Lynx_5Rnd", "MaxPriceThreshold": 357899, "MinPriceThreshold": 352200, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Gevar", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Gevar_7Rnd", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AS50", "MaxPriceThreshold": 359000, "MinPriceThreshold": 359000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AS50_5Rnd", "MaxPriceThreshold": 315000, "MinPriceThreshold": 315000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_VSSK", "MaxPriceThreshold": 380000, "MinPriceThreshold": 380000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_VSSK_5Rnd", "MaxPriceThreshold": 311500, "MinPriceThreshold": 311500, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Anzio", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Anzio_Black", "MaxPriceThreshold": 480000, "MinPriceThreshold": 480000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Anzio_3Rnd", "MaxPriceThreshold": 415000, "MinPriceThreshold": 415000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SnowOwl", "MaxPriceThreshold": 353000, "MinPriceThreshold": 353000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SnowOwl_10Rnd", "MaxPriceThreshold": 315000, "MinPriceThreshold": 315000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M240", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_M240_250Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 17000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M249", "MaxPriceThreshold": 255000, "MinPriceThreshold": 255000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_M249_200Rnd", "MaxPriceThreshold": 19800, "MinPriceThreshold": 19800, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M60", "MaxPriceThreshold": 270000, "MinPriceThreshold": 270000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_M60_150Rnd", "MaxPriceThreshold": 18000, "MinPriceThreshold": 18000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_PKM", "MaxPriceThreshold": 280000, "MinPriceThreshold": 280000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_PKM_250Rnd", "MaxPriceThreshold": 11100, "MinPriceThreshold": 11100, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_PKP", "MaxPriceThreshold": 366000, "MinPriceThreshold": 366000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_PKP_250Rnd", "MaxPriceThreshold": 11000, "MinPriceThreshold": 11000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SR25_Black", "MaxPriceThreshold": 317000, "MinPriceThreshold": 317000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SR25_Tan", "MaxPriceThreshold": 317000, "MinPriceThreshold": 317000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SR25_Green", "MaxPriceThreshold": 317000, "MinPriceThreshold": 317000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_SR25_25Rnd", "MaxPriceThreshold": 8000, "MinPriceThreshold": 8000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Beowulf", "MaxPriceThreshold": 494500, "MinPriceThreshold": 494500, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Ammo_50<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 44000, "MinPriceThreshold": 44000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M110_Black", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M110_Tan", "MaxPriceThreshold": 253000, "MinPriceThreshold": 250000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_M110_25Rnd", "MaxPriceThreshold": 9700, "MinPriceThreshold": 9200, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MRAD", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MRAD_10Rnd", "MaxPriceThreshold": 7000, "MinPriceThreshold": 7000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_R700", "MaxPriceThreshold": 350000, "MinPriceThreshold": 350000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_R700_7Rnd", "MaxPriceThreshold": 21890, "MinPriceThreshold": 21800, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scar_H", "MaxPriceThreshold": 314000, "MinPriceThreshold": 314000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scar_L", "MaxPriceThreshold": 314000, "MinPriceThreshold": 314000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Scar_H_25Rnd", "MaxPriceThreshold": 11800, "MinPriceThreshold": 11800, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Alligator", "MaxPriceThreshold": 490000, "MinPriceThreshold": 490000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Alligator_Black", "MaxPriceThreshold": 490000, "MinPriceThreshold": 490000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Alligator_5Rnd", "MaxPriceThreshold": 26000, "MinPriceThreshold": 26000, "SellPricePercent": 25, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_40mm_explosive", "MaxPriceThreshold": 25000, "MinPriceThreshold": 25000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_40mm_chemgas", "MaxPriceThreshold": 45000, "MinPriceThreshold": 45000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionammolaw", "MaxPriceThreshold": 35000, "MinPriceThreshold": 35000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionammorpg", "MaxPriceThreshold": 35000, "MinPriceThreshold": 35000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_expansion_m203_he", "MaxPriceThreshold": 65000, "MinPriceThreshold": 65000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_20mmBox_10Rnd", "MaxPriceThreshold": 65000, "MinPriceThreshold": 65000, "SellPricePercent": 2, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}