=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-12_9-12-3
====================================================================================
9:12:3 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
9:12:3 | [PermissionManager] Perm (FreeCamera) has been registered!
9:12:3 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
9:12:3 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
9:12:3 | [PermissionManager] Perm (MenuServerManager) has been registered!
9:12:3 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
9:12:3 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
9:12:3 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
9:12:3 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
9:12:3 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
9:12:3 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
9:12:3 | [PermissionManager] Perm (MenuObjectManager) has been registered!
9:12:3 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
9:12:3 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
9:12:3 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
9:12:3 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
9:12:3 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
9:12:3 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
9:12:3 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
9:12:3 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
9:12:3 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
9:12:3 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
9:12:3 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
9:12:3 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
9:12:3 | [PermissionManager] Perm (MenuBansManager) has been registered!
9:12:3 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
9:12:3 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
9:12:3 | [PermissionManager] Perm (MenuWebHooks) has been registered!
9:12:3 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
9:12:3 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
9:12:3 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
9:12:3 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
9:12:3 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
9:12:3 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
9:12:3 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
9:12:3 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
9:12:3 | [PermissionManager] Adding Super Admin (76561199239979485)
9:12:3 | [PermissionManager] Adding Super Admin (76561197999250250)
9:12:3 | [PermissionManager] Adding Super Admin (76561198153347717)
9:12:3 | [PermissionManager] Loaded UserGroups.json
9:12:3 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
9:12:3 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
9:12:3 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
9:12:3 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
9:12:3 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
9:12:3 | Building Sets Loaded: 0
9:12:3 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
9:15:53 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
9:16:26 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
9:18:26 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Alligator_Black)
9:19:19 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (SNAFUAlligator_5rdMag)
9:19:20 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (SNAFUAlligator_5rdMag)
9:20:36 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (SNAFUAlligator)
9:20:59 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (SNAFUAlligator)
9:21:57 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Remnants_Immortal_Scope)
9:24:45 | "BNeaveill99" (steamid=76561198153347717) healed player (steamid=76561198153347717)
9:25:32 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
9:25:35 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
9:25:43 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
9:25:55 | "BNeaveill99" (steamid=76561198153347717) healed player (steamid=76561198153347717)
9:27:23 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<6730.950195, 820.816406, 9765.540039>)
9:27:23 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=6730.95,0,9765.54)
9:27:32 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<7251.660156, 962.059937, 9499.580078>)
9:27:32 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=7251.66,0,9499.58)
9:29:21 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
9:29:22 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
14:50:48 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
15:57:21 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
15:58:1 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
15:58:38 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
15:58:40 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
15:58:49 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
15:59:3 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected early from server (EXIT NOW).
