<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<types>
	<type name="Paragon_Adoor_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Adoor_Gold">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Adoor_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Adoor_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Adoor_Rainbow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSafe_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSafe_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSafe_Rainbow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigTent_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigTent_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigTent_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Bdoor">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Compound_Gate">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Compound_Wall">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Container_Red">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunCase_Brown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunCase_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunCase_Cherry">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_DGunRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Dumpster">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Dumpster_Static">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Fridge_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Fridge_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GearStand">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GearStandC_B">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GearStandC_G">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GearStandC_C">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GraffitiCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GraffitiCan_Static">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunCase_Brown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunCase_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunCase_Cherry">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunWall_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunWall_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GunWall_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_HeliPad">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_HotDog_Cart">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_IceBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_IC_Freezer">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Purple">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Red">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Locker_Yellow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MediumCrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MediumCrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MediumCrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MediumCrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MediumCrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MetalRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MetalRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MetalRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mcabinet_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mcabinet_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mcabinet_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MiliCrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MiliCrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MiliCrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MiliCrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MiliCrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mlocker_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mlocker_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mlocker_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mlocker_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Mlocker_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Pallet_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Pallet_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Pallet_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_PlanterBox">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_GreenHouse">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_LargeGreenHouse">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Rdoor_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Rdoor_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Gold">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Safe_Rainbow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Guns">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Attachments">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Ammo">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Building">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Clothes">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Food">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Meds">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Sellables">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Armor">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Guns_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Attachments_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Ammo_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Building_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Clothes_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Food_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Meds_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Sellables_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSign_Armor_Clow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Guns">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Attachments">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Ammo">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Building">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Clothes">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Food">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Meds">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Sellables">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Armor">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Guns_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Attachments_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Ammo_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Building_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Clothes_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Food_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Meds_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Sellables_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_MedSign_Armor_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Guns">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Attachments">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Ammo">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Building">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Clothes">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Food">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Meds">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Sellables">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Armor">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Guns_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Attachments_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Ammo_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Building_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Clothes_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Food_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Meds_Clow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Sellables_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_BigSign_Armor_Glow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallCrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallCrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallCrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallCrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallCrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Gold">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_SmallSafe_Rainbow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Tcrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Tcrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Tcrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Tcrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Tcrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Scrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Scrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Scrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Scrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Scrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcrate_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_CunRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_CunRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DGunRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DCunRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DGunRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_CunCase_Brown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunCase_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunCase_Cherry">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DGunCase_Brown">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DGunCase_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_DGunCase_Cherry">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunWall_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunWall_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GunWall_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name=" StorageBox_IceBox   ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Purple ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Red ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_White ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Locker_Yellow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MetalRack_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MetalRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MetalRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name=" StorageBox_HotDog_Cart ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcabinet_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name=" StorageBox_Mcabinet_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mcabinet_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mlocker_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mlocker_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mlocker_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mlocker_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Mlocker_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Gold ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_White ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Safe_Rainbow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Gold ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_White ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_SmallSafe_Rainbow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigSafe_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigSafe_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigSafe_Rainbow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_ToolB_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_ToolB_Red ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_ToolB_White ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_ToolB_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_ToolB_Yellow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wood_Crate ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Pallet_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Pallet_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Pallet_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_HeliPad ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MiliCrate_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MiliCrate_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MiliCrate_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MiliCrate_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_MiliCrate_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Fridge_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Fridge_White ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Container_Red ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_IC_Freezer ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GearStand ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_PlanterBox ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_P_GreenHouse">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_LargeGreen House">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Adoor_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Adoor_Gold ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Adoor_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Adoor_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Adoor_Rainbow ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Rdoor_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Rdoor_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GearStandC_B ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GearStandC_G ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GearStandC_C ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wlocker_Black ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wlocker_Grey ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wlocker_Green ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wlocker_Tan ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Wlocker_Blue ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Bdoor ">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Compound_Gate">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Compound_Wall">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigTent_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigTent_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_BigTent_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Dumpster">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_GraffitiCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_TrashCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_WoodStorage">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_WallRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_WallRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_WallRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Weapons_Rack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Weapons_Rack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="StorageBox_Weapons_Rack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TínyCrate_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TinyCrate_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TinyCrate_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TinyCrate_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_ToolB_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_ToolB_Red">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_ToolB_White">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_ToolB_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_ToolB_Yellow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TrashCan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_TrashCan_Static">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WallRack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WallRack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WallRack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WaterBarrel_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WaterBarrel_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WaterBarrel_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WaterBarrel_Yellow">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_WaterBarrel_Red">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wlocker_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wlocker_Grey">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wlocker_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wlocker_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wlocker_Blue">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Weapons_Rack_Black">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Weapons_Rack_Green">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Weapons_Rack_Tan">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
	<type name="Paragon_Wood_Crate">
		<nominal>0</nominal>
		<lifetime>28800</lifetime>
		<restock>1800</restock>
		<min>0</min>
		<quantmin>-1</quantmin>
		<quantmax>-1</quantmax>
		<cost>100</cost>
		<flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		<category name="tools"/>
	</type>
</types>
