{"m_Version": 12, "DisplayName": "VEHICLES_RUSForma", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "AZLK_2141RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood", "AZLK_2141RF_doors_driver", "AZLK_2141RF_doors_codriver", "AZLK_2141RF_doors_cargo1", "AZLK_2141RF_doors_cargo2", "AZLK_2141RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AZLK_2141RF_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood_red", "AZLK_2141RF_doors_driver_red", "AZLK_2141RF_doors_codriver_red", "AZLK_2141RF_doors_cargo1_red", "AZLK_2141RF_doors_cargo2_red", "AZLK_2141RF_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AZLK_2141RF_beige", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood_beige", "AZLK_2141RF_doors_driver_beige", "AZLK_2141RF_doors_codriver_beige", "AZLK_2141RF_doors_cargo1_beige", "AZLK_2141RF_doors_cargo2_beige", "AZLK_2141RF_doors_trunk_beige", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AZLK_2141RF_Lettuce", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood_Lettuce", "AZLK_2141RF_doors_driver_Lettuce", "AZLK_2141RF_doors_codriver_Lettuce", "AZLK_2141RF_doors_cargo1_Lettuce", "AZLK_2141RF_doors_cargo2_Lettuce", "AZLK_2141RF_doors_trunk_Lettuce", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "azlk_400", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "CarBattery", "CarRadiator", "SparkPlug", "azlk_400_doors_hood", "azlk_400_doors_driver", "azlk_400_doors_codriver", "azlk_400_doors_cargo1", "AZLK_2141RF_doors_cargo2", "AZLK_2141RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "azlk_400_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "CarBattery", "CarRadiator", "SparkPlug", "azlk_400_doors_hood_green", "azlk_400_doors_driver_green", "azlk_400_doors_codriver_green", "azlk_400_doors_cargo1_green", "AZLK_2141RF_doors_cargo2_green", "AZLK_2141RF_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "azlk_400_beige", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "azlk_400_wheel", "CarBattery", "CarRadiator", "SparkPlug", "azlk_400_doors_hood_beige", "azlk_400_doors_driver_beige", "azlk_400_doors_codriver_beige", "azlk_400_doors_cargo1_beige", "AZLK_2141RF_doors_cargo2_beige", "AZLK_2141RF_doors_trunk_beige", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "F1_Pickup", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "CarBattery", "CarRadiator", "SparkPlug", "F1_Pickup_doors_hood", "F1_Pickup_doors_driver", "F1_Pickup_doors_codriver", "F1_Pickup_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "F1_Pickup_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "CarBattery", "CarRadiator", "SparkPlug", "F1_Pickup_doors_hood_green", "F1_Pickup_doors_driver_green", "F1_Pickup_doors_codriver_green", "F1_Pickup_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "F1_Pickup_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "F1_Pickup_wheel", "CarBattery", "CarRadiator", "SparkPlug", "F1_Pickup_doors_hood_black", "F1_Pickup_doors_driver_black", "F1_Pickup_doors_codriver_black", "F1_Pickup_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ21_Volga", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ21_Volga_doors_hood", "GAZ21_Volga_doors_driver", "GAZ21_Volga_doors_codriver", "GAZ21_Volga_doors_cargo1", "GAZ21_Volga_doors_cargo2", "GAZ21_Volga_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ21_Volga_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ21_Volga_doors_hood_black", "GAZ21_Volga_doors_driver_black", "GAZ21_Volga_doors_codriver_black", "GAZ21_Volga_doors_cargo1_black", "GAZ21_Volga_doors_cargo2_black", "GAZ21_Volga_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ21_Volga_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "GAZ21_Volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ21_Volga_doors_hood_green", "GAZ21_Volga_doors_driver_green", "GAZ21_Volga_doors_codriver_green", "GAZ21_Volga_doors_cargo1_green", "GAZ21_Volga_doors_cargo2_green", "GAZ21_Volga_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_12", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_12_doors_hood", "GAZ_12_doors_driver", "GAZ_12_doors_codriver", "GAZ_12_doors_cargo1", "GAZ_12_doors_cargo2", "GAZ_12_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_12_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_12_doors_hood_white", "GAZ_12_doors_driver_white", "GAZ_12_doors_codriver_white", "GAZ_12_doors_cargo1_white", "GAZ_12_doors_cargo2_white", "GAZ_12_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_12_kombi", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "GAZ_12_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_12_doors_hood_kombi", "GAZ_12_doors_driver_kombi", "GAZ_12_doors_codriver_kombi", "GAZ_12_doors_cargo1_kombi", "GAZ_12_doors_cargo2_kombi", "GAZ_12_doors_trunk_kombi", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_13_Cha<PERSON>", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_13_<PERSON><PERSON>_doors_hood", "GAZ_13_<PERSON><PERSON>_doors_driver", "GAZ_13_<PERSON><PERSON>_doors_codriver", "GAZ_13_<PERSON><PERSON>_doors_cargo1", "GAZ_13_<PERSON><PERSON>_doors_cargo2", "GAZ_13_<PERSON><PERSON>_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_13_<PERSON><PERSON>_black_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_13_<PERSON><PERSON>_doors_hood_black_rust", "GAZ_13_<PERSON><PERSON>_doors_driver_black_rust", "GAZ_13_<PERSON><PERSON>_doors_codriver_black_rust", "GAZ_13_<PERSON><PERSON>_doors_cargo1_black_rust", "GAZ_13_<PERSON><PERSON>_doors_cargo2_black_rust", "GAZ_13_<PERSON><PERSON>_doors_trunk_black_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_13_<PERSON><PERSON>_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_13_<PERSON><PERSON>_doors_hood_white", "GAZ_13_<PERSON><PERSON>_doors_driver_white", "GAZ_13_<PERSON><PERSON>_doors_codriver_white", "GAZ_13_<PERSON><PERSON>_doors_cargo1_white", "GAZ_13_<PERSON><PERSON>_doors_cargo2_white", "GAZ_13_<PERSON><PERSON>_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_13_<PERSON><PERSON>_white_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "GAZ_13_<PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_13_<PERSON><PERSON>_doors_hood_white_rust", "GAZ_13_<PERSON><PERSON>_doors_driver_white_rust", "GAZ_13_<PERSON><PERSON>_doors_codriver_white_rust", "GAZ_13_<PERSON><PERSON>_doors_cargo1_white_rust", "GAZ_13_<PERSON><PERSON>_doors_cargo2_white_rust", "GAZ_13_<PERSON><PERSON>_doors_trunk_white_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_3110_volga", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_3110_volga_doors_hood", "GAZ_3110_volga_doors_driver", "GAZ_3110_volga_doors_codriver", "GAZ_3110_volga_doors_cargo1", "GAZ_3110_volga_doors_cargo2", "GAZ_3110_volga_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_3110_volga_taxi", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_3110_volga_doors_hood_taxi", "GAZ_3110_volga_doors_driver_taxi", "GAZ_3110_volga_doors_codriver_taxi", "GAZ_3110_volga_doors_cargo1_taxi", "GAZ_3110_volga_doors_cargo2_taxi", "GAZ_3110_volga_doors_trunk_taxi", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_3110_volga_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_3110_volga_doors_hood_white", "GAZ_3110_volga_doors_driver_white", "GAZ_3110_volga_doors_codriver_white", "GAZ_3110_volga_doors_cargo1_white", "GAZ_3110_volga_doors_cargo2_white", "GAZ_3110_volga_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_3110_volga_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "GAZ_3110_volga_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_3110_volga_doors_hood_black", "GAZ_3110_volga_doors_driver_black", "GAZ_3110_volga_doors_codriver_black", "GAZ_3110_volga_doors_cargo1_black", "GAZ_3110_volga_doors_cargo2_black", "GAZ_3110_volga_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_33081", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_33081_doors_hood", "GAZ_33081_doors_driver", "GAZ_33081_doors_codriver", "GAZ_33081_doors_cargo1", "GAZ_33081_doors_cargo2", "GAZ_33081_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_33081_camo", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "GAZ_33081_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_33081_doors_hood_camo", "GAZ_33081_doors_driver_camo", "GAZ_33081_doors_codriver_camo", "GAZ_33081_doors_cargo1_camo", "GAZ_33081_doors_cargo2_camo", "GAZ_33081_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Gaz_53RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Gaz_53RF_wheel", "Gaz_53RF_wheel", "Gaz_53RF_wheel", "Gaz_53RF_WheelDouble", "Gaz_53RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "Gaz_53RF_doors_hood", "Gaz_53RF_doors_driver", "Gaz_53RF_doors_codriver", "Gaz_53RF_doors_trunk", "gaz53rf_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Gaz_53RF_petrol_truck", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Gaz_53RF_wheel", "Gaz_53RF_wheel", "Gaz_53RF_wheel", "Gaz_53RF_WheelDouble", "Gaz_53RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "Gaz_53RF_doors_hood", "Gaz_53RF_doors_driver", "Gaz_53RF_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood", "GAZ_M20_doors_driver", "GAZ_M20_doors_codriver", "GAZ_M20_doors_cargo1", "GAZ_M20_doors_cargo2", "GAZ_M20_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood_white", "GAZ_M20_doors_driver_white", "GAZ_M20_doors_codriver_white", "GAZ_M20_doors_cargo1_white", "GAZ_M20_doors_cargo2_white", "GAZ_M20_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood_green", "GAZ_M20_doors_driver_green", "GAZ_M20_doors_codriver_green", "GAZ_M20_doors_cargo1_green", "GAZ_M20_doors_cargo2_green", "GAZ_M20_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood_black", "GAZ_M20_doors_driver_black", "GAZ_M20_doors_codriver_black", "GAZ_M20_doors_cargo1_black", "GAZ_M20_doors_cargo2_black", "GAZ_M20_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood_red", "GAZ_M20_doors_driver_red", "GAZ_M20_doors_codriver_red", "GAZ_M20_doors_cargo1_red", "GAZ_M20_doors_cargo2_red", "GAZ_M20_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_M20_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "GAZ_M20_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_M20_doors_hood_blue", "GAZ_M20_doors_driver_blue", "GAZ_M20_doors_codriver_blue", "GAZ_M20_doors_cargo1_blue", "GAZ_M20_doors_cargo2_blue", "GAZ_M20_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2125_<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2125_<PERSON><PERSON><PERSON>_doors_hood", "I<PERSON>_2125_<PERSON><PERSON><PERSON>_doors_driver", "IJ_2125_<PERSON><PERSON><PERSON>_doors_codriver", "IJ_2125_Kombi_doors_cargo1", "IJ_2125_Kombi_doors_cargo2", "IJ_2125_<PERSON><PERSON><PERSON>_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2125_<PERSON><PERSON><PERSON>_beg", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2125_<PERSON><PERSON><PERSON>_doors_hood_beg", "IJ_2125_<PERSON><PERSON><PERSON>_doors_driver_beg", "IJ_2125_<PERSON><PERSON><PERSON>_doors_codriver_beg", "IJ_2125_<PERSON><PERSON>i_doors_cargo1_beg", "IJ_2125_<PERSON><PERSON>i_doors_cargo2_beg", "IJ_2125_<PERSON><PERSON><PERSON>_doors_trunk_beg", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2125_<PERSON><PERSON><PERSON>_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2125_<PERSON><PERSON><PERSON>_doors_hood_green", "IJ_2125_<PERSON><PERSON><PERSON>_doors_driver_green", "IJ_2125_<PERSON><PERSON><PERSON>_doors_codriver_green", "IJ_2125_Kombi_doors_cargo1_green", "IJ_2125_Kombi_doors_cargo2_green", "IJ_2125_<PERSON><PERSON><PERSON>_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2125_<PERSON><PERSON><PERSON>_orange", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2125_<PERSON><PERSON><PERSON>_doors_hood_orange", "IJ_2125_<PERSON><PERSON><PERSON>_doors_driver_orange", "IJ_2125_<PERSON><PERSON><PERSON>_doors_codriver_orange", "IJ_2125_Kombi_doors_cargo1_orange", "IJ_2125_Kombi_doors_cargo2_orange", "IJ_2125_<PERSON><PERSON><PERSON>_doors_trunk_orange", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2125_<PERSON><PERSON><PERSON>_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "IJ_2125_<PERSON><PERSON><PERSON>_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2125_<PERSON><PERSON><PERSON>_doors_hood_red", "IJ_2125_<PERSON><PERSON><PERSON>_doors_driver_red", "IJ_2125_<PERSON><PERSON><PERSON>_doors_codriver_red", "IJ_2125_Kombi_doors_cargo1_red", "IJ_2125_Kombi_doors_cargo2_red", "IJ_2125_<PERSON><PERSON>i_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ij_2715_RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ij_2715_RF_doors_hood", "ij_2715_RF_doors_driver", "ij_2715_RF_doors_codriver", "ij_2715_RF_doors_cargo1", "ij_2715_RF_doors_cargo2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ij_2715_RF_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ij_2715_RF_doors_hood_blue", "ij_2715_RF_doors_driver_blue", "ij_2715_RF_doors_codriver_blue", "ij_2715_RF_doors_cargo1_blue", "ij_2715_RF_doors_cargo2_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ij_2715_<PERSON>_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ij_2715_RF_doors_hood_green", "ij_2715_RF_doors_driver_green", "ij_2715_RF_doors_codriver_green", "ij_2715_RF_doors_cargo1_green", "ij_2715_RF_doors_cargo2_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ij_2715_RF_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "ij_2715_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ij_2715_RF_doors_hood_red", "ij_2715_RF_doors_driver_red", "ij_2715_RF_doors_codriver_red", "ij_2715_RF_doors_cargo1_red", "ij_2715_RF_doors_cargo2_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KAVZ685_RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_WheelDouble", "KAVZ685_RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KAVZ685_RF_doors_hood", "KAVZ685_RF_doors_driver", "KAVZ685_RF_doors_cargo1", "KAVZ685_RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KAVZ685_RF_beige", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_WheelDouble", "KAVZ685_RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KAVZ685_RF_doors_hood_beige", "KAVZ685_RF_doors_driver_beige", "KAVZ685_RF_doors_cargo1_beige", "KAVZ685_RF_doors_trunk_beige", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KAVZ685_RF_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_WheelDouble", "KAVZ685_RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KAVZ685_RF_doors_hood_red", "KAVZ685_RF_doors_driver_red", "KAVZ685_RF_doors_cargo1_red", "KAVZ685_RF_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KAVZ685_RF_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_wheel", "KAVZ685_RF_WheelDouble", "KAVZ685_RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KAVZ685_RF_doors_hood_green", "KAVZ685_RF_doors_driver_green", "KAVZ685_RF_doors_cargo1_green", "KAVZ685_RF_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KubanG1", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KubanG1_wheel", "KubanG1_wheel", "KubanG1_wheel", "KubanG1_WheelDouble", "KubanG1_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KubanG1_doors_hood", "<PERSON>banG1_doors_driver", "KubanG1_doors_cargo2", "KubanG1_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "KubanG1_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["KubanG1_wheel", "KubanG1_wheel", "KubanG1_wheel", "KubanG1_WheelDouble", "KubanG1_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "KubanG1_doors_hood_green", "KubanG1_doors_driver_green", "KubanG1_doors_cargo2_green", "KubanG1_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Niva2329", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "CarBattery", "CarRadiator", "SparkPlug", "Niva2329_doors_hood", "Niva2329_doors_driver", "Niva2329_doors_codriver", "Niva2329_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Niva2329_camo", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "Niva2329_wheel", "CarBattery", "CarRadiator", "SparkPlug", "Niva2329_doors_hood_camo", "Niva2329_doors_driver_camo", "Niva2329_doors_codriver_camo", "Niva2329_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "moskvich_407", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "CarBattery", "CarRadiator", "SparkPlug", "moskvich_407_doors_hood", "mos<PERSON>vich_407_doors_driver", "moskvich_407_doors_codriver", "moskvich_407_doors_cargo1", "moskvich_407_doors_cargo2", "moskvich_407_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "moskvich_407_double_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "CarBattery", "CarRadiator", "SparkPlug", "moskvich_407_doors_hood_double_red", "moskvich_407_doors_driver_double_red", "moskvich_407_doors_codriver_double_red", "moskvich_407_doors_cargo1_double_red", "moskvich_407_doors_cargo2_double_red", "moskvich_407_doors_trunk_double_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "moskvich_407_double_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "CarBattery", "CarRadiator", "SparkPlug", "moskvich_407_doors_hood_double_blue", "moskvich_407_doors_driver_double_blue", "moskvich_407_doors_codriver_double_blue", "moskvich_407_doors_cargo1_double_blue", "moskvich_407_doors_cargo2_double_blue", "moskvich_407_doors_trunk_double_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "moskvich_407_grey", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "moskvich_407_wheel", "CarBattery", "CarRadiator", "SparkPlug", "moskvich_407_doors_hood_grey", "moskvich_407_doors_driver_grey", "moskvich_407_doors_codriver_grey", "moskvich_407_doors_cargo1_grey", "moskvich_407_doors_cargo2_grey", "moskvich_407_doors_trunk_grey", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "PAZ_672", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["PAZ_672_wheel", "PAZ_672_wheel", "PAZ_672_wheel", "PAZ_672_WheelDouble", "PAZ_672_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "PAZ_672_doors_hood", "PAZ_672_doors_driver", "PAZ_672_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "PAZ_672_orange", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["PAZ_672_wheel", "PAZ_672_wheel", "PAZ_672_wheel", "PAZ_672_WheelDouble", "PAZ_672_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "PAZ_672_doors_hood_orange", "PAZ_672_doors_driver_orange", "PAZ_672_doors_codriver_orange", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood", "UAZ_3163_doors_driver", "UAZ_3163_doors_codriver", "UAZ_3163_doors_cargo1", "UAZ_3163_doors_cargo2", "UAZ_3163_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_black", "UAZ_3163_doors_driver_black", "UAZ_3163_doors_codriver_black", "UAZ_3163_doors_cargo1_black", "UAZ_3163_doors_cargo2_black", "UAZ_3163_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_Red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_Red", "UAZ_3163_doors_driver_Red", "UAZ_3163_doors_codriver_Red", "UAZ_3163_doors_cargo1_Red", "UAZ_3163_doors_cargo2_Red", "UAZ_3163_doors_trunk_Red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_blue", "UAZ_3163_doors_driver_blue", "UAZ_3163_doors_codriver_blue", "UAZ_3163_doors_cargo1_blue", "UAZ_3163_doors_cargo2_blue", "UAZ_3163_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_green", "UAZ_3163_doors_driver_green", "UAZ_3163_doors_codriver_green", "UAZ_3163_doors_cargo1_green", "UAZ_3163_doors_cargo2_green", "UAZ_3163_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_white", "UAZ_3163_doors_driver_white", "UAZ_3163_doors_codriver_white", "UAZ_3163_doors_cargo1_white", "UAZ_3163_doors_cargo2_white", "UAZ_3163_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_33094", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_33094_tent", "UAZ_33094_doors_driver", "UAZ_33094_doors_codriver", "UAZ_33094_doors_cargo2", "UAZ_33094_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_33094_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_33094_tent", "UAZ_33094_doors_driver_green", "UAZ_33094_doors_codriver_green", "UAZ_33094_doors_cargo2_green", "UAZ_33094_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_33094_gray", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "UAZ_33094_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_33094_tent", "UAZ_33094_doors_driver_gray", "UAZ_33094_doors_codriver_gray", "UAZ_33094_doors_cargo2_gray", "UAZ_33094_doors_trunk_gray", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3962", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3962_doors_driver", "UAZ_3962_doors_codriver", "UAZ_3962_doors_cargo1", "UAZ_3962_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3962_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3962_doors_driver_green", "UAZ_3962_doors_codriver_green", "UAZ_3962_doors_cargo1_green", "UAZ_3962_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_hood", "UA<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_codriver", "UAZ_Patrot_Pikap_doors_cargo1", "UAZ_Patrot_Pikap_doors_cargo2", "UAZ_<PERSON>rot_Pikap_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_hood_red", "UAZ_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver_red", "UAZ_<PERSON><PERSON>_Pikap_doors_codriver_red", "UAZ_Patrot_Pikap_doors_cargo1_red", "UAZ_Patrot_Pikap_doors_cargo2_red", "UAZ_Patrot_Pikap_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_hood_blue", "UAZ_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver_blue", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_codriver_blue", "UAZ_Patrot_Pikap_doors_cargo1_blue", "UAZ_Patrot_Pikap_doors_cargo2_blue", "UAZ_Patrot_Pikap_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_hood_green", "UAZ_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver_green", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_codriver_green", "UAZ_Patrot_Pikap_doors_cargo1_green", "UAZ_Patrot_Pikap_doors_cargo2_green", "UAZ_Patrot_Pikap_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>ka<PERSON>_doors_hood_white", "UAZ_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver_white", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_codriver_white", "UAZ_Patrot_Pikap_doors_cargo1_white", "UAZ_Patrot_Pikap_doors_cargo2_white", "UAZ_<PERSON>rot_Pikap_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_Patrot_Pikap_camo_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "UAZ_Patrot_Pikap_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_hood_camo_green", "UAZ_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_doors_driver_camo_green", "UAZ_<PERSON><PERSON>_<PERSON>kap_doors_codriver_camo_green", "UAZ_Patrot_Pikap_doors_cargo1_camo_green", "UAZ_Patrot_Pikap_doors_cargo2_camo_green", "UAZ_Patrot_Pikap_doors_trunk_camo_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2104RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2104RF_doors_hood", "VAZ_2104RF_doors_driver", "VAZ_2104RF_doors_codriver", "VAZ_2104RF_doors_cargo1", "VAZ_2104RF_doors_cargo2", "VAZ_2104RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2104RF_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2104RF_doors_hood_blue", "VAZ_2104RF_doors_driver_blue", "VAZ_2104RF_doors_codriver_blue", "VAZ_2104RF_doors_cargo1_blue", "VAZ_2104RF_doors_cargo2_blue", "VAZ_2104RF_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2104RF_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2104RF_doors_hood_black", "VAZ_2104RF_doors_driver_black", "VAZ_2104RF_doors_codriver_black", "VAZ_2104RF_doors_cargo1_black", "VAZ_2104RF_doors_cargo2_black", "VAZ_2104RF_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2104RF_beige", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "VAZ_2104RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2104RF_doors_hood_beige", "VAZ_2104RF_doors_driver_beige", "VAZ_2104RF_doors_codriver_beige", "VAZ_2104RF_doors_cargo1_beige", "VAZ_2104RF_doors_cargo2_beige", "VAZ_2104RF_doors_trunk_beige", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood", "VAZ_2105_RUS_doors_driver", "VAZ_2105_RUS_doors_codriver", "VAZ_2105_RUS_doors_cargo1", "VAZ_2105_RUS_doors_cargo2", "VAZ_2105_RUS_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_red", "VAZ_2105_RUS_doors_driver_red", "VAZ_2105_RUS_doors_codriver_red", "VAZ_2105_RUS_doors_cargo1_red", "VAZ_2105_RUS_doors_cargo2_red", "VAZ_2105_RUS_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_cherry", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_cherry", "VAZ_2105_RUS_doors_driver_cherry", "VAZ_2105_RUS_doors_codriver_cherry", "VAZ_2105_RUS_doors_cargo1_cherry", "VAZ_2105_RUS_doors_cargo2_cherry", "VAZ_2105_RUS_doors_trunk_cherry", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_black", "VAZ_2105_RUS_doors_driver_black", "VAZ_2105_RUS_doors_codriver_black", "VAZ_2105_RUS_doors_cargo1_black", "VAZ_2105_RUS_doors_cargo2_black", "VAZ_2105_RUS_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_yellow", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_yellow", "VAZ_2105_RUS_doors_driver_yellow", "VAZ_2105_RUS_doors_codriver_yellow", "VAZ_2105_RUS_doors_cargo1_yellow", "VAZ_2105_RUS_doors_cargo2_yellow", "VAZ_2105_RUS_doors_trunk_yellow", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_green", "VAZ_2105_RUS_doors_driver_green", "VAZ_2105_RUS_doors_codriver_green", "VAZ_2105_RUS_doors_cargo1_green", "VAZ_2105_RUS_doors_cargo2_green", "VAZ_2105_RUS_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2105_RUS_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "VAZ_2105_RUS_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2105_RUS_doors_hood_white", "VAZ_2105_RUS_doors_driver_white", "VAZ_2105_RUS_doors_codriver_white", "VAZ_2105_RUS_doors_cargo1_white", "VAZ_2105_RUS_doors_cargo2_white", "VAZ_2105_RUS_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood", "vaz_2106_RF_doors_driver", "vaz_2106_RF_doors_codriver", "vaz_2106_RF_doors_cargo1", "vaz_2106_RF_doors_cargo2", "vaz_2106_RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF_light_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_light_green", "vaz_2106_RF_doors_driver_light_green", "vaz_2106_RF_doors_codriver_light_green", "vaz_2106_RF_doors_cargo1_light_green", "vaz_2106_RF_doors_cargo2_light_green", "vaz_2106_RF_doors_trunk_light_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_red", "vaz_2106_RF_doors_driver_red", "vaz_2106_RF_doors_codriver_red", "vaz_2106_RF_doors_cargo1_red", "vaz_2106_RF_doors_cargo2_red", "vaz_2106_RF_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_white", "vaz_2106_RF_doors_driver_white", "vaz_2106_RF_doors_codriver_white", "vaz_2106_RF_doors_cargo1_white", "vaz_2106_RF_doors_cargo2_white", "vaz_2106_RF_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF_wine", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_wine", "vaz_2106_RF_doors_driver_wine", "vaz_2106_RF_doors_codriver_wine", "vaz_2106_RF_doors_cargo1_wine", "vaz_2106_RF_doors_cargo2_wine", "vaz_2106_RF_doors_trunk_wine", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_RF_yellow", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_yellow", "vaz_2106_RF_doors_driver_yellow", "vaz_2106_RF_doors_codriver_yellow", "vaz_2106_RF_doors_cargo1_yellow", "vaz_2106_RF_doors_cargo2_yellow", "vaz_2106_RF_doors_trunk_yellow", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood", "VAZ_2107RF_doors_driver", "VAZ_2107RF_doors_codriver", "VAZ_2107RF_doors_cargo1", "VAZ_2107RF_doors_cargo2", "VAZ_2107RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood_blue", "VAZ_2107RF_doors_driver_blue", "VAZ_2107RF_doors_codriver_blue", "VAZ_2107RF_doors_cargo1_blue", "VAZ_2107RF_doors_cargo2_blue", "VAZ_2107RF_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF_blue_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood_blue_rust", "VAZ_2107RF_doors_driver_blue_rust", "VAZ_2107RF_doors_codriver_blue_rust", "VAZ_2107RF_doors_cargo1_blue_rust", "VAZ_2107RF_doors_cargo2_blue_rust", "VAZ_2107RF_doors_trunk_blue_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood_black", "VAZ_2107RF_doors_driver_black", "VAZ_2107RF_doors_codriver_black", "VAZ_2107RF_doors_cargo1_black", "VAZ_2107RF_doors_cargo2_black", "VAZ_2107RF_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF_black_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood_black_rust", "VAZ_2107RF_doors_driver_black_rust", "VAZ_2107RF_doors_codriver_black_rust", "VAZ_2107RF_doors_cargo1_black_rust", "VAZ_2107RF_doors_cargo2_black_rust", "VAZ_2107RF_doors_trunk_black_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2107RF_white_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "VAZ_2107RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2107RF_doors_hood_white_rust", "VAZ_2107RF_doors_driver_white_rust", "VAZ_2107RF_doors_codriver_white_rust", "VAZ_2107RF_doors_cargo1_white_rust", "VAZ_2107RF_doors_cargo2_white_rust", "VAZ_2107RF_doors_trunk_white_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_21099R", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_21099R_doors_hood", "VAZ_21099R_doors_driver", "VAZ_21099R_doors_codriver", "VAZ_21099R_doors_cargo1", "VAZ_21099R_doors_cargo2", "VAZ_21099R_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_21099R_black", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_21099R_doors_hood_black", "VAZ_21099R_doors_driver_black", "VAZ_21099R_doors_codriver_black", "VAZ_21099R_doors_cargo1_black", "VAZ_21099R_doors_cargo2_black", "VAZ_21099R_doors_trunk_black", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_21099R_white", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_21099R_doors_hood_white", "VAZ_21099R_doors_driver_white", "VAZ_21099R_doors_codriver_white", "VAZ_21099R_doors_cargo1_white", "VAZ_21099R_doors_cargo2_white", "VAZ_21099R_doors_trunk_white", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_21099R_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "VAZ_21099R_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_21099R_doors_hood_blue", "VAZ_21099R_doors_driver_blue", "VAZ_21099R_doors_codriver_blue", "VAZ_21099R_doors_cargo1_blue", "VAZ_21099R_doors_cargo2_blue", "VAZ_21099R_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood", "VAZ_2109_RF_doors_driver", "VAZ_2109_RF_doors_codriver", "VAZ_2109_RF_doors_cargo1", "VAZ_2109_RF_doors_cargo2", "VAZ_2109_RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_rust", "VAZ_2109_RF_doors_driver_rust", "VAZ_2109_RF_doors_codriver_rust", "VAZ_2109_RF_doors_cargo1_rust", "VAZ_2109_RF_doors_cargo2_rust", "VAZ_2109_RF_doors_trunk_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_green", "VAZ_2109_RF_doors_driver_green", "VAZ_2109_RF_doors_codriver_green", "VAZ_2109_RF_doors_cargo1_green", "VAZ_2109_RF_doors_cargo2_green", "VAZ_2109_RF_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_green_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_green_rust", "VAZ_2109_RF_doors_driver_green_rust", "VAZ_2109_RF_doors_codriver_green_rust", "VAZ_2109_RF_doors_cargo1_green_rust", "VAZ_2109_RF_doors_cargo2_green_rust", "VAZ_2109_RF_doors_trunk_green_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_white_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_white_rust", "VAZ_2109_RF_doors_driver_white_rust", "VAZ_2109_RF_doors_codriver_white_rust", "VAZ_2109_RF_doors_cargo1_white_rust", "VAZ_2109_RF_doors_cargo2_white_rust", "VAZ_2109_RF_doors_trunk_white_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_blue", "VAZ_2109_RF_doors_driver_blue", "VAZ_2109_RF_doors_codriver_blue", "VAZ_2109_RF_doors_cargo1_blue", "VAZ_2109_RF_doors_cargo2_blue", "VAZ_2109_RF_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VAZ_2109_RF_blue_rust", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "VAZ_2109_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VAZ_2109_RF_doors_hood_blue_rust", "VAZ_2109_RF_doors_driver_blue_rust", "VAZ_2109_RF_doors_codriver_blue_rust", "VAZ_2109_RF_doors_cargo1_blue_rust", "VAZ_2109_RF_doors_cargo2_blue_rust", "VAZ_2109_RF_doors_trunk_blue_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_965", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_965_doors_hood", "ZAZ_965_doors_driver", "ZAZ_965_doors_codriver", "ZAZ_965_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_965_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_965_doors_hood_red", "ZAZ_965_doors_driver_red", "ZAZ_965_doors_codriver_red", "ZAZ_965_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_965_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_965_doors_hood_green", "ZAZ_965_doors_driver_green", "ZAZ_965_doors_codriver_green", "ZAZ_965_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_965_bk", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "ZAZ_965_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_965_doors_hood_bk", "ZAZ_965_doors_driver_bk", "ZAZ_965_doors_codriver_bk", "ZAZ_965_doors_trunk_bk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968_doors_hood", "ZAZ_968_doors_driver", "ZAZ_968_doors_codriver", "ZAZ_968_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968_doors_hood_green", "ZAZ_968_doors_driver_green", "ZAZ_968_doors_codriver_green", "ZAZ_968_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968_yellow", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968_doors_hood_yellow", "ZAZ_968_doors_driver_yellow", "ZAZ_968_doors_codriver_yellow", "ZAZ_968_doors_trunk_yellow", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968M", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968M_doors_hood", "ZAZ_968M_doors_driver", "ZAZ_968M_doors_codriver", "ZAZ_968M_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968M_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "ZAZ_968M_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968M_doors_hood_red", "ZAZ_968M_doors_driver_red", "ZAZ_968M_doors_codriver_red", "ZAZ_968M_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL133_double", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL133_double_wheel", "ZIL133_double_wheel", "ZIL133_double_wheel", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL133_double_doors_hood", "ZIL133_double_doors_driver", "ZIL133_double_doors_codriver", "ZIL133_double_doors_cargo1", "ZIL133_double_doors_cargo2", "ZIL133_double_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL133_double_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL133_double_wheel", "ZIL133_double_wheel", "ZIL133_double_wheel", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "ZIL133_double_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL133_double_doors_hood_green", "ZIL133_double_doors_driver_green", "ZIL133_double_doors_codriver_green", "ZIL133_double_doors_cargo1_green", "ZIL133_double_doors_cargo2_green", "ZIL133_double_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_WheelDouble", "ZIL_130_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_doors_hood", "ZIL_130_doors_driver", "ZIL_130_doors_codriver", "ZIL_130_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_WheelDouble", "ZIL_130_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_doors_hood_green", "ZIL_130_doors_driver_green", "ZIL_130_doors_codriver_green", "ZIL_130_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130_CDF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_WheelDouble", "ZIL_130_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_doors_hood_CDF", "ZIL_130_doors_driver_CDF", "ZIL_130_doors_codriver_CDF", "ZIL_130_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130_kung", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_WheelDouble", "ZIL_130_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_doors_hood", "ZIL_130_doors_driver", "ZIL_130_doors_codriver", "ZIL_130_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130_kung_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_wheel", "ZIL_130_WheelDouble", "ZIL_130_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_doors_hood_green", "ZIL_130_doors_driver_green", "ZIL_130_doors_codriver_green", "ZIL_130_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2140_SL", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2140_SL_doors_hood", "IJ_2140_SL_doors_driver", "IJ_2140_SL_doors_codriver", "IJ_2140_SL_doors_cargo1", "IJ_2140_SL_doors_cargo2", "IJ_2140_SL_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2140_SL_red", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2140_SL_doors_hood_red", "IJ_2140_SL_doors_driver_red", "IJ_2140_SL_doors_codriver_red", "IJ_2140_SL_doors_cargo1_red", "IJ_2140_SL_doors_cargo2_red", "IJ_2140_SL_doors_trunk_red", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2140_SL_blue", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2140_SL_doors_hood_blue", "IJ_2140_SL_doors_driver_blue", "IJ_2140_SL_doors_codriver_blue", "IJ_2140_SL_doors_cargo1_blue", "IJ_2140_SL_doors_cargo2_blue", "IJ_2140_SL_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2140_SL_green", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2140_SL_doors_hood_green", "IJ_2140_SL_doors_driver_green", "IJ_2140_SL_doors_codriver_green", "IJ_2140_SL_doors_cargo1_green", "IJ_2140_SL_doors_cargo2_green", "IJ_2140_SL_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LIAZ_677RF", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LIAZ_677RF_wheel", "LIAZ_677RF_wheel", "LIAZ_677RF_wheel", "LIAZ_677RF_WheelDouble", "LIAZ_677RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "LIAZ_677RF_doors_hood", "LIAZ_677RF_doors_driver", "LIAZ_677RF_doors_cargo1", "LIAZ_677RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LIAZ_677RF_camo", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LIAZ_677RF_wheel", "LIAZ_677RF_wheel", "LIAZ_677RF_wheel", "LIAZ_677RF_WheelDouble", "LIAZ_677RF_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "LIAZ_677RF_doors_hood_camo", "LIAZ_677RF_doors_driver_camo", "LIAZ_677RF_doors_cargo1_camo", "LIAZ_677RF_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}]}