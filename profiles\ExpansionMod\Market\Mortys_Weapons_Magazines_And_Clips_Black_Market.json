{"m_Version": 12, "DisplayName": "Mortys Weapons Magazines And Clips Black Market", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "TTC_AWM_Magazine_5rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_BAR_Magazine_20rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_50Beo_mag", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_20rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_20rnd_camo", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_20rnd_Digitan", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_20rnd_Tan", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_20rnd_UPC", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_40rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_10rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_10rnd_camo", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_10rnd_Digitan", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_10rnd_Tan", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_762Stanag_10rnd_UPC", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Coupled_FAL_Magazine", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_Magazine", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_338mag_10rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_338mag_5rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_GEVAR43_Magazine_10rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M14_Mag_20rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24New_mag_5rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24mag_5rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M82_5rnd_Mag", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_G3_Magazine_20rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK417_Magazine_20rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_STG44_Magazine_30rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARHMag", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mag_SVT40_10rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_XM2010_10rnd", "MaxPriceThreshold": 2750, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_300Cmag", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_300Cmag_60rnd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_300Stanag", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_308Stanag", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_44Mag_Cylinder", "MaxPriceThreshold": 2000, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_44Mag_Ejector", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_44Magnum", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_556Cmag_30rnd", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_556Stanag_30rnd", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_556Stanag_60rnd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AG3_Coupled_Magazine_40rnd", "MaxPriceThreshold": 4500, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AK12_Drum_90rnd", "MaxPriceThreshold": 5000, "MinPriceThreshold": 4500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AVS36_Mag_10rnd", "MaxPriceThreshold": 2000, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AmmoBox_44Mag", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Ammo_44Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_100rnd", "MaxPriceThreshold": 6000, "MinPriceThreshold": 5500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_100rnd_Digitan", "MaxPriceThreshold": 6000, "MinPriceThreshold": 5500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_100rnd_Tan", "MaxPriceThreshold": 6000, "MinPriceThreshold": 5500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_100rnd_UPC", "MaxPriceThreshold": 6000, "MinPriceThreshold": 5500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_100rnd_camo", "MaxPriceThreshold": 6000, "MinPriceThreshold": 5500, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_30rnd", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_30rnd_Digitan", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_30rnd_Tan", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_30rnd_UPC", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_30rnd_camo", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_40rnd", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_40rnd_Digitan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_40rnd_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_40rnd_UPC", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Pmag_40rnd_camo", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_100rnd", "MaxPriceThreshold": 5500, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_100rnd_Digitan", "MaxPriceThreshold": 5500, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_100rnd_Tan", "MaxPriceThreshold": 5500, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_100rnd_UPC", "MaxPriceThreshold": 5500, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_100rnd_camo", "MaxPriceThreshold": 5500, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_30rnd", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_30rnd_Digitan", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_30rnd_Tan", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_30rnd_UPC", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_556Stanag_30rnd_camo", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Deagle_Magazine_7rnd", "MaxPriceThreshold": 2000, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MC<PERSON>_Spear_Magazine_20rnd", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MP7A1_Mag_40rnd", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPX_Mag_30rnd", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPX_Mag_60rnd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mag_Glock_17Rnd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mag_M9_15Rnd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mag_M9_Custom_20Rnd", "MaxPriceThreshold": 2000, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_PP91_Mag_20rnd", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Raptr_Drum_45rnd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_TAVOR_Magazine_30rnd", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_TAVOR_Magazine_60rnd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_UZI_Mag_30rnd", "MaxPriceThreshold": 2500, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}