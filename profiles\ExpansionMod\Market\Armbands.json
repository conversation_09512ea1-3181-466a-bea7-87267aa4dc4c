{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_ARMBANDS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "armband_white", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["armband_yellow", "armband_orange", "armband_red", "armband_green", "armband_pink", "armband_blue", "armband_black"]}, {"ClassName": "armband_apa", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["armband_altis", "armband_babydeer", "armband_bear", "armband_bohemia", "armband_brainz", "armband_cdf", "armband_chel", "armband_cmc", "armband_cannibals", "armband_chedaki", "armband_chernarus", "armband_dayz", "armband_hunterz", "armband_livonia", "armband_livoniaarmy", "armband_livoniapolice", "armband_napa", "armband_n<PERSON><PERSON>ni", "armband_pirates", "armband_rsta", "armband_refuge", "armband_rooster", "armband_ssa<PERSON>ni", "armband_snake", "armband_tec", "armband_uec", "armband_wolf", "armband_zenit"]}, {"ClassName": "armband_expansion_expansion", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["armband_expansion_australia", "armband_expansion_canada", "armband_expansion_chernarus", "armband_expansion_france", "armband_expansion_germany", "armband_expansion_latvia", "armband_expansion_luxembourg", "armband_expansion_mexico", "armband_expansion_netherlands", "armband_expansion_newzealand", "armband_expansion_norway", "armband_expansion_poland", "armband_expansion_russia", "armband_expansion_uk", "armband_expansion_usa", "armband_expansion_scotland", "armband_expansion_sweden", "armband_expansion_spain", "armband_expansion_denmark", "armband_expansion_brazil", "armband_expansion_portugal", "armband_expansion_belgium", "armband_expansion_livonia", "armband_expansion_japan", "armband_expansion_china", "armband_expansion_southkorea", "armband_expansion_un", "armband_expansion_nato", "armband_expansion_pirate", "armband_expansion_chedaki", "armband_expansion_napa", "armband_expansion_cdf", "armband_expansion_nuevorico", "armband_expansion_borduria", "armband_expansion_biohazard", "armband_expansion_anyone<PERSON><PERSON>no", "armband_expansion_ireland", "armband_expansion_italy", "armband_expansion_wales", "armband_expansion_switzerland", "armband_expansion_srilanka", "armband_expansion_southafrica", "armband_expansion_sicily", "armband_expansion_offwithhead", "armband_expansion_gibraltar", "armband_expansion_czechia", "armband_expansion_fari", "armband_expansion_finland", "armband_expansion_argentina", "armband_expansion_turkey", "armband_expansion_ukraine", "armband_expansion_dayzwhite", "armband_expansion_dayzblack", "armband_expansion_doubleaxe", "armband_expansion_grenade"]}, {"ClassName": "armband_expansion_white", "MaxPriceThreshold": 30, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["armband_expansion_red", "armband_expansion_blue", "armband_expansion_green", "armband_expansion_yellow", "armband_expansion_orange", "armband_expansion_pink", "armband_expansion_purple", "armband_expansion_rainbow"]}]}