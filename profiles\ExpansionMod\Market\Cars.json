{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_CARS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "<PERSON>roa<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 33600, "MinPriceThreshold": 20160, "SellPricePercent": -1.0, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["hatchbackwheel", "hatchbackwheel", "hatchbackwheel", "hatchbackwheel", "hatchbackwheel", "carbattery", "carradiator", "sparkplug", "hatchbackhood", "hatchbacktrunk", "hatchbackdoors_driver", "hatchbackdoors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "hatchback_02", "MaxPriceThreshold": 24000, "MinPriceThreshold": 14400, "SellPricePercent": -1.0, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["hatchback_02_wheel", "hatchback_02_wheel", "hatchback_02_wheel", "hatchback_02_wheel", "hatchback_02_wheel", "hatchback_02_door_1_1", "hatchback_02_door_2_1", "hatchback_02_door_1_2", "hatchback_02_door_2_2", "hatchback_02_hood", "hatchback_02_trunk", "carbattery", "carradiator", "sparkplug", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "sedan_02", "MaxPriceThreshold": 31200, "MinPriceThreshold": 18720, "SellPricePercent": -1.0, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["sedan_02_wheel", "sedan_02_wheel", "sedan_02_wheel", "sedan_02_wheel", "sedan_02_wheel", "carbattery", "carradiator", "sparkplug", "sedan_02_hood", "sedan_02_trunk", "sedan_02_door_1_1", "sedan_02_door_2_1", "sedan_02_door_1_2", "sedan_02_door_2_2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "civiliansedan", "MaxPriceThreshold": 38400, "MinPriceThreshold": 23040, "SellPricePercent": -1.0, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["civsedanwheel", "civsedanwheel", "civsedanwheel", "civsedanwheel", "civsedanwheel", "carbattery", "carradiator", "sparkplug", "civsedanhood", "civsedantrunk", "civ<PERSON><PERSON><PERSON><PERSON>_driver", "civsedandoors_codriver", "civsedandoors_backleft", "civ<PERSON><PERSON><PERSON><PERSON>_backright", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "truck_01_covered", "MaxPriceThreshold": 48000, "MinPriceThreshold": 28800, "SellPricePercent": -1.0, "MaxStockThreshold": 7, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["truck_01_wheel", "truck_01_wheel", "truck_01_wheel", "truck_01_wheel", "truck_01_wheeldouble", "truck_01_wheeldouble", "truck_01_wheeldouble", "truck_01_wheeldouble", "truckbattery", "truck_01_hood", "truck_01_door_1_1", "truck_01_door_2_1", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "offroad_02", "MaxPriceThreshold": 44400, "MinPriceThreshold": 26640, "SellPricePercent": -1.0, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["offroad_02_wheel", "offroad_02_wheel", "offroad_02_wheel", "offroad_02_wheel", "offroad_02_wheel", "offroad_02_door_1_1", "offroad_02_door_2_1", "offroad_02_door_1_2", "offroad_02_door_2_2", "offroad_02_hood", "offroad_02_trunk", "carbattery", "glowplug", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "expansiontractor", "MaxPriceThreshold": 20000, "MinPriceThreshold": 10000, "SellPricePercent": -1.0, "MaxStockThreshold": 7, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["expansiontractorbackwheel", "expansiontractorbackwheel", "expansiontractorfrontwheel", "expansiontractorfrontwheel", "expansiontractordoorsdriver", "expansiontractordoorscodriver", "headlighth7", "headlighth7", "sparkplug", "carbattery"], "Variants": []}, {"ClassName": "expansionuaz", "MaxPriceThreshold": 60000, "MinPriceThreshold": 30000, "SellPricePercent": -1.0, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["expansionuazwheel", "expansionuazwheel", "expansionuazwheel", "expansionuazwheel", "carbattery", "carradiator", "sparkplug", "expansionuazdoorhood", "expansionuazdoordriver", "expansionuazdoorcodriver", "expansionuazdoorcargo1", "expansionuazdoorcargo2", "headlighth7", "headlighth7"], "Variants": ["expansionuazroofless", "expansionuazcargo", "expansionuazcargoroofless"]}, {"ClassName": "expansionbus", "MaxPriceThreshold": 180000, "MinPriceThreshold": 70000, "SellPricePercent": -1.0, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["expansionbuswheel", "expansionbuswheel", "expansionbuswheeldouble", "expansionbuswheeldouble", "headlighth7", "headlighth7", "truckbattery", "carradiator", "glowplug"], "Variants": []}, {"ClassName": "expansionvodnik", "MaxPriceThreshold": 200000, "MinPriceThreshold": 100000, "SellPricePercent": -1.0, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["expansionvodnikwheel", "expansionvodnikwheel", "expansionvodnikwheel", "expansionvodnikwheel", "truckbattery", "carradiator", "sparkplug", "expansionvodnikdoordriver", "expansionvodnikdoorcodriver"], "Variants": []}, {"ClassName": "expansion_landrover", "MaxPriceThreshold": 180000, "MinPriceThreshold": 70000, "SellPricePercent": -1.0, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["expansion_landrover_hood", "expansion_landrover_driverdoor", "expansion_landrover_codriverdoor", "expansion_landrover_left", "expansion_landrover_right", "expansion_landrover_trunk", "carbattery", "carradiator", "sparkplug", "headlighth7", "headlighth7", "canistergasoline", "seachest", "woodencrate", "woodencrate", "woodencrate", "expansion_landrover_wheel", "expansion_landrover_wheel", "expansion_landrover_wheel", "expansion_landrover_wheel", "expansion_landrover_wheel"], "Variants": []}]}