{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_EXPLOSIVES & #STR_EXPANSION_MARKET_CATEGORY_GRENADES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "rgd5grenade", "MaxPriceThreshold": 22085, "MinPriceThreshold": 13250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m67grenade", "MaxPriceThreshold": 22085, "MinPriceThreshold": 13250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flashgrenade", "MaxPriceThreshold": 3450, "MinPriceThreshold": 2070, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "remotedetonator", "MaxPriceThreshold": 11875, "MinPriceThreshold": 7125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "remotedetonatortrigger", "MaxPriceThreshold": 600, "MinPriceThreshold": 300, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "improvisedexplosive", "MaxPriceThreshold": 600, "MinPriceThreshold": 300, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "plastic_explosive", "MaxPriceThreshold": 16965, "MinPriceThreshold": 10180, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "grenade_chemgas", "MaxPriceThreshold": 480, "MinPriceThreshold": 290, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m18smokegrenade_red", "MaxPriceThreshold": 3450, "MinPriceThreshold": 2070, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["m18smokegrenade_green", "m18smokegrenade_yellow", "m18smokegrenade_purple", "m18smokegrenade_white", "expansion_m18smokegrenade_teargas"]}, {"ClassName": "expansion_m18smokegrenade_teargas", "MaxPriceThreshold": 520, "MinPriceThreshold": 260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rdg2smokegrenade_black", "MaxPriceThreshold": 2760, "MinPriceThreshold": 1655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["rdg2smokegrenade_white"]}, {"ClassName": "rdg2smokegrenade_white", "MaxPriceThreshold": 2760, "MinPriceThreshold": 1655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionsupplysignal", "MaxPriceThreshold": 2000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionsatchel", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "landminetrap", "MaxPriceThreshold": 25445, "MinPriceThreshold": 15265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "claymoremine", "MaxPriceThreshold": 26625, "MinPriceThreshold": 15975, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}