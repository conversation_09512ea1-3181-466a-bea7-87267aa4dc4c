{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_MAGAZINES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "mag_ij70_8rnd", "MaxPriceThreshold": 705, "MinPriceThreshold": 425, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cz75_15rnd", "MaxPriceThreshold": 1105, "MinPriceThreshold": 665, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_glock_15rnd", "MaxPriceThreshold": 880, "MinPriceThreshold": 530, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_mkii_10rnd", "MaxPriceThreshold": 830, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_1911_7rnd", "MaxPriceThreshold": 4275, "MinPriceThreshold": 2565, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_fnx45_15rnd", "MaxPriceThreshold": 7060, "MinPriceThreshold": 4235, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_deagle_9rnd", "MaxPriceThreshold": 320, "MinPriceThreshold": 190, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cz527_5rnd", "MaxPriceThreshold": 1140, "MinPriceThreshold": 685, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cz61_20rnd", "MaxPriceThreshold": 2230, "MinPriceThreshold": 1340, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_pp19_64rnd", "MaxPriceThreshold": 2775, "MinPriceThreshold": 1665, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ump_25rnd", "MaxPriceThreshold": 3070, "MinPriceThreshold": 1840, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_mp5_15rnd", "MaxPriceThreshold": 2775, "MinPriceThreshold": 1665, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_mp5_30rnd", "MaxPriceThreshold": 3125, "MinPriceThreshold": 1875, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_fal_20rnd", "MaxPriceThreshold": 895, "MinPriceThreshold": 535, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_saiga_5rnd", "MaxPriceThreshold": 5600, "MinPriceThreshold": 3360, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_saiga_8rnd", "MaxPriceThreshold": 5600, "MinPriceThreshold": 3360, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_saiga_drum20rnd", "MaxPriceThreshold": 11095, "MinPriceThreshold": 6655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_akm_30rnd", "MaxPriceThreshold": 4995, "MinPriceThreshold": 2995, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_akm_palm30rnd", "MaxPriceThreshold": 4530, "MinPriceThreshold": 2720, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_akm_drum75rnd", "MaxPriceThreshold": 19580, "MinPriceThreshold": 11750, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ak101_30rnd", "MaxPriceThreshold": 2015, "MinPriceThreshold": 1210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ak74_30rnd", "MaxPriceThreshold": 4415, "MinPriceThreshold": 2650, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ak74_45rnd", "MaxPriceThreshold": 4440, "MinPriceThreshold": 2665, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_stanag_30rnd", "MaxPriceThreshold": 4490, "MinPriceThreshold": 2695, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_stanagcoupled_30rnd", "MaxPriceThreshold": 440, "MinPriceThreshold": 220, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_stanag_60rnd", "MaxPriceThreshold": 3510, "MinPriceThreshold": 2105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cmag_10rnd", "MaxPriceThreshold": 4780, "MinPriceThreshold": 2865, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cmag_20rnd", "MaxPriceThreshold": 2370, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cmag_30rnd", "MaxPriceThreshold": 1055, "MinPriceThreshold": 630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cmag_40rnd", "MaxPriceThreshold": 4515, "MinPriceThreshold": 2710, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_vss_10rnd", "MaxPriceThreshold": 8985, "MinPriceThreshold": 5390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_val_20rnd", "MaxPriceThreshold": 9060, "MinPriceThreshold": 5435, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_vikhr_30rnd", "MaxPriceThreshold": 7765, "MinPriceThreshold": 4660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ruger1022_15rnd", "MaxPriceThreshold": 740, "MinPriceThreshold": 445, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ruger1022_30rnd", "MaxPriceThreshold": 600, "MinPriceThreshold": 360, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_svd_10rnd", "MaxPriceThreshold": 1805, "MinPriceThreshold": 1085, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_sv98_10rnd", "MaxPriceThreshold": 3685, "MinPriceThreshold": 2210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_scout_5rnd", "MaxPriceThreshold": 2365, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_famas_25rnd", "MaxPriceThreshold": 1805, "MinPriceThreshold": 1085, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_aug_30rnd", "MaxPriceThreshold": 1805, "MinPriceThreshold": 1085, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_p1_8rnd", "MaxPriceThreshold": 645, "MinPriceThreshold": 390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_cz550_10rnd", "MaxPriceThreshold": 5540, "MinPriceThreshold": 3325, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_ssg82_5rnd", "MaxPriceThreshold": 6600, "MinPriceThreshold": 3960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_m14_10rnd", "MaxPriceThreshold": 6600, "MinPriceThreshold": 3960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_m14_20rnd", "MaxPriceThreshold": 6655, "MinPriceThreshold": 3995, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_taser", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_m9_15rnd", "MaxPriceThreshold": 80, "MinPriceThreshold": 40, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_kedr_20rnd", "MaxPriceThreshold": 400, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_mp7_40rnd", "MaxPriceThreshold": 520, "MinPriceThreshold": 260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_vityaz_30rnd", "MaxPriceThreshold": 500, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_mpx_50rnd", "MaxPriceThreshold": 640, "MinPriceThreshold": 320, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_m14_10rnd", "MaxPriceThreshold": 400, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_m14_20rnd", "MaxPriceThreshold": 520, "MinPriceThreshold": 260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_g36_30rnd", "MaxPriceThreshold": 500, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mag_expansion_awm_5rnd", "MaxPriceThreshold": 1000, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}