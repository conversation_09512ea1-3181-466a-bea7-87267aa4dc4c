=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-27_17-2-41
====================================================================================
17:2:41 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
17:2:41 | [PermissionManager] Perm (FreeCamera) has been registered!
17:2:41 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
17:2:41 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
17:2:41 | [PermissionManager] Perm (MenuServerManager) has been registered!
17:2:41 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
17:2:41 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
17:2:41 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
17:2:41 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
17:2:41 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
17:2:41 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
17:2:41 | [PermissionManager] Perm (MenuObjectManager) has been registered!
17:2:41 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
17:2:41 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
17:2:41 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
17:2:41 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
17:2:41 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
17:2:41 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
17:2:41 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
17:2:41 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
17:2:41 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
17:2:41 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
17:2:41 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
17:2:41 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
17:2:41 | [PermissionManager] Perm (MenuBansManager) has been registered!
17:2:41 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
17:2:41 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
17:2:41 | [PermissionManager] Perm (MenuWebHooks) has been registered!
17:2:41 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
17:2:41 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
17:2:41 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
17:2:41 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
17:2:41 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
17:2:41 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
17:2:41 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
17:2:41 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
17:2:41 | [PermissionManager] Adding Super Admin (76561199239979485)
17:2:41 | [PermissionManager] Adding Super Admin (76561197999250250)
17:2:41 | [PermissionManager] Adding Super Admin (76561198153347717)
17:2:41 | [PermissionManager] Loaded UserGroups.json
17:2:41 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
17:2:41 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
17:2:41 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
17:2:41 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
17:2:41 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
17:2:41 | Building Sets Loaded: 0
17:2:41 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
19:50:53 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
19:51:33 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
19:52:57 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<436.010010, 281.824219, 11246.200195>)
19:52:57 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=436.01,0,11246.2)
19:53:10 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<461.717651, 282.131958, 11239.189453>)
19:53:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<478.650970, 281.907135, 11234.690430>)
19:53:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<499.560974, 280.937256, 11229.138672>)
19:53:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<540.768250, 278.505310, 11218.679688>)
19:53:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<566.377930, 277.019012, 11204.333984>)
19:53:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<590.088989, 275.662994, 11190.627930>)
19:53:22 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<610.606445, 273.811951, 11167.136719>)
19:53:22 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<629.380371, 272.120972, 11154.372070>)
19:53:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<672.389099, 265.808533, 11128.619141>)
19:53:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<708.198303, 260.721191, 11107.431641>)
19:53:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<732.239441, 257.841614, 11093.085938>)
19:53:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<749.366638, 255.680618, 11080.385742>)
19:53:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<767.300964, 253.312393, 11066.683594>)
19:53:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<786.991638, 250.531128, 11051.608398>)
19:53:26 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<889.515076, 234.803711, 11035.278320>)
19:53:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1635.385620, 366.423004, 11040.105469>)
19:53:39 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1635.738647, 367.922791, 11040.125977>)
19:53:40 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1808.448486, 379.852631, 10975.257813>)
19:54:1 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<3551.919922, 212.315842, 8636.110352>)
19:54:1 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=3551.92,0,8636.11)
19:54:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3615.406738, 212.791138, 8361.792969>)
19:55:5 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3976.438721, 214.363373, 7142.447754>)
19:55:7 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4808.225586, 389.121277, 6443.682129>)
19:55:9 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<5028.258301, 197.763229, 6233.271484>)
19:55:22 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4044.945557, 668.789978, 6384.459961>)
19:55:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3252.201904, 284.442993, 6490.844238>)
19:55:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2574.813232, 864.675537, 7136.152832>)
19:55:26 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1802.259521, 336.372437, 7712.852539>)
19:55:40 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2249.179443, 452.347046, 6714.194824>)
19:55:54 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2497.780518, 249.526733, 7088.454590>)
19:56:6 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2579.675781, 244.376678, 6995.530762>)
19:56:6 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2588.716797, 245.604080, 6980.817383>)
19:56:7 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2595.356934, 246.946198, 6967.243164>)
19:56:7 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2600.638184, 248.502945, 6955.346191>)
19:56:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2605.749023, 250.142410, 6941.882813>)
19:56:9 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2609.185547, 251.988647, 6869.761719>)
19:56:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1915.697144, 1004.259338, 6419.547363>)
19:56:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1803.682617, 543.157593, 6541.920898>)
19:58:49 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2690.555908, 1023.251282, 6155.707031>)
19:58:52 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2793.895752, 591.027466, 5782.553711>)
19:59:4 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2809.407471, 1176.152222, 6714.941406>)
19:59:5 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.150879, 197.460632, 7212.727051>)
19:59:7 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2770.983887, 199.067139, 7212.746094>)
19:59:7 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.042969, 200.691925, 7212.616699>)
19:59:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.233398, 202.303558, 7212.621582>)
19:59:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.368896, 203.909027, 7212.729980>)
19:59:9 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.505371, 205.504288, 7212.842285>)
19:59:14 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.485352, 207.169250, 7212.863281>)
19:59:14 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.458496, 208.832870, 7212.878906>)
19:59:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.429443, 210.496521, 7212.893555>)
19:59:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<2771.398193, 212.159821, 7212.907227>)
19:59:16 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1829.733154, 726.049683, 6962.955078>)
19:59:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.352173, 485.457214, 6903.674316>)
19:59:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.240479, 478.875671, 6903.645996>)
19:59:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.267578, 480.545197, 6903.670410>)
19:59:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.300171, 482.214203, 6903.700195>)
19:59:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.294556, 483.880768, 6903.712891>)
19:59:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1452.291992, 485.539673, 6903.674805>)
19:59:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<1939.382324, 716.946045, 5944.479492>)
19:59:25 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
19:59:27 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
