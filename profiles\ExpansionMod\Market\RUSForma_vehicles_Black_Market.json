{"m_Version": 12, "DisplayName": "RUS Forma Vehicles Black Market", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "kraz_255B", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kraz_255B_doors_hood", "kraz_255B_doors_trunk", "kraz_255B_doors_driver", "kraz_255B_doors_codriver", "kraz_255B_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kraz_255B_kung", "MaxPriceThreshold": 205000, "MinPriceThreshold": 205000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "kraz_255B_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kraz_255B_doors_hood", "Kraz_255B_doors_trunk_kung", "kraz_255B_doors_driver", "kraz_255B_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AEC_Matador", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AEC_Matador_wheel", "AEC_Matador_wheel", "AEC_Matador_wheel", "AEC_Matador_wheel", "AEC_Matador_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "AEC_Matador_doors_trunk", "A<PERSON>_<PERSON><PERSON>_doors_driver", "AEC_Matador_doors_codriver", "AEC_Matado_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "artillery_tractor", "MaxPriceThreshold": 200000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "artillery_tractor_tent", "artillery_tractor_doors_driver", "artillery_tractor_doors_codriver", "artillery_tractor_doors_hood", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "artillery_tractor_camo", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "artillery_tractor_tent", "artillery_tractor_doors_driver_camo", "artillery_tractor_doors_codriver_camo", "artillery_tractor_doors_hood_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "artillery_tractor_kung", "MaxPriceThreshold": 213000, "MinPriceThreshold": 213000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "artillery_tractor_doors_driver", "artillery_tractor_doors_codriver", "artillery_tractor_doors_hood", "artillery_tractor_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "artillery_tractor_kung_camo", "MaxPriceThreshold": 220000, "MinPriceThreshold": 220000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "artillery_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "artillery_tractor_doors_driver_camo", "artillery_tractor_doors_codriver_camo", "artillery_tractor_doors_hood_camo", "artillery_tractor_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AZLK_2141RF_milicia", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood_milicia", "AZLK_2141RF_doors_driver_milicia", "AZLK_2141RF_doors_codriver_milicia", "AZLK_2141RF_doors_cargo1_milicia", "AZLK_2141RF_doors_cargo2_milicia", "AZLK_2141RF_doors_trunk_milicia", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "AZLK_2141RF_milicia_PG", "MaxPriceThreshold": 210000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "AZLK_2141RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "AZLK_2141RF_doors_hood_milicia_PG", "AZLK_2141RF_doors_driver_milicia_PG", "AZLK_2141RF_doors_codriver_milicia_PG", "AZLK_2141RF_doors_cargo1_milicia_PG", "AZLK_2141RF_doors_cargo2_milicia_PG", "AZLK_2141RF_doors_trunk_milicia_PG", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BRDM2_RF", "MaxPriceThreshold": 200000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BRDM2_RF_doors_hood", "BRDM2_RF_doors_driver", "BRDM2_RF_doors_codriver", "BRDM2_RF_doors_cargo1", "BRDM2_RF_doors_cargo2", "AZLK_2141RF_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BRDM2_RF_camo", "MaxPriceThreshold": 205000, "MinPriceThreshold": 205000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BRDM2_RF_doors_hood_camo", "BRDM2_RF_doors_driver_camo", "BRDM2_RF_doors_codriver_camo", "BRDM2_RF_doors_cargo1_camo", "BRDM2_RF_doors_cargo2_camo", "AZLK_2141RF_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BRDM2_RF_winter", "MaxPriceThreshold": 200000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "BRDM2_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BRDM2_RF_doors_hood_winter", "BRDM2_RF_doors_driver_winter", "BRDM2_RF_doors_codriver_winter", "BRDM2_RF_doors_cargo1_winter", "BRDM2_RF_doors_cargo2_winter", "AZLK_2141RF_doors_trunk_winter", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80", "MaxPriceThreshold": 200000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3", "BTR_80_doors_driver", "BTR_80_doors_codriver", "BTR_80_doors_cargo1", "BTR_80_doors_cargo2", "BTR_80_doors_cargo4", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_camo", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_camo", "BTR_80_doors_driver_camo", "BTR_80_doors_codriver_camo", "BTR_80_doors_cargo1_camo", "BTR_80_doors_cargo2_camo", "BTR_80_doors_cargo4_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_camo2", "MaxPriceThreshold": 209000, "MinPriceThreshold": 209000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_camo2", "BTR_80_doors_driver_camo2", "BTR_80_doors_codriver_camo2", "BTR_80_doors_cargo1_camo2", "BTR_80_doors_cargo2_camo2", "BTR_80_doors_cargo4_camo2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_rust", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_rust", "BTR_80_doors_driver_rust", "BTR_80_doors_codriver_rust", "BTR_80_doors_cargo1_rust", "BTR_80_doors_cargo2_rust", "BTR_80_doors_cargo4_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_P", "MaxPriceThreshold": 210000, "MinPriceThreshold": 210000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3", "BTR_80_doors_driver", "BTR_80_doors_codriver", "BTR_80_doors_cargo1", "BTR_80_doors_cargo2", "BTR_80_doors_cargo4", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_P_camo", "MaxPriceThreshold": 213000, "MinPriceThreshold": 213000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_camo", "BTR_80_doors_driver_camo", "BTR_80_doors_codriver_camo", "BTR_80_doors_cargo1_camo", "BTR_80_doors_cargo2_camo", "BTR_80_doors_cargo4_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_P_camo2", "MaxPriceThreshold": 213000, "MinPriceThreshold": 213000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_camo2", "BTR_80_doors_driver_camo2", "BTR_80_doors_codriver_camo2", "BTR_80_doors_cargo1_camo2", "BTR_80_doors_cargo2_camo2", "BTR_80_doors_cargo4_camo2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "BTR_80_P_rust", "MaxPriceThreshold": 213000, "MinPriceThreshold": 213000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "BTR_80_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "BTR_80_doors_cargo3_rust", "BTR_80_doors_driver_rust", "BTR_80_doors_codriver_rust", "BTR_80_doors_cargo1_rust", "BTR_80_doors_cargo2_rust", "BTR_80_doors_cargo4_rust", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Gaz_233002_Tiger", "MaxPriceThreshold": 211000, "MinPriceThreshold": 211000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "CarBattery", "CarRadiator", "SparkPlug", "Gaz_233002_Tiger_doors_hood", "Gaz_233002_Tiger_doors_driver", "Gaz_233002_<PERSON>_doors_codriver", "Gaz_233002_Tiger_tent", "Gaz_233002_Tiger_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Gaz_233002_Tiger_camo", "MaxPriceThreshold": 211000, "MinPriceThreshold": 211000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "Gaz_233002_Tiger_wheel", "CarBattery", "CarRadiator", "SparkPlug", "Gaz_233002_Tiger_doors_hood_camo", "Gaz_233002_Tiger_doors_driver_camo", "Gaz_233002_Tiger_doors_codriver_camo", "Gaz_233002_Tiger_tent_camo", "Gaz_233002_Tiger_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "gaz_51", "MaxPriceThreshold": 233000, "MinPriceThreshold": 233000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["gaz_51_wheel", "gaz_51_wheel", "gaz_51_wheel", "gaz_51_WheelDouble", "gaz_51_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "gaz_51_doors_hood", "gaz_51_doors_driver", "gaz_51_doors_codriver", "gaz_51_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66", "MaxPriceThreshold": 220000, "MinPriceThreshold": 220000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver", "GAZ_66_doors_codriver", "GAZ_66_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66_camo", "MaxPriceThreshold": 225000, "MinPriceThreshold": 225000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver_camo", "GAZ_66_doors_codriver_camo", "GAZ_66_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66_zima_camo", "MaxPriceThreshold": 225000, "MinPriceThreshold": 225000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver_zima_camo", "GAZ_66_doors_codriver_zima_camo", "GAZ_66_doors_trunk_zima_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66_kuzov", "MaxPriceThreshold": 220000, "MinPriceThreshold": 220000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver_kuzov", "GAZ_66_doors_codriver_kuzov", "GAZ_66_doors_trunk_kuzov", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66_kuzov_camo", "MaxPriceThreshold": 222000, "MinPriceThreshold": 222000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver_camo", "GAZ_66_doors_codriver_camo", "GAZ_66_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_66_kuzov_zima_camo", "MaxPriceThreshold": 223400, "MinPriceThreshold": 223400, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "GAZ_66_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "GAZ_66_doors_driver_zima_camo", "GAZ_66_doors_codriver_zima_camo", "GAZ_66_doors_trunk_zima_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_69_rus", "MaxPriceThreshold": 230000, "MinPriceThreshold": 230000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_69_rus_doors_hood", "GAZ_69_rus_doors_driver", "GAZ_69_rus_doors_codriver", "GAZ_69_rus_doors_cargo1", "GAZ_69_rus_doors_cargo2", "GAZ_69_rus_doors_trunk", "GAZ_69_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_69_rus_green", "MaxPriceThreshold": 230000, "MinPriceThreshold": 230000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_69_rus_doors_hood_green", "GAZ_69_rus_doors_driver_green", "GAZ_69_rus_doors_codriver_green", "GAZ_69_rus_doors_cargo1_green", "GAZ_69_rus_doors_cargo2_green", "GAZ_69_rus_doors_trunk_green", "GAZ_69_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "GAZ_69_rus_yellow", "MaxPriceThreshold": 219000, "MinPriceThreshold": 219000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "GAZ_69_rus_wheel", "CarBattery", "CarRadiator", "SparkPlug", "GAZ_69_rus_doors_hood_yellow", "GAZ_69_rus_doors_driver_yellow", "GAZ_69_rus_doors_codriver_yellow", "GAZ_69_rus_doors_cargo1_yellow", "GAZ_69_rus_doors_cargo2_yellow", "GAZ_69_rus_doors_trunk_yellow", "GAZ_69_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS", "MaxPriceThreshold": 230000, "MinPriceThreshold": 230000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood", "kamaz_4310RUS_doors_driver", "kamaz_4310RUS_doors_codriver", "kamaz_4310RUS_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_blue", "MaxPriceThreshold": 225000, "MinPriceThreshold": 225000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood_blue", "kamaz_4310RUS_doors_driver_blue", "kamaz_4310RUS_doors_codriver_blue", "kamaz_4310RUS_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_orange", "MaxPriceThreshold": 225500, "MinPriceThreshold": 225500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood_orange", "kamaz_4310RUS_doors_driver_orange", "kamaz_4310RUS_doors_codriver_orange", "kamaz_4310RUS_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_kung", "MaxPriceThreshold": 223400, "MinPriceThreshold": 223400, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood", "kamaz_4310RUS_doors_driver", "kamaz_4310RUS_doors_codriver", "kamaz_4310RUS_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_kung_blue", "MaxPriceThreshold": 224000, "MinPriceThreshold": 224000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood_blue", "kamaz_4310RUS_doors_driver_blue", "kamaz_4310RUS_doors_codriver_blue", "kamaz_4310RUS_doors_trunk_blue", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_kung_orange", "MaxPriceThreshold": 224000, "MinPriceThreshold": 224000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood_orange", "kamaz_4310RUS_doors_driver_orange", "kamaz_4310RUS_doors_codriver_orange", "kamaz_4310RUS_doors_trunk_orange", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "kamaz_4310RUS_kung_oon", "MaxPriceThreshold": 222000, "MinPriceThreshold": 222000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "kamaz_4310RUS_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "kamaz_4310RUS_doors_hood_oon", "kamaz_4310RUS_doors_driver_oon", "kamaz_4310RUS_doors_codriver_oon", "kamaz_4310RUS_doors_trunk_oon", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LUAZ_969", "MaxPriceThreshold": 30000, "MinPriceThreshold": 30000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "CarBattery", "CarRadiator", "SparkPlug", "LUAZ_969_doors_hood", "LUAZ_969_doors_driver", "LUAZ_969_doors_codriver", "LUAZ_969_doors_trunk", "LUAZ_969_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LUAZ_969_green", "MaxPriceThreshold": 29000, "MinPriceThreshold": 29000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "CarBattery", "CarRadiator", "SparkPlug", "LUAZ_969_doors_hood_green", "LUAZ_969_doors_driver_green", "LUAZ_969_doors_codriver_green", "LUAZ_969_doors_trunk_green", "LUAZ_969_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LUAZ_969_blue", "MaxPriceThreshold": 28900, "MinPriceThreshold": 28900, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "CarBattery", "CarRadiator", "SparkPlug", "LUAZ_969_doors_hood_blue", "LUAZ_969_doors_driver_blue", "LUAZ_969_doors_codriver_blue", "LUAZ_969_doors_trunk_blue", "LUAZ_969_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "LUAZ_969_orang", "MaxPriceThreshold": 28000, "MinPriceThreshold": 28000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "LUAZ_969_wheel", "CarBattery", "CarRadiator", "SparkPlug", "LUAZ_969_doors_hood_orang", "LUAZ_969_doors_driver_orang", "LUAZ_969_doors_codriver_orang", "LUAZ_969_doors_trunk_orang", "LUAZ_969_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "M3A1_Scout_Car", "MaxPriceThreshold": 16000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["M3A1_Scout_Car_wheel", "M3A1_Scout_Car_wheel", "M3A1_Scout_Car_wheel", "M3A1_Scout_Car_wheel", "M3A1_Scout_Car_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "M3A1_Scout_Car_doors_hood", "M3A1_Scout_Car_doors_driver", "M3A1_Scout_Car_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "m4_tractor", "MaxPriceThreshold": 430000, "MinPriceThreshold": 430000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "m4_tractor_doors_hood", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "m4_tractor_usa", "MaxPriceThreshold": 440000, "MinPriceThreshold": 440000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "m4_tractor_doors_hood_usa", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "m4_tractor_winter", "MaxPriceThreshold": 450500, "MinPriceThreshold": 450500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "m4_tractor_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "m4_tractor_doors_hood_winter", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Mercedes_G4_W31", "MaxPriceThreshold": 19844, "MinPriceThreshold": 19844, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "Mercedes_G4_W31_Wheel", "CarBattery", "CarRadiator", "SparkPlug", "Mercedes_G4_W31_doors_hood", "Mercedes_G4_W31_doors_driver", "Mercedes_G4_W31_doors_codriver", "Mercedes_G4_W31_doors_cargo1", "Mercedes_G4_W31_doors_cargo2", "Mercedes_G4_W31_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "MTLB_RF", "MaxPriceThreshold": 696969, "MinPriceThreshold": 696969, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "MTLB_RF_doors_hood", "MTLB_RF_doors_driver", "MTLB_RF_doors_codriver", "MTLB_RF_doors_cargo1", "MTLB_RF_doors_cargo2", "MTLB_RF_doors_trunk", "MTLB_RF_doors_trunk0", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "MTLB_RF_camo", "MaxPriceThreshold": 699000, "MinPriceThreshold": 699000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "MTLB_RF_doors_hood_camo", "MTLB_RF_doors_driver_camo", "MTLB_RF_doors_codriver_camo", "MTLB_RF_doors_cargo1_camo", "MTLB_RF_doors_cargo2_camo", "MTLB_RF_doors_trunk_camo", "MTLB_RF_doors_trunk0_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "MTLB_RF_camo2", "MaxPriceThreshold": 699990, "MinPriceThreshold": 699990, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "MTLB_RF_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "MTLB_RF_doors_hood_camo2", "MTLB_RF_doors_driver_camo2", "MTLB_RF_doors_codriver_camo2", "MTLB_RF_doors_cargo1_camo2", "MTLB_RF_doors_cargo2_camo2", "MTLB_RF_doors_trunk_camo2", "MTLB_RF_doors_trunk0_camo2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "opel_blitz", "MaxPriceThreshold": 19000, "MinPriceThreshold": 19000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["opel_blitz_wheel", "opel_blitz_wheel", "opel_blitz_wheel", "opel_blitz_WheelDouble", "opel_blitz_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "opel_blitz_doors_hood", "opel_blitz_doors_driver", "opel_blitz_doors_codriver", "opel_blitz_doors_hood_two", "opel_blitz_doors_trunk", "opel_blitz_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "opel_blitz_grey", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["opel_blitz_wheel", "opel_blitz_wheel", "opel_blitz_wheel", "opel_blitz_WheelDouble", "opel_blitz_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "opel_blitz_doors_hood_grey", "opel_blitz_doors_driver_grey", "opel_blitz_doors_codriver_grey", "opel_blitz_doors_hood_two_grey", "opel_blitz_doors_trunk_grey", "opel_blitz_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "T_34_76", "MaxPriceThreshold": 890000, "MinPriceThreshold": 890000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "T_34_76_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "T_34_76_doors_driver", "T_34_76_doors_cargo1", "T_34_76_doors_cargo2", "T_34_76_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "T_34_76_camo", "MaxPriceThreshold": 890000, "MinPriceThreshold": 890000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "T_34_76_wheel_camo", "TruckBattery", "CarRadiator", "SparkPlug", "T_34_76_doors_driver_camo", "T_34_76_doors_cargo1_camo", "T_34_76_doors_cargo2_camo", "T_34_76_doors_trunk_camo", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "T_34_76_camo2", "MaxPriceThreshold": 899000, "MinPriceThreshold": 899000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "T_34_76_wheel_camo2", "TruckBattery", "CarRadiator", "SparkPlug", "T_34_76_doors_driver_camo2", "T_34_76_doors_cargo1_camo2", "T_34_76_doors_cargo2_camo2", "T_34_76_doors_trunk_camo2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_27722_mchs", "MaxPriceThreshold": 22000, "MinPriceThreshold": 22000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_27722_mchs_doors_hood", "UAZ_27722_mchs_doors_driver", "UAZ_27722_mchs_doors_codriver", "UAZ_27722_mchs_doors_cargo1", "UAZ_27722_mchs_doors_trunk1", "UAZ_27722_mchs_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_27722_ambulancia", "MaxPriceThreshold": 21000, "MinPriceThreshold": 21000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_27722_doors_hood_ambulancia", "UAZ_27722_doors_driver_ambulancia", "UAZ_27722_doors_codriver_ambulancia", "UAZ_27722_doors_cargo1_ambulancia", "UAZ_27722_doors_trunk1_ambulancia", "UAZ_27722_doors_trunk_ambulancia", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_27722_ambulancia2", "MaxPriceThreshold": 21000, "MinPriceThreshold": 21000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "UAZ_27722_mchs_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_27722_doors_hood_ambulancia2", "UAZ_27722_doors_driver_ambulancia2", "UAZ_27722_doors_codriver_ambulancia2", "UAZ_27722_doors_cargo1_ambulancia2", "UAZ_27722_doors_trunk1_ambulancia2", "UAZ_27722_doors_trunk_ambulancia2", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_31519_Police", "MaxPriceThreshold": 21000, "MinPriceThreshold": 21000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_31519_Police_wheel", "UAZ_31519_Police_wheel", "UAZ_31519_Police_wheel", "UAZ_31519_Police_wheel", "UAZ_31519_Police_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_31519_Police_doors_hood", "UAZ_31519_Police_doors_driver", "UAZ_31519_Police_doors_codriver", "UAZ_31519_Police_doors_cargo1", "UAZ_31519_Police_doors_cargo2", "UAZ_31519_Police_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_gamekeeper", "MaxPriceThreshold": 22000, "MinPriceThreshold": 22000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_gamekeeper", "UAZ_3163_doors_driver_gamekeeper", "UAZ_3163_doors_codriver_gamekeeper", "UAZ_3163_doors_cargo1_gamekeeper", "UAZ_3163_doors_cargo2_gamekeeper", "UAZ_3163_doors_trunk_gamekeeper", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_Police", "MaxPriceThreshold": 40000, "MinPriceThreshold": 40000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_Police", "UAZ_3163_doors_driver_Police", "UAZ_3163_doors_codriver_Police", "UAZ_3163_doors_cargo1_Police", "UAZ_3163_doors_cargo2_Police", "UAZ_3163_doors_trunk_Police", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_Ambulance", "MaxPriceThreshold": 40000, "MinPriceThreshold": 40000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_Ambulance", "UAZ_3163_doors_driver_Ambulance", "UAZ_3163_doors_codriver_Ambulance", "UAZ_3163_doors_cargo1_Ambulance", "UAZ_3163_doors_cargo2_Ambulance", "UAZ_3163_doors_trunk_Ambulance", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_MCHS", "MaxPriceThreshold": 43000, "MinPriceThreshold": 43000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_MCHS", "UAZ_3163_doors_driver_MCHS", "UAZ_3163_doors_codriver_MCHS", "UAZ_3163_doors_cargo1_MCHS", "UAZ_3163_doors_cargo2_MCHS", "UAZ_3163_doors_trunk_MCHS", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_VP", "MaxPriceThreshold": 26000, "MinPriceThreshold": 26000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_VP", "UAZ_3163_doors_driver_VP", "UAZ_3163_doors_codriver_VP", "UAZ_3163_doors_cargo1_VP", "UAZ_3163_doors_cargo2_VP", "UAZ_3163_doors_trunk_VP", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3163_emergency_service", "MaxPriceThreshold": 39000, "MinPriceThreshold": 39000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "UAZ_3163_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3163_doors_hood_emergency_service", "UAZ_3163_doors_driver_emergency_service", "UAZ_3163_doors_codriver_emergency_service", "UAZ_3163_doors_cargo1_emergency_service", "UAZ_3163_doors_cargo2_emergency_service", "UAZ_3163_doors_trunk_emergency_service", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3172", "MaxPriceThreshold": 67000, "MinPriceThreshold": 67000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3172_doors_hood", "UAZ_3172_doors_driver", "UAZ_3172_doors_codriver", "UAZ_3172_doors_cargo1", "UAZ_3172_doors_cargo2", "UAZ_3172_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3172_green", "MaxPriceThreshold": 67000, "MinPriceThreshold": 67000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3172_doors_hood_green", "UAZ_3172_doors_driver_green", "UAZ_3172_doors_codriver_green", "UAZ_3172_doors_cargo1_green", "UAZ_3172_doors_cargo2_green", "UAZ_3172_doors_trunk_green", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3172_camo_flora", "MaxPriceThreshold": 67000, "MinPriceThreshold": 67000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3172_doors_hood_camo_flora", "UAZ_3172_doors_driver_camo_flora", "UAZ_3172_doors_codriver_camo_flora", "UAZ_3172_doors_cargo1_camo_flora", "UAZ_3172_doors_cargo2_camo_flora", "UAZ_3172_doors_trunk_camo_flora", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3172_camo_Winter", "MaxPriceThreshold": 67000, "MinPriceThreshold": 67000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3172_doors_hood_camo_Winter", "UAZ_3172_doors_driver_camo_Winter", "UAZ_3172_doors_codriver_camo_Winter", "UAZ_3172_doors_cargo1_camo_Winter", "UAZ_3172_doors_cargo2_camo_Winter", "UAZ_3172_doors_trunk_camo_Winter", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3172_camo_Desert", "MaxPriceThreshold": 68000, "MinPriceThreshold": 68000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "UAZ_3172_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3172_doors_hood_camo_Desert", "UAZ_3172_doors_driver_camo_Desert", "UAZ_3172_doors_codriver_camo_Desert", "UAZ_3172_doors_cargo1_camo_Desert", "UAZ_3172_doors_cargo2_camo_Desert", "UAZ_3172_doors_trunk_camo_Desert", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3962_MCHS", "MaxPriceThreshold": 68000, "MinPriceThreshold": 68000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3962_doors_driver_MCHS", "UAZ_3962_doors_codriver_MCHS", "UAZ_3962_doors_cargo1_MCHS", "UAZ_3962_doors_trunk_MCHS", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_3962_VAI", "MaxPriceThreshold": 68000, "MinPriceThreshold": 68000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "UAZ_3962_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_3962_doors_driver_VAI", "UAZ_3962_doors_codriver_VAI", "UAZ_3962_doors_cargo1_VAI", "UAZ_3962_doors_trunk_VAI", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_army", "MaxPriceThreshold": 69000, "MinPriceThreshold": 69000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_army_doors_hood", "UAZ_469_army_doors_driver", "UAZ_469_army_doors_codriver", "UAZ_469_army_doors_cargo1", "UAZ_469_army_doors_cargo2", "UAZ_469_army_doors_trunk", "UAZ_469_army_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_army_yellow", "MaxPriceThreshold": 69000, "MinPriceThreshold": 69000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_army_doors_hood_yellow", "UAZ_469_army_doors_driver_yellow", "UAZ_469_army_doors_codriver_yellow", "UAZ_469_army_doors_cargo1_yellow", "UAZ_469_army_doors_cargo2_yellow", "UAZ_469_army_doors_trunk_yellow", "UAZ_469_army_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_army_camo", "MaxPriceThreshold": 70000, "MinPriceThreshold": 70000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_army_doors_hood_camo", "UAZ_469_army_doors_driver_camo", "UAZ_469_army_doors_codriver_camo", "UAZ_469_army_doors_cargo1_camo", "UAZ_469_army_doors_cargo2_camo", "UAZ_469_army_doors_trunk_camo", "UAZ_469_army_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_army_CDF", "MaxPriceThreshold": 70000, "MinPriceThreshold": 70000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_army_doors_hood_CDF", "UAZ_469_army_doors_driver_CDF", "UAZ_469_army_doors_codriver_CDF", "UAZ_469_army_doors_cargo1_CDF", "UAZ_469_army_doors_cargo2_CDF", "UAZ_469_army_doors_trunk_CDF", "UAZ_469_army_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_army_ChDKZ", "MaxPriceThreshold": 89000, "MinPriceThreshold": 89000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_army_doors_hood_ChDKZ", "UAZ_469_army_doors_driver_ChDKZ", "UAZ_469_army_doors_codriver_ChDKZ", "UAZ_469_army_doors_cargo1_ChDKZ", "UAZ_469_army_doors_cargo2_ChDKZ", "UAZ_469_army_doors_trunk_ChDKZ", "UAZ_469_army_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_Hunter", "MaxPriceThreshold": 80000, "MinPriceThreshold": 80000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_<PERSON>_doors_hood", "UAZ_469_<PERSON>_doors_driver", "UAZ_469_<PERSON>_doors_codriver", "UAZ_469_Hunter_doors_cargo1", "UAZ_469_Hunter_doors_cargo2", "UAZ_469_<PERSON>_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "UAZ_469_milicia", "MaxPriceThreshold": 29000, "MinPriceThreshold": 29000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "UAZ_469_army_wheel", "CarBattery", "CarRadiator", "SparkPlug", "UAZ_469_milicia_doors_hood", "UAZ_469_milicia_doors_driver", "UAZ_469_milicia_doors_codriver", "UAZ_469_milicia_doors_cargo1", "UAZ_469_milicia_doors_cargo2", "UAZ_469_milicia_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "URAL_375_1964", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "URAL_375_1964_doors_hood", "URAL_375_1964_doors_driver", "URAL_375_1964_doors_codriver", "URAL_375_1964_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "URAL_375_1964_zima", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "URAL_375_1964_doors_hood_zima", "URAL_375_1964_doors_driver_zima", "URAL_375_1964_doors_codriver_zima", "URAL_375_1964_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "URAL_375_1964_CDF", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "URAL_375_1964_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "URAL_375_1964_doors_hood_CDF", "URAL_375_1964_doors_driver_CDF", "URAL_375_1964_doors_codriver_CDF", "URAL_375_1964_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "vaz_2106_milicia", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "vaz_2106_RF_wheel", "CarBattery", "CarRadiator", "SparkPlug", "vaz_2106_RF_doors_hood_milicia", "vaz_2106_RF_doors_driver_milicia", "vaz_2106_RF_doors_codriver_milicia", "vaz_2106_RF_doors_cargo1_milicia", "vaz_2106_RF_doors_cargo2_milicia", "vaz_2106_RF_doors_trunk_milicia", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "VBL", "MaxPriceThreshold": 289000, "MinPriceThreshold": 289000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["VBL_wheel", "VBL_wheel", "VBL_wheel", "VBL_wheel", "VBL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "VBL_doors_driver", "VBL_doors_codriver", "VBL_doors_cargo1", "VBL_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "Willys_MB", "MaxPriceThreshold": 89000, "MinPriceThreshold": 89000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["<PERSON><PERSON>_MB_wheel", "<PERSON><PERSON>_MB_wheel", "<PERSON><PERSON>_MB_wheel", "<PERSON><PERSON>_MB_wheel", "<PERSON><PERSON>_MB_wheel", "CarBattery", "CarRadiator", "SparkPlug", "<PERSON><PERSON>_<PERSON>_doors_hood", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZAZ_968_milicia", "MaxPriceThreshold": 21000, "MinPriceThreshold": 21000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "ZAZ_968_wheel", "CarBattery", "CarRadiator", "SparkPlug", "ZAZ_968_doors_hood_milicia", "ZAZ_968_doors_driver_milicia", "ZAZ_968_doors_codriver_milicia", "ZAZ_968_doors_trunk_milicia", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_130_ac40", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_130_ac40_wheel", "ZIL_130_ac40_wheel", "ZIL_130_ac40_wheel", "ZIL_130_ac40_WheelDouble", "ZIL_130_ac40_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_130_ac40_doors_hood", "ZIL_130_ac40_driver", "ZIL_130_ac40_codriver", "ZIL_130_ac40_trunk", "ZIL_130_ac40_doors_cargo1", "ZIL_130_ac40_doors_cargo2", "ZIL_130_ac40_doors_trunk2", "ZIL_130_ac40_doors_trunk3", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_131", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_131_doors_hood", "ZIL_131_doors_driver", "ZIL_131_doors_codriver", "ZIL_131_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_131_tent", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "ZIL_131_Wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_131_tent_box", "ZIL_131_doors_hood", "ZIL_131_doors_driver", "ZIL_131_doors_codriver", "ZIL_131_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_rus", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood", "ZIL_157_rus_doors_driver", "ZIL_157_rus_doors_codriver", "ZIL_157_rus_doors_trunk", "ZIL_157_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_rus_gray", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood_gray", "ZIL_157_rus_doors_driver_gray", "ZIL_157_rus_doors_codriver_gray", "ZIL_157_rus_doors_trunk_gray", "ZIL_157_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_rus_yellow", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood_yellow", "ZIL_157_rus_doors_driver_yellow", "ZIL_157_rus_doors_codriver_yellow", "ZIL_157_rus_doors_trunk_yellow", "ZIL_157_rus_tent", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_kung_rus", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood", "ZIL_157_rus_doors_driver", "ZIL_157_rus_doors_codriver", "ZIL_157_rus_doors_trunk", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_kung_rus_gray", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood_gray", "ZIL_157_rus_doors_driver_gray", "ZIL_157_rus_doors_codriver_gray", "ZIL_157_rus_doors_trunk_gray", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZIL_157_kung_rus_yellow", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "ZIL_157_rus_wheel", "TruckBattery", "CarRadiator", "SparkPlug", "ZIL_157_rus_doors_hood_yellow", "ZIL_157_rus_doors_driver_yellow", "ZIL_157_rus_doors_codriver_yellow", "ZIL_157_rus_doors_trunk_yellow", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZiS5_Benzovoz", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_WheelDouble", "ZiS5_Benzovoz_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIS5_kung_doors_hood", "Zi<PERSON><PERSON>_Benz<PERSON>z_doors_driver", "ZiS5_Benzovoz_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "ZiS5_kung", "MaxPriceThreshold": 150000, "MinPriceThreshold": 150000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_wheel", "ZiS5_Benzovoz_WheelDouble", "ZiS5_Benzovoz_WheelDouble", "TruckBattery", "CarRadiator", "SparkPlug", "ZIS5_kung_tent", "ZIS5_kung_doors_hood", "Zi<PERSON><PERSON>_Benz<PERSON>z_doors_driver", "ZiS5_Benzovoz_doors_codriver", "headlighth7", "headlighth7"], "Variants": []}, {"ClassName": "IJ_2140_SLGAI", "MaxPriceThreshold": 40000, "MinPriceThreshold": 40000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "IJ_2140_SL_wheel", "CarBattery", "CarRadiator", "SparkPlug", "IJ_2140_SL_doors_hood_GAI", "IJ_2140_SL_doors_driver_GAI", "IJ_2140_SL_doors_codriver_GAI", "IJ_2140_SL_doors_cargo1_GAI", "IJ_2140_SL_doors_cargo2_GAI", "IJ_2140_SL_doors_trunk_GAI", "headlighth7", "headlighth7"], "Variants": []}]}