{"m_Version": 12, "DisplayName": "<PERSON><PERSON><PERSON>_Hand_Gaurds", "Icon": "Deliver", "Color": "FBFCFEFF", "InitStockPercent": 75, "Items": [{"ClassName": "SNAFU_M249HG", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AK_HG", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SR3HG", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M60_Handguard_E3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M60_Handguard_E4", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_DSAHG1", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_DSAHG2", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOEFS", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOEFS_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOEFS_Grey", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOEFS_OD", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOELS", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOELS_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOELS_Grey", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MOELS_OD", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_GADAR", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HG625", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HGGS", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK47HG", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR07", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR08", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR09", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR10", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR13", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MCMR15", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HGMlok", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HGMlok_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK7", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK9", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK10", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK13", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MK15", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_LVOAHG", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}