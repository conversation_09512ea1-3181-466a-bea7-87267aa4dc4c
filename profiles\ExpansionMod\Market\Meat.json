{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_MEAT", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "bearsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "goatsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "boarsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pigsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "deersteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "wolfsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cowsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sheepsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "chickenbreastmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rabbitlegmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "foxsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mouflonsteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "reindeersteakmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}