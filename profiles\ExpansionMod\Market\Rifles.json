{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_RIFLES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "izh18", "MaxPriceThreshold": 885, "MinPriceThreshold": 530, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ruger1022", "MaxPriceThreshold": 1440, "MinPriceThreshold": 865, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_ruger1022_15rnd"], "Variants": []}, {"ClassName": "repeater", "MaxPriceThreshold": 2500, "MinPriceThreshold": 1500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mosin9130", "MaxPriceThreshold": 2050, "MinPriceThreshold": 1230, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cz527", "MaxPriceThreshold": 1300, "MinPriceThreshold": 780, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_cz527_5rnd"], "Variants": []}, {"ClassName": "cz550", "MaxPriceThreshold": 6210, "MinPriceThreshold": 3725, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_cz550_10rnd"], "Variants": []}, {"ClassName": "winchester70", "MaxPriceThreshold": 6210, "MinPriceThreshold": 3725, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ssg82", "MaxPriceThreshold": 6600, "MinPriceThreshold": 3960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_ssg82_5rnd"], "Variants": []}, {"ClassName": "sks", "MaxPriceThreshold": 1635, "MinPriceThreshold": 980, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["sks_bayonet"], "Variants": []}, {"ClassName": "m14", "MaxPriceThreshold": 11190, "MinPriceThreshold": 6715, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["m14_10rnd", "m14_20rnd"], "Variants": []}, {"ClassName": "expansion_kar98", "MaxPriceThreshold": 2050, "MinPriceThreshold": 1230, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_m1a", "MaxPriceThreshold": 11190, "MinPriceThreshold": 6715, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_expansion_m14_10rnd"], "Variants": []}]}