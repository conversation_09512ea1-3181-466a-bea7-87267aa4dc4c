{"m_Version": 5, "EnableStatusTab": 1, "EnablePartyTab": 1, "EnableServerInfoTab": 1, "EnableServerRulesTab": 1, "EnableTerritoryTab": 1, "EnableBookMenu": 1, "CreateBookmarks": 0, "ShowHaBStats": 1, "ShowPlayerFaction": 0, "RuleCategories": [{"CategoryName": "General", "Rules": [{"RuleParagraph": "1.1.", "RuleText": "Insults, discrimination, extremist and racist statements or texts are taboo."}, {"RuleParagraph": "1.2.", "RuleText": "We reserve the right to exclude people from the server who share extremist or racist ideas or who clearly disturb the server harmony."}, {"RuleParagraph": "1.3.", "RuleText": "Decisions of the team members, both the supporter and the admin are to be accepted without discussion."}, {"RuleParagraph": "1.4.", "RuleText": "Provocations and toxic behavior will not be tolerated and punished! Be friendly to fellow players and your team, both in chat and in voice!"}, {"RuleParagraph": "1.5.", "RuleText": "The use of external programs, scripts and cheats is not tolerated and is punished with a permanent exclusion."}]}], "DisplayServerSettingsInServerInfoTab": 1, "SettingCategories": [{"CategoryName": "Base-Building Settings", "Settings": [{"SettingTitle": "Expansion.Settings.BaseBuilding.CanCraftVanillaBasebuilding", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.BaseBuilding.CanCraftExpansionBasebuilding", "SettingText": "", "SettingValue": ""}]}, {"CategoryName": "Raid Settings", "Settings": [{"SettingTitle": "Expansion.Settings.Raid.CanRaidSafes", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Raid.SafeExplosionDamageMultiplier", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Raid.SafeProjectileDamageMultiplier", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Raid.ExplosionTime", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Raid.ExplosionDamageMultiplier", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Raid.ProjectileDamageMultiplier", "SettingText": "", "SettingValue": ""}]}, {"CategoryName": "Territory Settings", "Settings": [{"SettingTitle": "Expansion.Settings.Territory.TerritorySize", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Territory.UseWholeMapForInviteList", "SettingText": "", "SettingValue": ""}]}, {"CategoryName": "Map Settings", "Settings": [{"SettingTitle": "Expansion.Settings.Map.NeedGPSItemForKeyBinding", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Map.NeedMapItemForKeyBinding", "SettingText": "", "SettingValue": ""}]}, {"CategoryName": "Party Settings", "Settings": [{"SettingTitle": "Expansion.Settings.Party.MaxMembersInParty", "SettingText": "", "SettingValue": ""}, {"SettingTitle": "Expansion.Settings.Party.UseWholeMapForInviteList", "SettingText": "", "SettingValue": ""}]}], "Links": [{"Name": "Homepage", "URL": "https://www.google.com/", "IconName": "Homepage", "IconColor": -14473430}, {"Name": "<PERSON><PERSON><PERSON>", "URL": "https://www.google.com/", "IconName": "Forums", "IconColor": -14473430}, {"Name": "Discord", "URL": "https://www.google.com/", "IconName": "Discord", "IconColor": -9270822}, {"Name": "Patreon", "URL": "https://www.patreon.com/dayzexpansion", "IconName": "Patreon", "IconColor": -432044}, {"Name": "Steam", "URL": "https://steamcommunity.com/sharedfiles/filedetails/?id=2116151222", "IconName": "Steam", "IconColor": -14006434}, {"Name": "Reddit", "URL": "https://www.reddit.com/r/ExpansionProject/", "IconName": "Reddit", "IconColor": -12386303}, {"Name": "GitHub", "URL": "https://github.com/salutesh/DayZ-Expansion-Scripts/wiki", "IconName": "GitHub", "IconColor": -16777216}, {"Name": "YouTube", "URL": "https://www.youtube.com/channel/UCZNgSvIEWfru963tQZOAVJg", "IconName": "YouTube", "IconColor": -65536}, {"Name": "Twitter", "URL": "https://twitter.com/DayZExpansion", "IconName": "Twitter", "IconColor": -14835214}], "Descriptions": [{"CategoryName": "General Info", "Descriptions": [{"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}, {"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}, {"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}]}, {"CategoryName": "Mod Info", "Descriptions": [{"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}, {"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}, {"DescriptionText": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet."}]}], "CraftingCategories": [{"CategoryName": "Accessories", "Results": ["armband_", "armband_white", "eyepatch_improvised"]}, {"CategoryName": "Backpacks", "Results": ["courierbag", "furcourierbag", "furimprovisedbag", "improvisedbag", "leathersack_brown"]}, {"CategoryName": "Base-Building", "Results": ["expansionbarbedwirekit", "fencekit", "expansionfloorkit", "expansionhelipadkit", "expansionhescokit", "expansionrampkit", "shelterkit", "expansionstairkit", "territoryflagkit", "expansionwallkit", "watchtowerkit"]}, {"CategoryName": "Camouflage", "Results": ["camonet", "ghillie<PERSON>_tan", "ghillieatt_mossy", "ghillieatt_woodland", "ghillie<PERSON><PERSON>_tan", "ghillie<PERSON>rag_mossy", "ghilliebushrag_woodland", "ghillie<PERSON>_tan", "ghilliehood_mossy", "ghilliehood_woodland", "ghillies<PERSON>_tan", "ghilliesuit_mossy", "ghilliesuit_woodland", "ghilliet<PERSON>_tan", "ghillietop_mossy", "ghillietop_woodland"]}, {"CategoryName": "Cooking", "Results": ["fireplace", "firewood", "handdrillkit"]}, {"CategoryName": "Fishing", "Results": ["bait", "bonebait", "bonehook", "improvisedfishingrod"]}, {"CategoryName": "Food", "Results": ["carpfilletmeat", "mackerelfilletmeat", "expansionmilkbottle", "potato", "slicedpumpkin"]}, {"CategoryName": "Horticulture", "Results": ["pepperseeds", "pumpkinseeds", "tomatoseeds", "zucchiniseeds"]}, {"CategoryName": "Lights", "Results": ["longtorch", "torch"]}, {"CategoryName": "Medical Supplies", "Results": ["bloodbagiv", "salinebagiv", "splint"]}, {"CategoryName": "Melee Weapons", "Results": ["nailedbaseballbat", "stoneknife"]}, {"CategoryName": "Storage", "Results": ["woodencrate"]}, {"CategoryName": "Supplies", "Results": ["boarpelt", "burlapsack", "burlapstrip", "longwoodenstick", "expansionlumber1", "expansionlumber1_5", "expansionlumber3", "nails", "netting", "rag", "rope", "sharpwoodenstick", "smallstone", "tannedleather", "woodenplank", "woodenstick"]}, {"CategoryName": "Weapon Modifications", "Results": ["sawedoffizh18shotgun"]}, {"CategoryName": "Weapon Attachments", "Results": ["improvisedsuppressor"]}], "EnableCraftingRecipesTab": 1}