=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-16_2-2-50
====================================================================================
2:2:50 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
2:2:50 | [PermissionManager] Perm (FreeCamera) has been registered!
2:2:50 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
2:2:50 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
2:2:50 | [PermissionManager] Perm (MenuServerManager) has been registered!
2:2:50 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
2:2:50 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
2:2:50 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
2:2:50 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
2:2:50 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
2:2:50 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
2:2:50 | [PermissionManager] Perm (MenuObjectManager) has been registered!
2:2:50 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
2:2:50 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
2:2:50 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
2:2:50 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
2:2:50 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
2:2:50 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
2:2:50 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
2:2:50 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
2:2:50 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
2:2:50 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
2:2:50 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
2:2:50 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
2:2:50 | [PermissionManager] Perm (MenuBansManager) has been registered!
2:2:50 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
2:2:50 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
2:2:50 | [PermissionManager] Perm (MenuWebHooks) has been registered!
2:2:50 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
2:2:50 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
2:2:50 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
2:2:50 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
2:2:50 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
2:2:50 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
2:2:50 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
2:2:50 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
2:2:50 | [PermissionManager] Adding Super Admin (76561199239979485)
2:2:50 | [PermissionManager] Adding Super Admin (76561197999250250)
2:2:50 | [PermissionManager] Adding Super Admin (76561198153347717)
2:2:50 | [PermissionManager] Loaded UserGroups.json
2:2:50 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
2:2:50 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
2:2:50 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
2:2:50 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
2:2:50 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
2:2:50 | Building Sets Loaded: 0
2:2:50 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
2:3:47 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
2:15:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3796.347656, 1106.664551, 1750.779785>)
2:15:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3809.006592, 1113.749756, 1738.356079>)
2:15:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3829.482422, 1128.121582, 1718.904419>)
2:15:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3832.659424, 1139.746704, 1706.721802>)
2:16:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3827.325684, 1151.591431, 1697.046753>)
2:16:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3821.415283, 1160.848267, 1689.152832>)
2:16:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3817.802734, 1169.279419, 1682.129028>)
2:16:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3815.554688, 1188.426270, 1661.875732>)
2:16:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3812.470947, 1194.344360, 1651.347290>)
2:16:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3755.948730, 1219.962158, 1642.609985>)
2:16:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3750.164551, 1222.430542, 1639.081299>)
2:16:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3738.832520, 1226.375977, 1633.339233>)
2:16:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3691.354492, 1237.915161, 1612.004150>)
2:16:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3685.536377, 1241.344849, 1609.689575>)
2:16:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3678.871338, 1245.043335, 1608.054199>)
2:16:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3670.442627, 1249.110352, 1607.399780>)
2:16:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3662.880371, 1252.880615, 1606.981323>)
2:16:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3655.653320, 1256.667236, 1606.612915>)
2:16:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3647.844971, 1260.636230, 1606.165405>)
2:16:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3640.340576, 1264.116089, 1605.302490>)
2:16:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3632.578613, 1267.411377, 1604.263428>)
2:16:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3625.887451, 1270.048462, 1603.151123>)
2:16:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3620.260498, 1271.991211, 1602.087402>)
2:16:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3613.780273, 1273.574585, 1600.606323>)
2:16:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3607.279785, 1274.749512, 1598.940674>)
2:16:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3600.567627, 1275.702759, 1597.177124>)
2:16:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3589.302734, 1276.421021, 1593.973633>)
2:17:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3094.191406, 1290.125977, 2205.909668>)
2:17:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3087.063477, 1295.871826, 2220.967529>)
2:17:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3082.659668, 1299.714600, 2229.550293>)
2:17:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3078.312744, 1303.419556, 2237.669922>)
2:17:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3073.005859, 1307.411377, 2247.365479>)
2:17:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3068.924805, 1309.943359, 2254.093994>)
2:17:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3064.819092, 1311.665527, 2260.109863>)
2:17:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3129.380615, 1285.053223, 2224.374756>)
2:17:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3574.811523, 1095.286377, 1804.736206>)
2:18:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3633.412354, 1094.099976, 1796.213379>)
2:18:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3644.178955, 1096.060425, 1794.833984>)
2:18:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3705.560547, 1096.791504, 1798.425049>)
2:21:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Hatchet)
2:23:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MouthRag)
2:26:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamoMediumTent)
2:29:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MD_CamonetShelter_RE_Kit)
2:32:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3758.147217, 1100.840088, 1797.139160>)
2:46:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3748.652832, 1099.647949, 1780.011353>)
2:50:52 | [WeatherManager] Send Data Count is 0
2:57:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3973.942383, 918.968506, 2245.152344>)
2:57:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4114.141602, 930.856750, 2179.974121>)
2:57:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4075.366943, 925.058350, 2204.663330>)
2:57:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4080.220215, 918.290833, 2202.714844>)
2:57:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4073.610596, 918.238770, 2196.493652>)
2:57:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4076.384766, 921.348938, 2205.519043>)
2:57:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4073.889404, 920.082397, 2213.900879>)
2:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4077.729980, 921.843628, 2208.809082>)
2:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4078.420654, 920.640137, 2206.159668>)
2:57:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4081.507813, 918.817322, 2202.372803>)
2:57:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4083.350098, 921.851563, 2190.109131>)
2:58:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4122.469727, 930.468445, 2195.635254>)
3:3:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4088.175537, 916.274292, 2414.454102>)
3:6:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4098.859375, 918.009277, 2409.142578>)
3:6:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4113.802246, 915.624207, 2372.874512>)
3:7:2 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
3:7:27 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
