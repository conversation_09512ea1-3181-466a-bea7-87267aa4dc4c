=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-13_2-2-44
====================================================================================
2:2:44 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
2:2:44 | [PermissionManager] Perm (FreeCamera) has been registered!
2:2:44 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
2:2:44 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
2:2:44 | [PermissionManager] Perm (MenuServerManager) has been registered!
2:2:44 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
2:2:44 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
2:2:44 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
2:2:44 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
2:2:44 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
2:2:44 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
2:2:44 | [PermissionManager] Perm (MenuObjectManager) has been registered!
2:2:44 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
2:2:44 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
2:2:44 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
2:2:44 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
2:2:44 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
2:2:44 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
2:2:44 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
2:2:44 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
2:2:44 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
2:2:44 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
2:2:44 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
2:2:44 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
2:2:44 | [PermissionManager] Perm (MenuBansManager) has been registered!
2:2:44 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
2:2:44 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
2:2:44 | [PermissionManager] Perm (MenuWebHooks) has been registered!
2:2:44 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
2:2:44 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
2:2:44 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
2:2:44 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
2:2:44 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
2:2:44 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
2:2:44 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
2:2:44 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
2:2:44 | [PermissionManager] Adding Super Admin (76561199239979485)
2:2:44 | [PermissionManager] Adding Super Admin (76561197999250250)
2:2:44 | [PermissionManager] Adding Super Admin (76561198153347717)
2:2:44 | [PermissionManager] Loaded UserGroups.json
2:2:44 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
2:2:44 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
2:2:44 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
2:2:44 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
2:2:44 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
2:2:44 | Building Sets Loaded: 0
2:2:44 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
2:5:36 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
2:9:52 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<8672.929688, 172.369202, 3089.620117>)
2:9:52 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=8672.93,0,3089.62)
2:19:14 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7700.759766, 202.540604, 4534.520020>)
2:19:14 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7700.76,0,4534.52)
2:28:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TerritoryFlagKit)
2:30:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "CrSk_Land_Rover_Defender_110_Green" (pos=<7545.253418, 214.759583, 4697.297852>)
2:35:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (artillery_tractor_kung)
2:35:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionCarKey" (pos=<7526.957031, 214.740189, 4714.174316>)
2:36:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionCarKey)
2:41:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "artillery_tractor_kung" (pos=<7546.765625, 214.768356, 4698.584961>)
2:41:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionCarKey" (pos=<7550.108887, 214.740189, 4700.549316>)
2:45:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7519.096680, 208.468201, 4969.072754>)
2:46:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (GAZ_66_camo)
2:51:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "GAZ_66_camo" (pos=<7544.782715, 214.786987, 4697.509277>)
2:52:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_mtvr)
2:52:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "rag_mtvr" (pos=<7532.044434, 214.756607, 4712.675781>)
2:53:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_469_Hunter)
2:53:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
2:56:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:56:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:56:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:56:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:56:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:57:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MouthRag)
3:6:18 | [WeatherManager] Send Data Count is 0
3:7:42 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
3:8:7 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
