=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-25_2-2-40
====================================================================================
2:2:40 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
2:2:40 | [PermissionManager] Perm (FreeCamera) has been registered!
2:2:40 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
2:2:40 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
2:2:40 | [PermissionManager] Perm (MenuServerManager) has been registered!
2:2:40 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
2:2:40 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
2:2:40 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
2:2:40 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
2:2:40 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
2:2:40 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
2:2:40 | [PermissionManager] Perm (MenuObjectManager) has been registered!
2:2:40 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
2:2:40 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
2:2:40 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
2:2:40 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
2:2:40 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
2:2:40 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
2:2:40 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
2:2:40 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
2:2:40 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
2:2:40 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
2:2:40 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
2:2:40 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
2:2:40 | [PermissionManager] Perm (MenuBansManager) has been registered!
2:2:40 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
2:2:40 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
2:2:40 | [PermissionManager] Perm (MenuWebHooks) has been registered!
2:2:40 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
2:2:40 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
2:2:40 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
2:2:40 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
2:2:40 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
2:2:40 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
2:2:40 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
2:2:40 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
2:2:40 | [PermissionManager] Adding Super Admin (76561199239979485)
2:2:40 | [PermissionManager] Adding Super Admin (76561197999250250)
2:2:40 | [PermissionManager] Adding Super Admin (76561198153347717)
2:2:40 | [PermissionManager] Loaded UserGroups.json
2:2:40 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
2:2:40 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
2:2:40 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
2:2:40 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
2:2:40 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
2:2:40 | Building Sets Loaded: 0
2:2:40 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
2:5:27 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
2:7:9 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<10349.099609, 128.279999, 914.601013>)
2:7:9 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=10349.1,0,914.601)
2:11:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11373.787109, 256.617584, 1203.711060>)
2:11:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11360.187500, 256.626801, 1209.334839>)
2:12:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11355.987305, 270.410095, 1199.227173>)
2:13:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11377.778320, 256.624207, 1167.207397>)
2:22:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) toggled godmode
2:23:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
2:23:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) just updated a health stat on (steamid=76561197999250250)
2:29:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
2:30:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8803.831055, 222.079620, 6263.836426>)
2:31:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8858.028320, 219.424515, 6242.980469>)
2:33:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8775.755859, 220.388779, 6458.141602>)
2:36:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<10498.280273, 344.325562, 7209.754883>)
2:37:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<10490.330078, 340.453979, 7172.422852>)
2:38:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<10500.591797, 340.573090, 7152.968262>)
2:39:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<10428.787109, 337.261322, 7194.245605>)
2:46:53 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<12057.299805, 691.458923, 4979.560059>)
2:46:53 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=12057.3,0,4979.56)
2:47:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<12065.463867, 692.815491, 4981.496094>)
2:50:29 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7877.330078, 256.577393, 11446.099609>)
2:50:29 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7877.33,0,11446.1)
2:53:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7786.031250, 303.017517, 11160.767578>)
2:53:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7785.482910, 306.087433, 11160.918945>)
2:53:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7769.766113, 356.217957, 11102.326172>)
2:53:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7768.262207, 367.597717, 11089.396484>)
2:53:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7765.935547, 374.243286, 11081.500977>)
2:53:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7763.313477, 381.150787, 11072.877930>)
2:53:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7760.589844, 388.369171, 11063.298828>)
2:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7758.593262, 393.395660, 11056.030273>)
2:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7756.392578, 398.819733, 11047.495117>)
2:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7754.030273, 404.313507, 11037.947266>)
2:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7751.468262, 409.892334, 11027.322266>)
2:53:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7751.953125, 413.193268, 11020.452148>)
2:53:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7753.792480, 415.665039, 11014.793945>)
2:53:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7756.321289, 417.811005, 11009.483398>)
2:53:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7759.740234, 419.840668, 11003.777344>)
2:53:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7765.196289, 421.906189, 10996.679688>)
2:53:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7771.671387, 423.925354, 10988.246094>)
2:53:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7779.797363, 425.811920, 10977.736328>)
2:53:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7787.210938, 426.951294, 10968.475586>)
2:53:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7795.229980, 427.302429, 10961.098633>)
2:53:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7816.255371, 423.896637, 10973.207031>)
2:54:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7825.574707, 415.507507, 10999.755859>)
2:54:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7829.178223, 398.286041, 11034.492188>)
2:54:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7791.338867, 266.624573, 11229.418945>)
2:54:34 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7430.799805, 586.385010, 10611.799805>)
2:54:34 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7430.8,0,10611.8)
2:56:40 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7425.290039, 586.756836, 10613.200195>)
2:56:40 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7425.29,0,10613.2)
2:57:7 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7808.770020, 256.683960, 11440.700195>)
2:57:7 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7808.77,0,11440.7)
2:59:20 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
2:59:40 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
