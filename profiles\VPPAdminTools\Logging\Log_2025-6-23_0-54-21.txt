=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-23_0-54-21
====================================================================================
0:54:21 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
0:54:21 | [PermissionManager] Perm (FreeCamera) has been registered!
0:54:21 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
0:54:21 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
0:54:21 | [PermissionManager] Perm (MenuServerManager) has been registered!
0:54:21 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
0:54:21 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
0:54:21 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
0:54:21 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
0:54:21 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
0:54:21 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
0:54:21 | [PermissionManager] Perm (MenuObjectManager) has been registered!
0:54:21 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
0:54:21 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
0:54:21 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
0:54:21 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
0:54:21 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
0:54:21 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
0:54:21 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
0:54:21 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
0:54:21 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
0:54:21 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
0:54:21 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
0:54:21 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
0:54:21 | [PermissionManager] Perm (MenuBansManager) has been registered!
0:54:21 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
0:54:21 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
0:54:21 | [PermissionManager] Perm (MenuWebHooks) has been registered!
0:54:21 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
0:54:21 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
0:54:21 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
0:54:21 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
0:54:21 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
0:54:21 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
0:54:21 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
0:54:21 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
0:54:21 | [PermissionManager] Adding Super Admin (76561199239979485)
0:54:21 | [PermissionManager] Adding Super Admin (76561197999250250)
0:54:21 | [PermissionManager] Adding Super Admin (76561198153347717)
0:54:21 | [PermissionManager] Loaded UserGroups.json
0:54:21 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
0:54:21 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
0:54:21 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
0:54:21 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
0:54:21 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
0:54:21 | Building Sets Loaded: 0
0:54:21 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
0:55:15 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
0:55:25 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
0:55:29 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
0:55:33 | "BNeaveill99" (steamid=76561198153347717) deleted object "FishingRod" (pos=<4155.416504, 914.838074, 2300.021973>)
0:55:38 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4189.013672, 915.836975, 2350.025146>)
0:56:14 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (FishingRod)
1:3:37 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Worm)
1:3:37 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Worm)
1:5:34 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
1:6:34 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Tripod)
1:6:34 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Tripod)
1:6:44 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (CookingStand)
1:7:27 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Pot)
1:9:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (gaz_51)
1:9:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "gaz_51" (pos=<4191.179688, 915.957581, 2340.231934>)
1:9:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Gaz_53RF)
1:10:50 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (CombatKnife)
1:11:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Gaz_53RF" (pos=<4191.691895, 916.035522, 2340.160645>)
1:12:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_kung)
1:12:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "kamaz_4310RUS_kung" (pos=<4194.957031, 916.457825, 2337.994141>)
1:12:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_blue)
1:13:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "kamaz_4310RUS_blue" (pos=<4195.507813, 916.533691, 2339.532715>)
1:13:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kraz_255B)
1:13:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "kraz_255B" (pos=<4195.592285, 916.533386, 2340.778320>)
1:14:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_mtvr)
1:21:13 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
1:22:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
1:25:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted objects using ESP
1:25:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4027.145752, 917.783081, 2172.920410>)
1:25:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4026.562744, 919.789490, 2173.125977>)
1:25:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4025.530273, 920.053040, 2172.875977>)
1:25:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4025.251953, 919.536072, 2173.041016>)
1:26:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4022.006836, 919.260376, 2172.237549>)
1:26:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4010.245605, 917.931824, 2165.734375>)
1:26:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3998.390625, 918.597351, 2161.230225>)
1:26:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3983.428223, 918.028076, 2164.710938>)
1:26:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (URAL_375_1964)
1:27:59 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3975.867676, 915.964539, 2176.527832>)
1:28:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3975.531006, 917.355713, 2170.859863>)
1:28:14 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3970.407227, 917.393860, 2174.795898>)
1:28:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3964.655518, 918.015808, 2179.117920>)
1:28:16 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3958.898438, 919.266907, 2183.368896>)
1:28:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3969.822998, 916.978027, 2177.283936>)
1:29:2 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4003.383057, 906.289917, 2221.052490>)
1:29:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4092.018799, 910.354492, 2317.918457>)
1:29:13 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4255.764648, 936.445129, 2355.575195>)
1:29:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4258.358398, 937.208984, 2352.394531>)
1:29:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4262.745605, 935.200745, 2352.193359>)
1:29:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4267.365234, 926.692688, 2352.157227>)
1:29:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4272.028809, 917.640076, 2352.342285>)
1:29:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4276.535645, 909.125366, 2353.068115>)
1:29:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4279.892090, 899.323608, 2353.677246>)
1:29:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4281.666504, 889.089233, 2353.947510>)
1:29:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4284.299316, 878.675415, 2354.443848>)
1:29:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4288.527832, 865.668396, 2355.218018>)
1:29:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4288.655273, 857.638794, 2355.229492>)
1:29:36 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<3971.850098, 925.789490, 2139.729980>)
1:29:36 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=3971.85,0,2139.73)
1:30:48 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3993.950928, 907.819092, 2224.515137>)
1:32:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4157.114746, 917.352844, 2350.584961>)
1:32:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4157.116211, 916.657654, 2350.585449>)
1:32:29 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<4101.680176, 906.902344, 2288.669922>)
1:32:29 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=4101.68,0,2288.67)
1:33:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "URAL_375_1964" (pos=<4195.519531, 916.507935, 2343.055420>)
1:34:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ZAZ_965_bk)
1:35:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
1:39:13 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4198.609375, 917.008789, 2338.374756>)
1:40:5 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<4105.209961, 906.406128, 2282.270020>)
1:40:5 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=4105.21,0,2282.27)
1:40:13 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4118.336426, 915.299500, 2246.570068>)
1:40:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4213.070313, 919.283813, 2346.803711>)
1:40:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4120.654785, 917.900696, 2246.065674>)
1:40:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4119.976074, 918.948669, 2248.542480>)
1:40:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4119.901367, 917.424133, 2248.853760>)
1:40:22 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4119.917969, 917.247009, 2248.874512>)
1:40:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4120.110840, 917.410400, 2249.116211>)
1:40:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4124.294922, 918.574280, 2251.294922>)
1:40:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4127.755371, 917.540344, 2251.561279>)
1:40:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4131.820313, 920.304504, 2251.201904>)
1:40:26 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4136.146973, 921.249878, 2250.872314>)
1:40:28 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4136.396484, 922.704102, 2246.674561>)
1:40:29 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4137.007324, 922.710083, 2245.739990>)
1:40:30 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4137.143066, 922.901245, 2245.885986>)
1:40:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ZAZ_965_bk" (pos=<4185.523926, 915.237244, 2337.389648>)
1:40:31 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4137.080566, 923.065918, 2246.199707>)
1:40:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4136.743652, 922.953674, 2246.318115>)
1:40:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4135.909180, 922.984070, 2246.265137>)
1:40:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4132.478516, 923.205994, 2245.852539>)
1:40:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4132.154297, 922.699280, 2243.081055>)
1:40:37 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4088.633545, 907.791077, 2299.581055>)
1:40:45 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4179.331543, 914.682922, 2338.158936>)
1:40:47 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4193.250488, 916.181274, 2342.463379>)
1:40:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ZIL_130_green)
1:41:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4190.582520, 915.814270, 2339.682373>)
1:41:2 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4191.668945, 915.995422, 2336.821533>)
1:41:3 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4192.952148, 916.237000, 2332.380859>)
1:41:30 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Pliers)
1:41:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ZIL_130_green" (pos=<4196.008301, 916.575195, 2342.332764>)
1:42:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_469_army)
1:44:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "UAZ_469_army" (pos=<4196.733398, 916.703369, 2341.254395>)
1:45:19 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (MAZ_509)
1:45:23 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (MAZ_509)
1:46:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4159.835938, 924.722900, 2288.285156>)
1:48:51 | "BNeaveill99" (steamid=76561198153347717) deleted object "MAZ_509" (pos=<4189.241699, 915.820740, 2349.359863>)
1:49:3 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (rag_zil_130)
1:52:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4170.111328, 916.557007, 2346.404297>)
1:53:31 | "BNeaveill99" (steamid=76561198153347717) deleted object "rag_zil_130" (pos=<4194.029297, 916.570068, 2362.420654>)
1:53:49 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (kamaz_4310RUS_blue)
1:55:21 | "BNeaveill99" (steamid=76561198153347717) deleted object "kamaz_4310RUS_blue" (pos=<4183.127930, 915.029785, 2339.114014>)
1:55:33 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
1:55:35 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
1:55:56 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
1:56:16 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
