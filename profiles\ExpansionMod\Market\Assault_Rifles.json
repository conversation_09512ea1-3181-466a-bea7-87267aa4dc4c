{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_ASSAULT_RIFLES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "fal", "MaxPriceThreshold": 10030, "MinPriceThreshold": 6020, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["fal_oebttstck", "mag_fal_20rnd"], "Variants": []}, {"ClassName": "akm", "MaxPriceThreshold": 11190, "MinPriceThreshold": 6715, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ak_woodbttstck", "ak_woodhndgrd", "mag_akm_30rnd"], "Variants": []}, {"ClassName": "ak101", "MaxPriceThreshold": 4550, "MinPriceThreshold": 2730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ak_plasticbttstck", "ak_plastichndgrd", "mag_ak101_30rnd"], "Variants": []}, {"ClassName": "ak74", "MaxPriceThreshold": 9900, "MinPriceThreshold": 5940, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["ak74_woodbttstck", "ak74_hndgrd", "mag_ak74_30rnd"], "Variants": []}, {"ClassName": "m4a1", "MaxPriceThreshold": 2040, "MinPriceThreshold": 1225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["m4_carryhandleoptic", "mag_stanag_30rnd", "m4_oebttstck", "m4_plastichndgrd"], "Variants": []}, {"ClassName": "m16a2", "MaxPriceThreshold": 4550, "MinPriceThreshold": 2730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_stanag_30rnd"], "Variants": []}, {"ClassName": "famas", "MaxPriceThreshold": 4550, "MinPriceThreshold": 2730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_famas_25rnd"], "Variants": []}, {"ClassName": "aug", "MaxPriceThreshold": 2040, "MinPriceThreshold": 1225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_aug_30rnd"], "Variants": []}, {"ClassName": "augshort", "MaxPriceThreshold": 4550, "MinPriceThreshold": 2730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_aug_30rnd"], "Variants": []}, {"ClassName": "expansion_m16", "MaxPriceThreshold": 4550, "MinPriceThreshold": 2730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_stanag_30rnd"], "Variants": []}, {"ClassName": "expansion_g36", "MaxPriceThreshold": 2040, "MinPriceThreshold": 1225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_expansion_g36_30rnd"], "Variants": []}]}