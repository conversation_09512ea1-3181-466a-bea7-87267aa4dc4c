[{"m_groupName": "Admins", "m_permissionLevel": 1, "m_GroupMembers": [{"m_UserName": "Example User", "m_Steam64ID": "26561198420222028", "m_SessionId": -1}], "m_Permissions": ["DeleteObjectAtCrosshair", "TeleportToCrosshair", "FreeCamera", "RepairVehiclesAtCrosshair", "MenuItemManager", "MenuItemManager:SpawnItem", "MenuItemManager:EditPreset", "MenuItemManager:SpawnPreset", "MenuItemManager:DeletePreset", "MenuItemManager:AddPreset", "MenuServerManager", "ServerManager:RestartServer", "ServerManager:LockServer", "ServerManager:KickAllPlayers", "ServerManager:LoadScripts", "MenuWeatherManager", "WeatherManager:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WeatherManager:ApplyTime", "WeatherManager:SavePreset", "WeatherManager:DeletePreset", "WeatherManager:ApplyPreset", "WeatherManager:ApplyTimePreset", "WeatherManager:SaveTimePreset", "WeatherManager:DeleteTimePreset", "MenuObjectManager", "MenuObjectManager:CreateNewSet", "MenuObjectManager:UpdateSet", "MenuObjectManager:DeleteSet", "MenuObjectManager:EditSet", "MenuPermissionsEditor", "PermissionsEditor:RemoveUser", "PermissionsEditor:AddUser", "PermissionsEditor:CreateUserGroup", "PermissionsEditor:DeleteUserGroup", "PermissionsEditor:ChangePermLevel", "MenuPlayerManager", "PlayerManager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "PlayerManager:BanPlayer", "PlayerManager:KickPlayer", "PlayerManager:HealPlayers", "PlayerManager:SetPlayerStats", "PlayerManager:KillPlayers", "PlayerManager:<PERSON><PERSON><PERSON>", "PlayerManager:SpectatePlayer", "PlayerManager:TeleportToPlayer", "PlayerManager:TeleportPlayerTo", "PlayerManager:SetPlayerInvisible", "PlayerManager:SendMessage", "PlayerManager:GiveUnlimitedAmmo", "PlayerManager:MakePlayerVomit", "PlayerManager:FreezePlayers", "PlayerManager:ChangeScale", "MenuBansManager", "BansManager:UnbanPlayer", "BansManager:UpdateBanDuration", "BansManager:UpdateBanReason", "MenuWebHooks", "MenuWebHooks:Create", "MenuWebHooks:Edit", "MenuWebHooks:Delete", "MenuTeleportManager", "TeleportManager:ViewPlayerPositions", "TeleportManager:TPPlayers", "TeleportManager:TPSelf", "TeleportManager:DeletePreset", "TeleportManager:AddNewPreset", "TeleportManager:EditPreset", "TeleportManager:TeleportEntity", "EspToolsMenu", "EspToolsMenu:DeleteObjects", "EspToolsMenu:PlayerESP", "EspToolsMenu:RestPasscodeFence", "EspToolsMenu:RetriveCodeFromObj", "EspToolsMenu:PlayerMeshEsp", "EspToolsMenu:InstantBaseBuild", "MenuXMLEditor", "MenuCommandsConsole"], "m_ForceSavedName": 0}]