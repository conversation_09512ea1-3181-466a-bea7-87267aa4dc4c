<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>

<spawnabletypes>
	<type name="BBP_Letter_Base">

	</type>
	<type name="BBP_Letter_Large_A">

	</type>
	<type name="BBP_Letter_Large_B">

	</type>
	<type name="BBP_Letter_Large_C">

	</type>
	<type name="BBP_Letter_Large_D">

	</type>
	<type name="BBP_Letter_Large_E">

	</type>
	<type name="BBP_Letter_Large_F">

	</type>
	<type name="BBP_Letter_Large_G">

	</type>
	<type name="BBP_Letter_Large_H">

	</type>
	<type name="BBP_Letter_Large_I">

	</type>
	<type name="BBP_Letter_Large_J">

	</type>
	<type name="BBP_Letter_Large_K">

	</type>
	<type name="BBP_Letter_Large_L">

	</type>
	<type name="BBP_Letter_Large_M">

	</type>
	<type name="BBP_Letter_Large_N">

	</type>
	<type name="BBP_Letter_Large_O">

	</type>
	<type name="BBP_Letter_Large_P">

	</type>
	<type name="BBP_Letter_Large_Q">

	</type>
	<type name="BBP_Letter_Large_R">

	</type>
	<type name="BBP_Letter_Large_S">

	</type>
	<type name="BBP_Letter_Large_T">

	</type>
	<type name="BBP_Letter_Large_U">

	</type>
	<type name="BBP_Letter_Large_V">

	</type>
	<type name="BBP_Letter_Large_W">

	</type>
	<type name="BBP_Letter_Large_X">

	</type>
	<type name="BBP_Letter_Large_Y">

	</type>
	<type name="BBP_Letter_Large_Z">

	</type>
	<type name="BBP_Magnet_A">

	</type>
	<type name="BBP_Magnet_B">

	</type>
	<type name="BBP_Magnet_C">

	</type>
	<type name="BBP_Magnet_D">

	</type>
	<type name="BBP_Magnet_E">

	</type>
	<type name="BBP_Magnet_F">

	</type>
	<type name="BBP_Magnet_G">

	</type>
	<type name="BBP_Magnet_H">

	</type>
	<type name="BBP_Magnet_I">

	</type>
	<type name="BBP_Magnet_J">

	</type>
	<type name="BBP_Magnet_K">

	</type>
	<type name="BBP_Magnet_L">

	</type>
	<type name="BBP_Magnet_M">

	</type>
	<type name="BBP_Magnet_N">

	</type>
	<type name="BBP_Magnet_O">

	</type>
	<type name="BBP_Magnet_P">

	</type>
	<type name="BBP_Magnet_Q">

	</type>
	<type name="BBP_Magnet_R">

	</type>
	<type name="BBP_Magnet_S">

	</type>
	<type name="BBP_Magnet_T">

	</type>
	<type name="BBP_Magnet_U">

	</type>
	<type name="BBP_Magnet_V">

	</type>
	<type name="BBP_Magnet_W">

	</type>
	<type name="BBP_Magnet_X">

	</type>
	<type name="BBP_Magnet_Y">

	</type>
	<type name="BBP_Magnet_Z">

	</type>
	<type name="BBP_MagnetBox">

	</type>
	<type name="BBP_Tape_Measure">

	</type>
	<type name="BBP_Painting">

	</type>
	<type name="BBP_Painting_1">

	</type>
	<type name="BBP_Painting_2">

	</type>
	<type name="BBP_Painting_Landscape">

	</type>
	<type name="BBP_Sun_Shade">

	</type>
	<type name="BBP_Xmas_Tree">

	</type>
	<type name="BBP_XmasLights_9v">

	</type>
	<type name="BBP_XmasLights_Solar">

	</type>
	<type name="BBP_Wood_Storage">

	</type>
	<type name="BBPA_Admin_Hammer">

	</type>
	<type name="BBP_Flashlight">

	</type>
	<type name="BBP_Old_Lamp">

	</type>
	<type name="BBP_Floor_Lamp">

	</type>
	<type name="BBP_Land_Claim_FlagKit">

	</type>
	<type name="BBP_Land_Claím_Pole">

	</type>
	<type name="BBP_Land_Claim_Flag">

	</type>
	<type name="BBP_Admin_Land_Claim_Flag">

	</type>
	<type name="BBP_Golf">

	</type>
	<type name="BBP_Gang_Plug">

	</type>
	<type name="BBP_SolarPlus_Panelkit">

	</type>
	<type name="BBP_SolarPlus">

	</type>
	<type name="BBP_BedKit">

	</type>
	<type name="BBP_Bed_Hologram">

	</type>
	<type name="BBP_Bed">

	</type>
	<type name="BBP_Bedding">

	</type>
	<type name="BBPA_Step_LadderKit">

	</type>
	<type name="BBPA_Step_Ladder_Hologram">

	</type>
	<type name="BBPA_Step_Ladder">

	</type>
	<type name="BBP_Bucket">

	</type>
	<type name="BBP_WellKit">

	</type>
	<type name="BBP_Well_Hologram">

	</type>
	<type name="BBP_Well">

	</type>
	<type name="BBP_Wheelbarrow">

	</type>
	<type name="BBP_SofaKit">

	</type>
	<type name="BBP_Sofa_Hologram">

	</type>
	<type name="BBP_Sofa">

	</type>
	<type name="BBP_Toilet">

	</type>
	<type name="BBP_Dance_FloorKit">

	</type>
	<type name="BBP_Dance_Floor_Hologram">

	</type>
	<type name="BBP_Dance_Floor">

	</type>
	<type name="BBP_Carpet_Roll">

	</type>
	<type name="BBP_Carpet_Base">

	</type>
	<type name="BBP_Carpet_01">

	</type>
	<type name="BBP_Carpet_02">

	</type>
	<type name="BBP_Carpet_03">

	</type>
	<type name="BBP_Carpet_04">

	</type>
	<type name="BBP_Carpet_05">

	</type>
	<type name="BBP_Carpet_06">

	</type>
	<type name="BBP_Carpet_07">

	</type>
	<type name="BBP_Carpet_08">

	</type>
	<type name="BBP_Carpet_09">

	</type>
	<type name="BBP_PhotoFrame_Portrait">

	</type>
	<type name="BBP_PhotoFrame_Landscape">

	</type>
	<type name="BBP_Mesh_Floor_Kit">

	</type>
	<type name="BBP_Mesh_Floor_Hologram">

	</type>
	<type name="BBP_Mesh_Floor">

	</type>
	<type name="BBP_Mortar_Mix">

	</type>
	<type name="BBP_Blueprint_Expanded">

	</type>
</spawnabletypes>