{"m_Version": 12, "DisplayName": "<PERSON><PERSON><PERSON>_Misc_And_Other", "Icon": "Deliver", "Color": "FBFCFEFF", "InitStockPercent": 75, "Items": [{"ClassName": "GCGN_M1Garand_Bayonet", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Kabar", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PEQ1_Laser", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Black", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Blue", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgun<PERSON>ke_Brown", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Green", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Purple", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Red", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Tur", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Shotgunchoke_Yellow", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUUniversalLight", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SKS_Bayonet", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}