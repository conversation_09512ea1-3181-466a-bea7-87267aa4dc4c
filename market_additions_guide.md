# Market Additions Implementation Guide

## Summary
After comprehensive analysis, 506 high-priority items need to be added to market files:
- **263 ExpansionMod items** (20 weapons, 72 flags, 4 attachments, 5 ammo, 162 misc)
- **243 TTC/Mortys_Weapons items** (107 weapons, 53 magazines, 77 attachments, 6 ammo)

## High Priority Actions

### 1. Add TTC Weapons to Mortys_Weapons_Black_Market.json
**File:** `profiles/ExpansionMod/Market/Mortys_Weapons_Black_Market.json`
**Items to add:** 107 weapons

**Sample entries to add before the closing `]`:**
```json
    {
      "ClassName": "TTC_AK12",
      "MaxPriceThreshold": 100000,
      "MinPriceThreshold": 100000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 1,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    },
    {
      "ClassName": "TTC_M16",
      "MaxPriceThreshold": 100000,
      "MinPriceThreshold": 100000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 1,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    },
    {
      "ClassName": "TTC_M16A4",
      "MaxPriceThreshold": 100000,
      "MinPriceThreshold": 100000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 1,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    }
```

### 2. Add TTC Magazines to Mortys_Weapons_Magazines_And_Clips_Black_Market.json
**File:** `profiles/ExpansionMod/Market/Mortys_Weapons_Magazines_And_Clips_Black_Market.json`
**Items to add:** 53 magazines

**Sample entries:**
```json
    {
      "ClassName": "TTC_AK12_Drum_90rnd",
      "MaxPriceThreshold": 5000,
      "MinPriceThreshold": 3000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 5,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    },
    {
      "ClassName": "TTC_556Stanag_30rnd",
      "MaxPriceThreshold": 2000,
      "MinPriceThreshold": 1500,
      "SellPricePercent": -1,
      "MaxStockThreshold": 10,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    }
```

### 3. Add ExpansionMod Flags to Flags.json
**File:** `profiles/ExpansionMod/Market/Flags.json`
**Items to add:** 72 flags

**Sample entries:**
```json
        {
            "ClassName": "Expansion_Flag_Argentina",
            "MaxPriceThreshold": 625,
            "MinPriceThreshold": 375,
            "SellPricePercent": -1.0,
            "MaxStockThreshold": 100,
            "MinStockThreshold": 1,
            "QuantityPercent": -1,
            "SpawnAttachments": [],
            "Variants": []
        },
        {
            "ClassName": "Expansion_Flag_Australia",
            "MaxPriceThreshold": 625,
            "MinPriceThreshold": 375,
            "SellPricePercent": -1.0,
            "MaxStockThreshold": 100,
            "MinStockThreshold": 1,
            "QuantityPercent": -1,
            "SpawnAttachments": [],
            "Variants": []
        }
```

### 4. Add ExpansionMod Weapons to Appropriate Categories

#### Sniper_Rifles.json - Add 3 AWM variants:
```json
    {
      "ClassName": "Expansion_AWM",
      "MaxPriceThreshold": 40000,
      "MinPriceThreshold": 35000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 1,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": [
        "Expansion_AWM_Black",
        "Expansion_AWM_Green"
      ]
    }
```

#### Crossbows.json - Add 12 crossbow variants:
```json
    {
      "ClassName": "Expansion_Crossbow",
      "MaxPriceThreshold": 15000,
      "MinPriceThreshold": 10000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 3,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": [
        "Expansion_Crossbow_Black",
        "Expansion_Crossbow_Blue",
        "Expansion_Crossbow_Brown",
        "Expansion_Crossbow_Clean",
        "Expansion_Crossbow_Green",
        "Expansion_Crossbow_Grey",
        "Expansion_Crossbow_Orange",
        "Expansion_Crossbow_Pink",
        "Expansion_Crossbow_Red",
        "Expansion_Crossbow_White",
        "Expansion_Crossbow_Yellow"
      ]
    }
```

#### Shotguns.json - Add 2 shotguns:
```json
    {
      "ClassName": "Expansion_BenelliM4",
      "MaxPriceThreshold": 25000,
      "MinPriceThreshold": 20000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 2,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    },
    {
      "ClassName": "Expansion_DT11",
      "MaxPriceThreshold": 30000,
      "MinPriceThreshold": 25000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 2,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    }
```

## Medium Priority Actions

### 5. Create New TTC Attachments File
**Create:** `profiles/ExpansionMod/Market/Mortys_Weapons_Attachments_Black_Market.json`
**Items to add:** 77 attachments

**File structure:**
```json
{
  "m_Version": 12,
  "DisplayName": "Mortys Weapons Attachments Black Market",
  "Icon": "Deliver",
  "Color": "FBFCFEFF",
  "IsExchange": 0,
  "InitStockPercent": 75,
  "Items": [
    {
      "ClassName": "TTC_Aimpoint2000",
      "MaxPriceThreshold": 8000,
      "MinPriceThreshold": 5000,
      "SellPricePercent": -1,
      "MaxStockThreshold": 5,
      "MinStockThreshold": 1,
      "QuantityPercent": -1,
      "SpawnAttachments": [],
      "Variants": []
    }
  ]
}
```

### 6. Add ExpansionMod Attachments to Optics.json
**Items to add:** 4 attachments (ANPEQ15 variants, PMII25Optic)

### 7. Add ExpansionMod Ammo to Ammo.json and Ammo_Boxes.json
**Items to add:** 5 ammo items

## Implementation Steps

1. **Backup existing market files** before making changes
2. **Start with high-priority items** (TTC weapons, magazines, flags)
3. **Use consistent pricing** based on existing similar items
4. **Test in-game** after each major addition
5. **Use Variants system** where appropriate for color/style variations

## Pricing Guidelines

- **TTC Weapons:** 40,000-100,000 (match existing TTC pricing)
- **TTC Magazines:** 1,500-5,000 (based on capacity)
- **TTC Attachments:** 3,000-15,000 (based on type)
- **ExpansionMod Flags:** 375-625 (match existing flags)
- **ExpansionMod Weapons:** 10,000-40,000 (based on weapon type)

## Files Requiring Updates

**High Priority:**
- Mortys_Weapons_Black_Market.json (+107 items)
- Mortys_Weapons_Magazines_And_Clips_Black_Market.json (+53 items)
- Flags.json (+72 items)
- Sniper_Rifles.json (+3 items)
- Crossbows.json (+12 items)
- Shotguns.json (+2 items)

**Medium Priority:**
- NEW: Mortys_Weapons_Attachments_Black_Market.json (+77 items)
- Optics.json (+4 items)
- Ammo.json (+4 items)
- Ammo_Boxes.json (+1 item)

This implementation will increase market coverage from 74.7% to approximately 81.4%.
