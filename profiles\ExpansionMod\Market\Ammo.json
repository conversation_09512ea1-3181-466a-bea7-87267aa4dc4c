{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_AMMO", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "ammo_12gapellets", "MaxPriceThreshold": 50, "MinPriceThreshold": 30, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_12garubberslug", "MaxPriceThreshold": 80, "MinPriceThreshold": 45, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_12gaslug", "MaxPriceThreshold": 45, "MinPriceThreshold": 25, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_22", "MaxPriceThreshold": 50, "MinPriceThreshold": 30, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_308win", "MaxPriceThreshold": 65, "MinPriceThreshold": 40, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_308wintracer"]}, {"ClassName": "ammo_308wintracer", "MaxPriceThreshold": 745, "MinPriceThreshold": 445, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_357", "MaxPriceThreshold": 25, "MinPriceThreshold": 15, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_380", "MaxPriceThreshold": 55, "MinPriceThreshold": 35, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_45acp", "MaxPriceThreshold": 120, "MinPriceThreshold": 75, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_545x39", "MaxPriceThreshold": 140, "MinPriceThreshold": 85, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_545x39tracer"]}, {"ClassName": "ammo_545x39tracer", "MaxPriceThreshold": 180, "MinPriceThreshold": 105, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_556x45", "MaxPriceThreshold": 505, "MinPriceThreshold": 305, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_556x45tracer"]}, {"ClassName": "ammo_556x45tracer", "MaxPriceThreshold": 370, "MinPriceThreshold": 225, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_762x39", "MaxPriceThreshold": 150, "MinPriceThreshold": 90, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_762x39tracer"]}, {"ClassName": "ammo_762x39tracer", "MaxPriceThreshold": 710, "MinPriceThreshold": 425, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_762x54", "MaxPriceThreshold": 255, "MinPriceThreshold": 155, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_762x54tracer"]}, {"ClassName": "ammo_762x54tracer", "MaxPriceThreshold": 990, "MinPriceThreshold": 595, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_9x19", "MaxPriceThreshold": 25, "MinPriceThreshold": 15, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_9x39", "MaxPriceThreshold": 190, "MinPriceThreshold": 115, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_9x39ap"]}, {"ClassName": "ammo_9x39ap", "MaxPriceThreshold": 655, "MinPriceThreshold": 390, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_flare", "MaxPriceThreshold": 100, "MinPriceThreshold": 60, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_flareblue", "ammo_flaregreen", "ammo_flarered"]}, {"ClassName": "ammo_huntingbolt", "MaxPriceThreshold": 355, "MinPriceThreshold": 215, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_expansion_m203_smoke_teargas", "MaxPriceThreshold": 900, "MinPriceThreshold": 450, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_expansion_m203_sticky_smoke_white", "MaxPriceThreshold": 600, "MinPriceThreshold": 300, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammo_expansion_m203_sticky_smoke_red", "ammo_expansion_m203_sticky_smoke_green", "ammo_expansion_m203_sticky_smoke_yellow", "ammo_expansion_m203_sticky_smoke_purple"]}, {"ClassName": "ammo_expansion_m203_sticky_smoke_teargas", "MaxPriceThreshold": 900, "MinPriceThreshold": 450, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_expansion_46x30", "MaxPriceThreshold": 5, "MinPriceThreshold": 2, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammo_expansion_338", "MaxPriceThreshold": 5, "MinPriceThreshold": 2, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_ammo_8mm", "MaxPriceThreshold": 5, "MinPriceThreshold": 2, "SellPricePercent": -1, "MaxStockThreshold": 500, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_ammo_taser", "MaxPriceThreshold": 1, "MinPriceThreshold": 1, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}