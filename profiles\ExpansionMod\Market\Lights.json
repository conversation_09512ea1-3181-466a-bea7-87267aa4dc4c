{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_LIGHTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "chemlight_blue", "MaxPriceThreshold": 615, "MinPriceThreshold": 370, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["chemlight_green", "chemlight_red", "chemlight_white", "chemlight_yellow"]}, {"ClassName": "roadflare", "MaxPriceThreshold": 9445, "MinPriceThreshold": 5670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "matchbox", "MaxPriceThreshold": 445, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "fireworkslauncher", "MaxPriceThreshold": 825, "MinPriceThreshold": 495, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "petrollighter", "MaxPriceThreshold": 450, "MinPriceThreshold": 270, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flashlight", "MaxPriceThreshold": 280, "MinPriceThreshold": 170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "xmaslights", "MaxPriceThreshold": 430, "MinPriceThreshold": 260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "portablegaslamp", "MaxPriceThreshold": 1250, "MinPriceThreshold": 750, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mediumgascanister"], "Variants": []}, {"ClassName": "portablegasstove", "MaxPriceThreshold": 605, "MinPriceThreshold": 365, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mediumgascanister"], "Variants": []}, {"ClassName": "headtorch_black", "MaxPriceThreshold": 2955, "MinPriceThreshold": 1770, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": ["headtorch_grey"]}, {"ClassName": "headtorch_grey", "MaxPriceThreshold": 2955, "MinPriceThreshold": 1770, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "spotlight", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "universallight", "MaxPriceThreshold": 4760, "MinPriceThreshold": 2855, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tlrlight", "MaxPriceThreshold": 4760, "MinPriceThreshold": 2855, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}