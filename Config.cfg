hostname = "B&B GHOST BLOOD!";			// Server name
description = "Welcome To B&B GHOST BLOOD";   // Description of the server. Gets displayed to users in the client server browser, max length 255 characters
password = "5678";						// Password to connect to the server
passwordAdmin = "1752lidk";					// Password to become a server admin

enableWhitelist = 0;				// Enable/disable whitelist (value 0-1)
disableBanlist = false;				// Disables the usage of ban.txt (default: false)
disablePrioritylist = false;		// Disables usage of priority.txt (default: false)

maxPlayers = 20;					// Maximum amount of players

verifySignatures = 2;				// Verifies .pbos against .bisign files. (only 2 is supported)

forceSameBuild = 1;					// When enabled, the server will allow the connection only to clients with same the .exe revision as the server (value 0-1)

disableVoN = 0;						// Enable/disable voice over network (value 0-1)
vonCodecQuality = 20;				// Voice over network codec quality, the higher the better (values 0-20)

disable3rdPerson = 0;				// Toggles the 3rd person view for players (value 0-1)
disableCrosshair = 0;				// Toggles the cross-hair (value 0-1)

serverTime = "2025/6/1/12/00";			// Initial in-game time of the server. "SystemTime" means the local time of the machine.
									// Another possibility is to set the time to some value in "YYYY/MM/DD/HH/MM" format, e.g "2015/4/8/17/23".

serverTimeAcceleration = 1;			// Accelerated Time - The numerical value being a multiplier (0.1-64).
									// Thus, in case it is set to 24, time would move 24 times faster than normal. An entire day would pass in one hour.

serverNightTimeAcceleration = 1;	// Accelerated Nigh Time - The numerical value being a multiplier (0.1-64) and also multiplied by serverTimeAcceleration value.
									// Thus, in case it is set to 4 and serverTimeAcceleration is set to 2, night time would move 8 times faster than normal.
									// An entire night would pass in 3 hours.

serverTimePersistent = 0;			// Persistent Time (value 0-1) - The actual server time is saved to storage, so when active, the next server start will use the saved time value.

guaranteedUpdates = 1;				// Communication protocol used with game server (use only number 1)

loginQueueConcurrentPlayers = 5;	// The number of players concurrently processed during the login process.
									// Should prevent massive performance drop during connection when a lot of people are connecting at the same time.

loginQueueMaxPlayers = 500;			// The maximum number of players that can wait in login queue

instanceId = 380881;						// DayZ server instance id, to identify the number of instances per box and their storage folders with persistence files



storageAutoFix = 1;					// Checks if the persistence files are corrupted and replaces corrupted ones with empty ones (value 0-1)

respawnTime = 5;				// Sets the respawn delay (in seconds) before the player is able to get a new character on the server, when the previous one is dead

motd[] = { // Message of the day displayed in the in-game chat

"",
"Welcome To B&B GHOST BLOOD Discord Link https://discord.gg/wSZ7aQTDSV",

""
};
motdInterval = 30;				// Time interval (in seconds) between each message

timeStampFormat = "Short";		// Format for timestamps in the .rpt file (value Full/Short)
logAverageFps = 0;				// Logs the average server FPS (value in seconds), needs to have ''-doLogs'' launch parameter active
logMemory = 0;					// Logs the server memory usage (value in seconds), needs to have the ''-doLogs'' launch parameter active
logPlayers = 0;					// Logs the count of currently connected players (value in seconds), needs to have the ''-doLogs'' launch parameter active
logFile = "server_console.log";	// Saves the server console log to a file in the folder with the other server logs

adminLogPlayerHitsOnly = 0;		// 1 - log player hits only / 0 - log all hits ( animals/infected )
adminLogPlacement = 0;			// 1 - log placement action ( traps, tents )
adminLogBuildActions = 0;		// 1 - log basebuilding actions ( build, dismantle, destroy )
adminLogPlayerList = 0;			// 1 - log periodic player list with position every 5 minutes

disableMultiAccountMitigation = false;	// disables multi account mitigation on consoles when true (default: false)

enableDebugMonitor = 0;			// shows info about the character using a debug window in a corner of the screen (value 0-1)

steamQueryPort = 2492;			// defines Steam query port, should fix the issue with server not being visible in client server browser

allowFilePatching = 1;			// if set to 1 it will enable connection of clients with "-filePatching" launch parameter enabled

simulatedPlayersBatch = 20;		// Set limit of how much players can be simulated per frame (for server performance gain)

multithreadedReplication = 1;	// enables multi-threaded processing of servers replication system
								// number of worker threads is derived by settings of jobsystem in dayzSettings.xml by "maxcores" and "reservedcores" parameters (value 0-1)

// WARNING - Disabled because is causing Unstable Connection kicks after 1.26 update // speedhackDetection = 10;			// enable speedhack detection, values 1-10 (1 strict, 10 benevolent, can be float)

networkRangeClose = 20;			// network bubble distance for spawn of close objects with items in them (f.i. backpacks), set in meters, default value if not set is 20
networkRangeNear = 150;			// network bubble distance for spawn (despawn +10%) of near inventory items objects, set in meters, default value if not set is 150
networkRangeFar = 1000;			// network bubble distance for spawn (despawn +10%) of far objects (other than inventory items), set in meters, default value if not set is 1000
networkRangeDistantEffect = 4000;		// network bubble distance for spawn of effects (currently only sound effects), set in meters, default value if not set is 4000
networkObjectBatchLogSlow = 5;	//Maximum time a bubble can take to iterate in seconds before it is logged to the console
networkObjectBatchEnforceBandwidthLimits = 1;	//Enables a limiter for object creation based on bandwidth statistics
networkObjectBatchUseEstimatedBandwidth = 0;	//Switch between the method behind finding the bandwidth usage of a connection. If set to 0, it will use the total of the actual data sent since the last server frame, and if set to 1, it will use a crude estimation
networkObjectBatchUseDynamicMaximumBandwidth = 1;	//Determines if the bandwidth limit should be a factor of the maximum bandwidth that can be sent or a hard limit. The maximum bandwidth that can be sent fluctuates depending on demand in the system.
networkObjectBatchBandwidthLimit = 0.8;		//The actual limit, could be a [0,1] value or a [1,inf] value depending on networkObjectBatchUseDynamicMaximumBandwidth. See above
networkObjectBatchCompute = 1000;	//Number of objects in the create/destroy lists that are checked in a single server frame
networkObjectBatchSendCreate = 10;	//Maximum number of objects that can be sent for creation
networkObjectBatchSendDelete = 10;	//Maximum number of objects that can be sent for deletion

steamProtocolMaxDataSize = 4096;

defaultVisibility=1375;			// highest terrain render distance on server (if higher than "viewDistance=" in DayZ client profile, clientside parameter applies)
defaultObjectViewDistance=1375;	// highest object render distance on server (if higher than "preferredObjectViewDistance=" in DayZ client profile, clientside parameter applies)

enableCfgGameplayFile = 1; // Enable the use of cfggameplay.json file.
lightingConfig = 0;				// 0 for brighter night, 1 for darker night, 2 for Sakhal-specific lighting - if enableCfgGameplayFile is enabled, this option will be overriden by the WorldsData::lightingConfig value
disablePersonalLight = 1;		// disables personal light for all clients connected to server

disableBaseDamage = 0;			// set to 1 to disable damage/destruction of fence and watchtower
disableContainerDamage = 0;		// set to 1 to disable damage/destruction of tents, barrels, wooden crate and seachest
disableRespawnDialog = 0;		// set to 1 to disable the respawn dialog (new characters will be spawning as random)

pingWarning = 200;				// set to define the ping value from which the initial yellow ping warning is triggered (value in milliseconds)
pingCritical = 250;				// set to define the ping value from which the red ping warning is triggered (value in milliseconds)
MaxPing = 300;					// set to define the ping value from which a player is kicked from the server (value in milliseconds)
serverFpsWarning = 15;			// set to define the server fps value under which the initial server fps warning is triggered (minimum value is 11)

shotValidation = 1;				// 1 enables the validation, 0 disables
clientPort = 2491;              // int value, forces the port the clients connect with

class Missions
{
	class DayZ
	{
		template = "dayzOffline.Bitterroot";	// Mission to load on server startup. <MissionName>.<TerrainName>
	};
};