=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-28_15-28-25
====================================================================================
15:28:25 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
15:28:25 | [PermissionManager] Perm (FreeCamera) has been registered!
15:28:25 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
15:28:25 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
15:28:25 | [PermissionManager] Perm (MenuServerManager) has been registered!
15:28:25 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
15:28:25 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
15:28:25 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
15:28:25 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
15:28:25 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
15:28:25 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
15:28:25 | [PermissionManager] Perm (MenuObjectManager) has been registered!
15:28:25 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
15:28:25 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
15:28:25 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
15:28:25 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
15:28:25 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
15:28:25 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
15:28:25 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
15:28:25 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
15:28:25 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
15:28:25 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
15:28:25 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
15:28:25 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
15:28:25 | [PermissionManager] Perm (MenuBansManager) has been registered!
15:28:25 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
15:28:25 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
15:28:25 | [PermissionManager] Perm (MenuWebHooks) has been registered!
15:28:25 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
15:28:25 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
15:28:25 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
15:28:25 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
15:28:25 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
15:28:25 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
15:28:25 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
15:28:25 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
15:28:25 | [PermissionManager] Adding Super Admin (76561199239979485)
15:28:25 | [PermissionManager] Adding Super Admin (76561197999250250)
15:28:25 | [PermissionManager] Adding Super Admin (76561198153347717)
15:28:25 | [PermissionManager] Loaded UserGroups.json
15:28:25 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
15:28:25 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
15:28:25 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
15:28:25 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
15:28:25 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
15:28:25 | Building Sets Loaded: 0
15:28:25 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
