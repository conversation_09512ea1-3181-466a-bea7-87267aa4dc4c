{"m_Version": 12, "DisplayName": "RedFalcon Flight System Heliz Parts", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "RFFS<PERSON><PERSON>_hoodie", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["<PERSON><PERSON><PERSON><PERSON>_hoodie_black"]}, {"ClassName": "RFFSHeli_PilotGloves", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Flag_RedFalcon", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_hydraulic_fluid", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_hydraulic_hoses", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_wiring_harness", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_igniter_plug", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_aviation_battery", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_avionics_unit", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_aviation_toolbox", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_flight_case", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_flight_case_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_flight_case_blue", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_batterycharger", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_PilotShirt", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_PilotPants", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_PilotVest", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_EC135_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_DriverDoor_BlackCamo"]}, {"ClassName": "RFFSHeli_EC135_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_CodriverDoor_BlackCamo"]}, {"ClassName": "RFFSHeli_EC135_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_Cargo1_BlackCamo"]}, {"ClassName": "RFFSHeli_EC135_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_Cargo2_BlackCamo"]}, {"ClassName": "RFFSHeli_EC135_Cargo3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_Cargo3_BlackCamo"]}, {"ClassName": "RFFSHeli_EC135_Cargo4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_EC135_Cargo4_BlackCamo"]}, {"ClassName": "RFFSHeli_Ka26_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Ka26_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Ka26_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Ka26_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Mi2_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Mi2_<PERSON><PERSON><PERSON>_Hornet", "RFFSHeli_Mi2_DriverDoor_Military"]}, {"ClassName": "RFFSHeli_Mi2_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Mi2_CodriverDoor_Hornet", "RFFSHeli_Mi2_CodriverDoor_Military"]}, {"ClassName": "RFFSHeli_Mi2_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Mi2_Cargo1_Hornet", "RFFSHeli_Mi2_Cargo1_Military"]}, {"ClassName": "RFFSHeli_R22_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_R22_DriverDoor_Black", "RFFSHeli_R22_DriverDoor_Red"]}, {"ClassName": "RFFSHeli_R22_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_R22_CodriverDoor_Black", "RFFSHeli_R22_CodriverDoor_Red"]}, {"ClassName": "RFFSHeli_S76_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_S76_DriverDoor_WoodlandCamo"]}, {"ClassName": "RFFSHeli_S76_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_S76_CodriverDoor_WoodlandCamo"]}, {"ClassName": "RFFSHeli_S76_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_S76_Cargo1_WoodlandCamo"]}, {"ClassName": "RFFSHeli_S76_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_S76_Cargo2_WoodlandCamo"]}, {"ClassName": "RFFSHeli_UH1H_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_UH1H_Combat_DriverDoor"]}, {"ClassName": "RFFSHeli_UH1H_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_UH1H_Combat_CodriverDoor"]}, {"ClassName": "RFFSHeli_UH1H_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_UH1H_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_UH1H_Cargo1a", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_UH1H_Cargo2a", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Apache_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Apache_DriverDoor_Desert", "RFFSHeli_Apache_DriverDoor_Winter"]}, {"ClassName": "RFFSHeli_Apache_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Apache_CodriverDoor_Desert", "RFFSHeli_Apache_CodriverDoor_Winter"]}, {"ClassName": "RFFSHeli_AS350_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_AS350_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_AS350_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_AS350_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_Bell429_DriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_DriverDoor_Medic", "RFFSHeli_Bell429_DriverDoor_Police", "RFFSHeli_Bell429_DriverDoor_Uganda", "RFFSHeli_Bell429_DriverDoor_ZS"]}, {"ClassName": "RFFSHeli_Bell429_CodriverDoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_CodriverDoor_Medic", "RFFSHeli_Bell429_CodriverDoor_Police", "RFFSHeli_Bell429_CodriverDoor_Uganda", "RFFSHeli_Bell429_CodriverDoor_ZS"]}, {"ClassName": "RFFSHeli_Bell429_Cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_Cargo1_Medic", "RFFSHeli_Bell429_Cargo1_Police", "RFFSHeli_Bell429_Cargo1_Uganda", "RFFSHeli_Bell429_Cargo1_ZS"]}, {"ClassName": "RFFSHeli_Bell429_Cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_Cargo2_Medic", "RFFSHeli_Bell429_Cargo2_Police", "RFFSHeli_Bell429_Cargo2_Uganda", "RFFSHeli_Bell429_Cargo2_ZS"]}, {"ClassName": "RFFSHeli_Bell429_Cargo3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_Cargo3_Medic", "RFFSHeli_Bell429_Cargo3_Police", "RFFSHeli_Bell429_Cargo3_Uganda", "RFFSHeli_Bell429_Cargo3_ZS"]}, {"ClassName": "RFFSHeli_Bell429_Cargo4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bell429_Cargo4_Medic", "RFFSHeli_Bell429_Cargo4_Police", "RFFSHeli_Bell429_Cargo4_Uganda", "RFFSHeli_Bell429_Cargo4_ZS"]}, {"ClassName": "RFFSHeli_Blackhawk_DriverDoor", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFS<PERSON>eli_Blackhawk_DriverDoor_Cargo", "RF<PERSON><PERSON><PERSON>_Blackhawk_DriverDoor_blackblackhawk", "RFFS<PERSON>eli_Blackhawk_DriverDoor_Budhawk", "RFFS<PERSON><PERSON>_Blackhawk_DriverDoor_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_CodriverDoor", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_CodriverDoor_Cargo", "RFFSHeli_Blackhawk_CodriverDoor_blackblackhawk", "RFFSHeli_Blackhawk_CodriverDoor_Budhawk", "RFFSHeli_Blackhawk_CodriverDoor_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo1", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo1_Cargo", "RFFS<PERSON>eli_Blackhawk_Cargo1_blackblackhawk", "RFFSHeli_Blackhawk_Cargo1_Budhawk", "RFFSHeli_Blackhawk_Cargo1_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo1a", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo1a_Cargo", "RFFSHeli_Blackhawk_Cargo1a_blackblackhawk", "RFFSHeli_Blackhawk_Cargo1a_Budhawk", "RFFSHeli_Blackhawk_Cargo1a_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo2", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo2_Cargo", "RFFS<PERSON>eli_Blackhawk_Cargo2_blackblackhawk", "RFFSHeli_Blackhawk_Cargo2_Budhawk", "RFFSHeli_Blackhawk_Cargo2_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo2a", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo2a_Cargo", "RFFSHeli_Blackhawk_Cargo2a_blackblackhawk", "RFFSHeli_Blackhawk_Cargo2a_Budhawk", "RFFSHeli_Blackhawk_Cargo2a_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo3", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo3_Cargo", "RFFS<PERSON>eli_Blackhawk_Cargo3_blackblackhawk", "RFFSHeli_Blackhawk_Cargo3_Budhawk", "RFFSHeli_Blackhawk_Cargo3_Woodland"]}, {"ClassName": "RFFSHeli_Blackhawk_Cargo4", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Blackhawk_Cargo4_Cargo", "RFFS<PERSON>eli_Blackhawk_Cargo4_blackblackhawk", "RFFSHeli_Blackhawk_Cargo4_Budhawk", "RFFSHeli_Blackhawk_Cargo4_Woodland"]}, {"ClassName": "RFFSHeli_Bo105m_DriverDoor", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_DriverDoor_blackcamo"]}, {"ClassName": "RFFSHeli_Bo105m_CodriverDoor", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_CodriverDoor_blackcamo"]}, {"ClassName": "RFFSHeli_Bo105m_Cargo1", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_Cargo1_blackcamo"]}, {"ClassName": "RFFSHeli_Bo105m_Cargo2", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_DriverDoor_blackcamo"]}, {"ClassName": "RFFSHeli_Bo105m_Cargo3", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_Cargo3_blackcamo"]}, {"ClassName": "RFFSHeli_Bo105m_Cargo4", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["RFFSHeli_Bo105m_Cargo4_blackcamo"]}, {"ClassName": "RFFSHeli_CH47_Cargo1", "MaxPriceThreshold": 1300000, "MinPriceThreshold": 1300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_CH47_Cargo2", "MaxPriceThreshold": 1300000, "MinPriceThreshold": 1300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_CH53e_Cargo1", "MaxPriceThreshold": 1300000, "MinPriceThreshold": 1300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "RFFSHeli_CH53e_Cargo2", "MaxPriceThreshold": 1300000, "MinPriceThreshold": 1300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}