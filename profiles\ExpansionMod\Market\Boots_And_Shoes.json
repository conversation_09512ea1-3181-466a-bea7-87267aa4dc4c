{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_BOOTS & #STR_EXPANSION_MARKET_CATEGORY_SHOES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "athleticshoes_blue", "MaxPriceThreshold": 405, "MinPriceThreshold": 245, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["<PERSON><PERSON><PERSON>_brown", "athleticshoes_grey", "athletics<PERSON>s_black", "athleticshoes_green"]}, {"ClassName": "joggingshoes_black", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["joggingshoes_blue", "joggingshoes_red", "joggingshoes_violet", "joggingshoes_white"]}, {"ClassName": "sneakers_green", "MaxPriceThreshold": 375, "MinPriceThreshold": 225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["sneakers_red", "sneakers_white", "sneakers_black", "sneakers_gray"]}, {"ClassName": "dressshoes_white", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["dressshoes_beige", "dressshoes_black", "dressshoes_brown", "dressshoes_sunburst"]}, {"ClassName": "hikingbootslow_blue", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hikingbootslow_grey", "hikingbootslow_beige", "hikingbootslow_black"]}, {"ClassName": "hikingbootslow_grey", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hikingbootslow_beige", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hikingbootslow_black", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "workingboots_yellow", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["workingboots_grey", "workingboots_brown", "workingboots_beige", "workingboots_green"]}, {"ClassName": "workingboots_grey", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "workingboots_brown", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "workingboots_beige", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "workingboots_green", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hikingboots_brown", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hikingboots_black"]}, {"ClassName": "hikingboots_black", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "combatboots_beige", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["combatboots_black", "combatboots_brown", "combatboots_green", "combatboots_grey"]}, {"ClassName": "jungleboots_beige", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["jungleboots_black", "jungleboots_brown", "jungleboots_green", "jungleboots_olive"]}, {"ClassName": "wellies_black", "MaxPriceThreshold": 470, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["wellies_brown", "wellies_grey", "wellies_green"]}, {"ClassName": "ttskoboots", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryboots_redpunk", "MaxPriceThreshold": 470, "MinPriceThreshold": 285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["militaryboots_bluerock", "militaryboots_beige", "militaryboots_black", "militaryboots_brown"]}, {"ClassName": "militaryboots_bluerock", "MaxPriceThreshold": 470, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryboots_beige", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryboots_black", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryboots_brown", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "nbcbootsgray", "MaxPriceThreshold": 13165, "MinPriceThreshold": 7900, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["nbcbootsyellow", "nbcbootswhite"]}, {"ClassName": "medievalboots", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}