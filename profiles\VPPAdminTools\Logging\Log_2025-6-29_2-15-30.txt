=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-29_2-15-30
====================================================================================
2:15:30 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
2:15:30 | [PermissionManager] Perm (FreeCamera) has been registered!
2:15:30 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
2:15:30 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
2:15:30 | [PermissionManager] Perm (MenuServerManager) has been registered!
2:15:30 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
2:15:30 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
2:15:30 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
2:15:30 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
2:15:30 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
2:15:30 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
2:15:30 | [PermissionManager] Perm (MenuObjectManager) has been registered!
2:15:30 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
2:15:30 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
2:15:30 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
2:15:30 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
2:15:30 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
2:15:30 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
2:15:30 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
2:15:30 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
2:15:30 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
2:15:30 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
2:15:30 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
2:15:30 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
2:15:30 | [PermissionManager] Perm (MenuBansManager) has been registered!
2:15:30 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
2:15:30 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
2:15:30 | [PermissionManager] Perm (MenuWebHooks) has been registered!
2:15:30 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
2:15:30 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
2:15:30 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
2:15:30 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
2:15:30 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
2:15:30 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
2:15:30 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
2:15:30 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
2:15:30 | [PermissionManager] Adding Super Admin (76561199239979485)
2:15:30 | [PermissionManager] Adding Super Admin (76561197999250250)
2:15:30 | [PermissionManager] Adding Super Admin (76561198153347717)
2:15:30 | [PermissionManager] Loaded UserGroups.json
2:15:30 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
2:15:30 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
2:15:30 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
2:15:30 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
2:15:30 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
2:15:30 | Building Sets Loaded: 0
2:15:30 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
2:22:8 | Player "Survivor" (steamId=76561199239979485) connected to server!
2:22:44 | Player "Survivor" (steamId=76561199239979485) initiated disconnect process...
2:23:4 | Player "Survivor" (steamId=76561199239979485) disconnected from server.
3:35:26 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
3:39:1 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<5558.589844, 204.367615, 5998.379883>)
3:39:1 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=5558.59,0,5998.38)
3:44:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6139.530273, 208.896912, 5733.961914>)
3:44:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6122.660156, 208.736450, 5729.221191>)
3:44:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6113.075684, 208.720123, 5718.969238>)
3:44:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6107.095215, 208.720016, 5708.482910>)
3:44:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6108.200684, 208.723160, 5698.232422>)
3:45:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
3:59:11 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1045.569946, 280.131683, 7563.240234>)
3:59:11 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1045.57,0,7563.24)
4:0:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<927.868347, 294.304962, 7541.299805>)
4:0:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<913.627930, 294.687073, 7543.299805>)
4:0:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<901.038269, 294.687073, 7543.993652>)
4:2:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
4:9:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Mag_9x19_60Rnd)
4:11:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:11:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "TTC_Mag_M9_Custom_20Rnd" (pos=<827.593750, 291.327026, 7575.142578>)
4:11:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "TTC_Mag_M9_Custom_20Rnd" (pos=<827.593750, 291.327026, 7575.142578>)
4:11:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "TTC_Mag_M9_Custom_20Rnd" (pos=<827.593750, 291.327026, 7575.142578>)
4:11:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "TTC_Mag_M9_Custom_20Rnd" (pos=<827.593750, 291.327026, 7575.142578>)
4:11:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "TTC_Mag_M9_Custom_20Rnd" (pos=<827.593750, 291.327026, 7575.142578>)
4:12:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Mag_Expansion_M9_15Rnd)
4:12:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SN_M9_Mag)
4:13:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Mag_Expansion_M9_15Rnd)
4:19:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TTC_Mag_M9_Custom_20Rnd)
4:26:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_M100)
4:27:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Paragon_M100" (pos=<849.414795, 291.344330, 7575.098145>)
4:30:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SNAFU_TP82S)
4:31:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "SNAFU_TP82S" (pos=<849.452087, 291.338379, 7574.837402>)
4:40:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SNAFU_CALI_GUN)
4:40:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "SNAFU_CALI_GUN" (pos=<849.190186, 291.341461, 7575.694824>)
4:50:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "LongHorn" (pos=<2496.052734, 249.885666, 7133.631348>)
4:52:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Red9)
4:52:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Red9" (pos=<2496.000977, 249.870728, 7134.724121>)
4:54:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SNAFU_PL14HQP)
4:55:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "SNAFU_PL14HQP" (pos=<2495.710693, 249.882736, 7133.932129>)
4:55:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SN_USP)
4:55:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "SN_USP" (pos=<2495.564941, 249.895172, 7133.043457>)
4:56:0 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
4:56:20 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
