{"ConfigVersion": 22, "ID": 13, "Type": 1, "Title": "Catch a Fish", "Descriptions": ["PLACEHOLDER", "Craft an improvised fishing rod and catch a mackerel.", "Turn in quest to get reward."], "ObjectiveText": "Craft an improvised fishing rod and catch a mackerel!", "FollowUpQuest": -1, "Repeatable": 0, "IsDailyQuest": 0, "IsWeeklyQuest": 0, "CancelQuestOnPlayerDeath": 0, "Autocomplete": 0, "IsGroupQuest": 0, "ObjectSetFileName": "", "QuestItems": [], "Rewards": [{"ClassName": "ExpansionBanknoteHryvnia", "Amount": 10, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}, {"ClassName": "ImprovisedFishingRod", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}], "NeedToSelectReward": 0, "RandomReward": 0, "RandomRewardAmount": -1, "RewardsForGroupOwnerOnly": 1, "RewardBehavior": 0, "QuestGiverIDs": [1], "QuestTurnInIDs": [1], "IsAchievement": 0, "Objectives": [{"ConfigVersion": 28, "ID": 1, "ObjectiveType": 11}, {"ConfigVersion": 28, "ID": 2, "ObjectiveType": 4}], "QuestColor": 0, "ReputationReward": 0, "ReputationRequirement": -1, "PreQuestIDs": [], "RequiredFaction": "", "FactionReward": "", "PlayerNeedQuestItems": 1, "DeleteQuestItems": 1, "SequentialObjectives": 1, "FactionReputationRequirements": {}, "FactionReputationRewards": {}, "SuppressQuestLogOnCompetion": 0, "Active": 1}