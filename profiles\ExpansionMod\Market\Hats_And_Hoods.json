{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_HATS & #STR_EXPANSION_MARKET_CATEGORY_HOODS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "medicalscrubshat_blue", "MaxPriceThreshold": 1090, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["medicals<PERSON><PERSON><PERSON><PERSON>_white", "medicalscrubshat_green"]}, {"ClassName": "medicals<PERSON><PERSON><PERSON><PERSON>_white", "MaxPriceThreshold": 1090, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "medicalscrubshat_green", "MaxPriceThreshold": 1090, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cowboy<PERSON>_brown", "MaxPriceThreshold": 2460, "MinPriceThreshold": 1475, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["cowboyhat_black", "cowboyhat_darkbrown", "cowboyhat_green", "expansioncowboyhatgator", "expansioncowboyhatsnake", "expansioncowboyhatrattlesnake"]}, {"ClassName": "militaryberet_chdkz", "MaxPriceThreshold": 9310, "MinPriceThreshold": 5585, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["militaryberet_red", "militaryberet_un", "militaryberet_cdf", "militaryberet_nz"]}, {"ClassName": "militaryberet_red", "MaxPriceThreshold": 9310, "MinPriceThreshold": 5585, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryberet_un", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryberet_cdf", "MaxPriceThreshold": 9310, "MinPriceThreshold": 5585, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "militaryberet_nz", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "beaniehat_pink", "MaxPriceThreshold": 1550, "MinPriceThreshold": 930, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["beaniehat_beige", "beaniehat_blue", "beaniehat_brown", "beaniehat_grey", "beaniehat_red", "beaniehat_black", "beaniehat_green"]}, {"ClassName": "beaniehat_blue", "MaxPriceThreshold": 295, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ushanka_black", "MaxPriceThreshold": 1510, "MinPriceThreshold": 905, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ushanka_blue", "ushan<PERSON>_green"]}, {"ClassName": "ushanka_blue", "MaxPriceThreshold": 1510, "MinPriceThreshold": 905, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ushan<PERSON>_green", "MaxPriceThreshold": 285, "MinPriceThreshold": 170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "booniehat_blue", "MaxPriceThreshold": 1370, "MinPriceThreshold": 820, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["booniehat_navyblue", "booniehat_orange", "booniehat_red", "booniehat_black", "boonie<PERSON>_tan", "booniehat_olive", "booniehat_dpm", "booniehat_dubok", "booniehat_flecktran", "booniehat_winter"]}, {"ClassName": "booniehat_navyblue", "MaxPriceThreshold": 935, "MinPriceThreshold": 560, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "booniehat_dpm", "MaxPriceThreshold": 1340, "MinPriceThreshold": 805, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "booniehat_dubok", "MaxPriceThreshold": 1340, "MinPriceThreshold": 805, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "booniehat_flecktran", "MaxPriceThreshold": 1340, "MinPriceThreshold": 805, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "booniehat_winter", "MaxPriceThreshold": 1110, "MinPriceThreshold": 665, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bude<PERSON><PERSON><PERSON>_gray", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "officerhat", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "nbchoodgray", "MaxPriceThreshold": 13165, "MinPriceThreshold": 7900, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["nbcho<PERSON>ellow", "nbchoodwhite"]}, {"ClassName": "morozkohat", "MaxPriceThreshold": 600, "MinPriceThreshold": 360, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "okzkcap_beige", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["okzkcap_green"]}, {"ClassName": "pet<PERSON><PERSON><PERSON>_black", "MaxPriceThreshold": 470, "MinPriceThreshold": 285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["petushokhat_green", "petush<PERSON><PERSON>_yellow"]}, {"ClassName": "snowstorm<PERSON><PERSON><PERSON>_brown", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["snowstormushanka_navy", "snowstormush<PERSON><PERSON>_olive", "snowstormushanka_white"]}, {"ClassName": "wintercoif_black", "MaxPriceThreshold": 290, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["wintercoif_blue", "wintercoif_green", "wintercoif_skull"]}]}