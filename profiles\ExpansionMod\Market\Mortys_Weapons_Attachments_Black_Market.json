{"m_Version": 12, "DisplayName": "Mortys Weapons Attachments Black Market", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "TTC_Aimpoint2000", "MaxPriceThreshold": 8000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMOD_AFG", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Barrel_76239", "MaxPriceThreshold": 6000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Barrel_76254", "MaxPriceThreshold": 6000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Digi_Hndguard", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Gold_Hndguard", "MaxPriceThreshold": 7000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Hndguard", "MaxPriceThreshold": 4500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Multi_Hndguard", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_<PERSON><PERSON><PERSON>_Snow_Hndguard", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Suppressor", "MaxPriceThreshold": 12000, "MinPriceThreshold": 8000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_<PERSON><PERSON><PERSON>_Tan_Hndguard", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMount", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_BugBuster", "MaxPriceThreshold": 6000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_AK12", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_Camo", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_CAR15", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_Digitan", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_Honey", "MaxPriceThreshold": 4500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_Morty", "MaxPriceThreshold": 5000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_Tan", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock_UPC", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock2", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Buttstock3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ButtstockHK", "MaxPriceThreshold": 4500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_ButtstockHK_Black"]}, {"ClassName": "TTC_Car15V2_PlasticHndgrd", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_AFG", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_DMR_AFG_Green", "TTC_DMR_AFG_Tan"]}, {"ClassName": "TTC_DMR_Barrel_556", "MaxPriceThreshold": 6000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Barrel_762", "MaxPriceThreshold": 6500, "MinPriceThreshold": 4500, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Bipod", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Hndguard", "MaxPriceThreshold": 4500, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Hndguard_SLR", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_HndguardDesert", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_HndguardOD", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_HndguardSnow", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Suppressor", "MaxPriceThreshold": 12000, "MinPriceThreshold": 8000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_VFG", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_DMR_VFG_Green", "TTC_DMR_VFG_Tan"]}, {"ClassName": "TTC_Elcan", "MaxPriceThreshold": 10000, "MinPriceThreshold": 7000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Elcan_AK", "MaxPriceThreshold": 10000, "MinPriceThreshold": 7000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_EotechVudu_Optic", "MaxPriceThreshold": 12000, "MinPriceThreshold": 8000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_EotechVudu_AK_Optic", "TTC_EotechVudu_Tan_AK_Optic", "TTC_EotechVudu_Tan_Optic", "TTC_EotechVudu_White_AK_Optic", "TTC_EotechVudu_White_Optic"]}, {"ClassName": "TTC_FAL_Poly_Hndgrd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_RIS_Hndgrd", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_Wood_Hndgrd", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_G28_Optic", "MaxPriceThreshold": 15000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_G28_Optic_Black", "MaxPriceThreshold": 15000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_G3_Optic", "MaxPriceThreshold": 8000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HAMR", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HAMR_AK", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Holo", "MaxPriceThreshold": 7000, "MinPriceThreshold": 4500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Honey_Suppressor", "MaxPriceThreshold": 10000, "MinPriceThreshold": 7000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M14Suppressor", "MaxPriceThreshold": 12000, "MinPriceThreshold": 8000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX_Spear_Suppressor", "MaxPriceThreshold": 15000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MC<PERSON>_Spear_Suppressor_Black", "MaxPriceThreshold": 15000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 2, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_OpticMount_M16", "MaxPriceThreshold": 3000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Pistol_Light", "MaxPriceThreshold": 3500, "MinPriceThreshold": 2500, "SellPricePercent": -1, "MaxStockThreshold": 8, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Pistol_Optic", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 6, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_PistolSuppressor", "MaxPriceThreshold": 8000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Razor", "MaxPriceThreshold": 6000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SVT40_Bayonet", "MaxPriceThreshold": 2500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 10, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SVT40_Optic", "MaxPriceThreshold": 8000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 5, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Universal_Suppressor_BLACK", "MaxPriceThreshold": 10000, "MinPriceThreshold": 7000, "SellPricePercent": -1, "MaxStockThreshold": 4, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_Universal_Suppressor_CAMO", "TTC_Universal_Suppressor_Snow", "TTC_Universal_Suppressor_Tan", "TTC_Universal_Suppressor_TAVOR", "TTC_Universal_Suppressor_TAVOR_Tan"]}, {"ClassName": "TTC_VortexRHDAMG_Optic", "MaxPriceThreshold": 12000, "MinPriceThreshold": 8000, "SellPricePercent": -1, "MaxStockThreshold": 3, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}