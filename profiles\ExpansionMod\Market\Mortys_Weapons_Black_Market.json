{"m_Version": 12, "DisplayName": "Mortys Weapons Black Market", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "TTC_AWM", "MaxPriceThreshold": 40000, "MinPriceThreshold": 40000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_BAR", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Beowulf", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMR_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRTan_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRTwotone_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRCamo_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRDigiTan_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRUPC_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRSnow_762", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FD338", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_GEVAR43", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_GEVAR43_Green", "TTC_GEVAR43_Tan", "TTC_GEVAR43_silver"]}, {"ClassName": "TTC_G3", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK417", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HKG28", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HKG28_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AACHoney_300blk", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AACHoney_300blk_B", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_kar98k", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_LeeEnfield", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M110", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M110_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M14", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_M14_Camo", "TTC_M14_Snow", "TTC_M14_Black"]}, {"ClassName": "TTC_M1903", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M1Garand", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24_<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24_Woodland", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24_WoodlandOld", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M24_NEW_Wood", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_M24_NEW_Black", "TTC_M24_NEW_Camo", "TTC_M24_NEW_CamoW"]}, {"ClassName": "TTCSR25", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTCSR25_Desert", "TTCSR25_Snow", "TTCSR25_OD"]}, {"ClassName": "TTC_M4Tac", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M4Tac_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M82", "MaxPriceThreshold": 300000, "MinPriceThreshold": 300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M82_Tan", "MaxPriceThreshold": 300000, "MinPriceThreshold": 300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M82_gold", "MaxPriceThreshold": 300000, "MinPriceThreshold": 300000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MAS36", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_STG44", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MRAD", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_R700", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_R700_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARH", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARHBlack", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SVT40", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_XM2010", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_44Magnum_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Deagle_Gold", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AACHoney", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AACHoney_B", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AEK973", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AG3", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AK12", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AK12_Worn", "MaxPriceThreshold": 80000, "MinPriceThreshold": 80000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKM", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK_Digi", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK_Gold", "MaxPriceThreshold": 120000, "MinPriceThreshold": 120000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK_Multi", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK_Snow", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKModPK_Tan", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_762x54_30rnd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_762x54_65rnd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Digi", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Gold", "MaxPriceThreshold": 120000, "MinPriceThreshold": 120000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_Multi", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMod_RifleFrame", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_<PERSON><PERSON><PERSON>_Snow", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AK<PERSON>od_Tan", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AKMount", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_AVS36", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_BugBuster", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_CAR15", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Car15V2", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Car15V2_Bttstck", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Car15V2_PlasticHndgrd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_Bipod", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_RifleFrame", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_VFG", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_VFG_Green", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_DMR_VFG_Tan", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_De<PERSON>le", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_Poly_Hndgrd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_RIS_Hndgrd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_Wood_Bttstck", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_FAL_Wood_Hndgrd", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Glock17", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK416Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK416Comp", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK416OD", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_HK416Tan", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Kimber", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_L85", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M16", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M16A4", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M1A_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["TTC_M1A_Green", "TTC_M1A_Snow", "TTC_M1A_Tan"]}, {"ClassName": "TTC_M4DMR", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M4DMRDesert", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M4DMROD", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M4DMRSNOW", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_Blue_Slide", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_Custom", "MaxPriceThreshold": 60000, "MinPriceThreshold": 60000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_Gold_Grip", "MaxPriceThreshold": 70000, "MinPriceThreshold": 70000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_Red_Slide", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_Tan_Slide", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_M9_White_Slide", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX_300blk", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX_Spear", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MC<PERSON>_Spear_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX_Tan", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MCX_Tan_300blk", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MK18", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MK18_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MP5SD", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MP7A1", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPX", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPXMCX1_Bttstck", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPXMCX1_Bttstck2", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPXMCX2_Bttstck", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_MPXMCXSPEAR_Bttstck", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Maverick", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mk47", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRCamo_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRDigiTan_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRSnow_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRTan_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRTwotone_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMRUPC_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_ModDMR_556", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Mossberg", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_P320", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_P320_17Rnd", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_PP91", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Pistol_Light", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARL", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARLBlack", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SCARLSnow", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG550", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG550_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG550_Pink", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG552", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG552_Black", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SG552_Wood", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_SVT40_Bayonet", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_TAVOR", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_TAVOR_DMR", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_UZI", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_VHS", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "TTC_Winchester1873", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}