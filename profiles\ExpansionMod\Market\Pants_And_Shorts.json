{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_PANTS & #STR_EXPANSION_MARKET_CATEGORY_SHORTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "medicals<PERSON>rubspants_blue", "MaxPriceThreshold": 1090, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["medicals<PERSON><PERSON><PERSON><PERSON>_green", "medicals<PERSON><PERSON>bs<PERSON>_white"]}, {"ClassName": "tracksuitpants_black", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["tracksuitpants_blue", "tracksuitpants_green", "tracksuitpants_red", "tracksuitpants_lightblue"]}, {"ClassName": "prisonuniformpants", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "breeches_pink", "MaxPriceThreshold": 860, "MinPriceThreshold": 515, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["breeches_red", "breeches_white", "breeches_blue", "breeches_beetcheck", "breeches_beige", "breeches_browncheck", "breeches_black", "breeches_blackcheck", "breeches_green"]}, {"ClassName": "breeches_beige", "MaxPriceThreshold": 1035, "MinPriceThreshold": 620, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "breeches_browncheck", "MaxPriceThreshold": 1605, "MinPriceThreshold": 960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "breeches_black", "MaxPriceThreshold": 1035, "MinPriceThreshold": 620, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "breeches_blackcheck", "MaxPriceThreshold": 1605, "MinPriceThreshold": 960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "breeches_green", "MaxPriceThreshold": 1035, "MinPriceThreshold": 620, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "slackspants_beige", "MaxPriceThreshold": 465, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["slackspants_blue", "slackspants_darkgrey", "slackspants_lightgrey", "slackspants_white", "slackspants_brown", "slackspants_black", "slackspants_khaki"]}, {"ClassName": "slackspants_brown", "MaxPriceThreshold": 465, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "slackspants_black", "MaxPriceThreshold": 465, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "slackspants_khaki", "MaxPriceThreshold": 465, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "canvaspantsmidi_blue", "MaxPriceThreshold": 295, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["canvaspantsmidi_grey", "canvaspantsmidi_red", "canvaspantsmidi_violet", "canvaspantsmidi_beige"]}, {"ClassName": "canvaspantsmidi_beige", "MaxPriceThreshold": 295, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "canvaspants_blue", "MaxPriceThreshold": 290, "MinPriceThreshold": 170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["canvaspants_grey", "canvaspants_red", "canvaspants_violet", "canvaspants_beige"]}, {"ClassName": "canvaspants_beige", "MaxPriceThreshold": 290, "MinPriceThreshold": 170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "jumpsuitpants_blue", "MaxPriceThreshold": 2740, "MinPriceThreshold": 1645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["jumpsuitpants_green", "jumpsuitpants_grey", "jumpsuitpants_red"]}, {"ClassName": "policepants", "MaxPriceThreshold": 1100, "MinPriceThreshold": 660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "paramedic<PERSON>_blue", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["paramedicpants_crimson", "paramedic<PERSON>_green"]}, {"ClassName": "firefighterspants_beige", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["firefighterspants_black"]}, {"ClassName": "cargopants_beige", "MaxPriceThreshold": 3125, "MinPriceThreshold": 1875, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["cargopants_black", "cargopants_blue", "cargopants_green", "cargopants_grey"]}, {"ClassName": "shortjeans_blue", "MaxPriceThreshold": 1390, "MinPriceThreshold": 835, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["shortjeans_brown", "shortjeans_darkblue", "shortjeans_red", "shortjeans_black", "shortjeans_green"]}, {"ClassName": "shortjeans_black", "MaxPriceThreshold": 1390, "MinPriceThreshold": 835, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shortjeans_green", "MaxPriceThreshold": 1390, "MinPriceThreshold": 835, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "jeans_blue", "MaxPriceThreshold": 1320, "MinPriceThreshold": 790, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["jeans_brown", "jeans_grey", "jeans_bluedark", "jeans_green", "jeans_black"]}, {"ClassName": "jeans_green", "MaxPriceThreshold": 1320, "MinPriceThreshold": 790, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "jeans_black", "MaxPriceThreshold": 1320, "MinPriceThreshold": 790, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ttskopants", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bdupants", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "usmcpants_desert", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["usmcpants_woodland"]}, {"ClassName": "usmcpants_woodland", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "police<PERSON>orel", "MaxPriceThreshold": 1100, "MinPriceThreshold": 660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hunterpants_winter", "MaxPriceThreshold": 7305, "MinPriceThreshold": 4385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hunterpants_autumn", "hunter<PERSON>_brown", "hunterpants_spring", "hunterpants_summer"]}, {"ClassName": "hunterpants_autumn", "MaxPriceThreshold": 7120, "MinPriceThreshold": 4270, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hunter<PERSON>_brown", "MaxPriceThreshold": 7120, "MinPriceThreshold": 4270, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hunterpants_spring", "MaxPriceThreshold": 7430, "MinPriceThreshold": 4455, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hunterpants_summer", "MaxPriceThreshold": 5645, "MinPriceThreshold": 3385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "gorka<PERSON>_summer", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["gorkapants_autumn", "gorkapants_flat", "gorka<PERSON>_pautrev", "gorka<PERSON>_winter"]}, {"ClassName": "nbcpantsgray", "MaxPriceThreshold": 13165, "MinPriceThreshold": 7900, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["nbc<PERSON>yellow", "nbcpantswhite"]}, {"ClassName": "chainmail_leggings", "MaxPriceThreshold": 595, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "omkpants_navy", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "navyuniformpants", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}