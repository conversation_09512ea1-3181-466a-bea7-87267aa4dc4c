=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-21_20-2-50
====================================================================================
20:2:50 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
20:2:50 | [PermissionManager] Perm (FreeCamera) has been registered!
20:2:50 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
20:2:50 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
20:2:50 | [PermissionManager] Perm (MenuServerManager) has been registered!
20:2:50 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
20:2:50 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
20:2:50 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
20:2:50 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
20:2:50 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
20:2:50 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
20:2:50 | [PermissionManager] Perm (MenuObjectManager) has been registered!
20:2:50 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
20:2:50 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
20:2:50 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
20:2:50 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
20:2:50 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
20:2:50 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
20:2:50 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
20:2:50 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
20:2:50 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
20:2:50 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
20:2:50 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
20:2:50 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
20:2:50 | [PermissionManager] Perm (MenuBansManager) has been registered!
20:2:50 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
20:2:50 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
20:2:50 | [PermissionManager] Perm (MenuWebHooks) has been registered!
20:2:50 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
20:2:50 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
20:2:50 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
20:2:50 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
20:2:50 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
20:2:50 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
20:2:50 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
20:2:50 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
20:2:50 | [PermissionManager] Adding Super Admin (76561199239979485)
20:2:50 | [PermissionManager] Adding Super Admin (76561197999250250)
20:2:50 | [PermissionManager] Adding Super Admin (76561198153347717)
20:2:50 | [PermissionManager] Loaded UserGroups.json
20:2:50 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
20:2:50 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
20:2:50 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
20:2:50 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
20:2:50 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
20:2:50 | Building Sets Loaded: 0
20:2:50 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
20:45:36 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
20:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
21:14:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6772.953125, 223.549255, 4442.721680>)
21:14:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6773.981934, 223.547699, 4458.944824>)
21:14:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6777.175293, 223.402771, 4446.157227>)
21:14:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6779.836426, 223.589355, 4434.671875>)
21:21:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (LandMineTrap)
21:24:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
21:24:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
21:31:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
21:55:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6021.785156, 258.327362, 7277.964844>)
21:57:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6128.410645, 268.240204, 7320.465332>)
21:57:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6205.134277, 267.070435, 7270.627441>)
22:2:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "OffroadHatchback_White" (pos=<5269.530273, 254.131317, 7813.983887>)
22:3:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4660.817383, 201.404541, 8280.231445>)
22:3:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4632.175781, 203.817963, 8295.477539>)
22:3:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4616.035645, 204.989960, 8305.006836>)
22:3:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4586.871582, 206.323425, 8320.129883>)
22:3:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4566.790527, 207.296539, 8330.513672>)
22:4:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
22:6:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4898.217773, 199.545700, 8084.151855>)
22:6:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4897.045410, 200.271912, 8082.674805>)
22:8:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
22:8:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4913.226074, 206.348602, 8058.008301>)
22:17:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4865.119141, 197.219955, 8054.842773>)
22:35:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionUh1h_Uber" (pos=<4061.905518, 371.387726, 10552.514648>)
22:50:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "kamaz_4310RUS_kung" (pos=<5908.369629, 305.831665, 11132.641602>)
22:53:27 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
22:53:47 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
