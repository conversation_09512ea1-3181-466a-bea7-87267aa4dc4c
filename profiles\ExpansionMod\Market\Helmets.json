{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_HELMETS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "constructionhelmet_blue", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["constructionhelmet_orange", "constructionhelmet_red", "constructionhelmet_white", "constructionhelmet_yellow", "constructionhelmet_lime"]}, {"ClassName": "constructionhelmet_orange", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "constructionhelmet_red", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "constructionhelmet_white", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "constructionhelmet_yellow", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "constructionhelmet_lime", "MaxPriceThreshold": 800, "MinPriceThreshold": 480, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "skatehelmet_blue", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["skatehelmet_gray", "skatehelmet_red", "skatehelmet_black", "skatehelmet_green"]}, {"ClassName": "hockeyhelmet_blue", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hockeyhelmet_red", "hockeyhelmet_white", "hockeyhelmet_black"]}, {"ClassName": "hockeyhelmet_red", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hockeyhelmet_white", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hockeyhelmet_black", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "dirtbikehelmet_mouthguard", "MaxPriceThreshold": 355, "MinPriceThreshold": 210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "dirtbikehelmet_visor", "MaxPriceThreshold": 355, "MinPriceThreshold": 210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "dirtbikehelmet_chernarus", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": ["dirtbikehelmet_police", "dirtbikehelmet_red", "dirtbikehelmet_green", "dirtbikehelmet_blue", "dirtbikehelmet_black", "dirtbikehelmet_khaki"]}, {"ClassName": "dirtbikehelmet_police", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "dirtbikehelmet_red", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "dirtbikehelmet_green", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "dirtbikehelmet_blue", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "dirtbikehelmet_black", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "dirtbikehelmet_khaki", "MaxPriceThreshold": 360, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["dirtbikehelmet_mouthguard", "dirtbikehelmet_visor"], "Variants": []}, {"ClassName": "motohelmet_lime", "MaxPriceThreshold": 1095, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["motohelmet_blue", "motohelmet_red", "motohelmet_yellow", "motohelmet_white", "motohelmet_grey", "motohelmet_black", "motohelmet_green", "darkmotohelmet_lime", "darkmotohelmet_blue", "darkmotohelmet_red", "darkmotohelmet_white", "darkmotohelmet_grey", "darkmotohelmet_black", "darkmotohelmet_green"]}, {"ClassName": "motohelmet_blue", "MaxPriceThreshold": 1315, "MinPriceThreshold": 790, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "motohelmet_red", "MaxPriceThreshold": 1095, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "motohelmet_white", "MaxPriceThreshold": 1095, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "motohelmet_grey", "MaxPriceThreshold": 2325, "MinPriceThreshold": 1395, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "motohelmet_black", "MaxPriceThreshold": 2325, "MinPriceThreshold": 1395, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "motohelmet_green", "MaxPriceThreshold": 1315, "MinPriceThreshold": 790, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_grey", "MaxPriceThreshold": 2365, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_lime", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_blue", "MaxPriceThreshold": 1335, "MinPriceThreshold": 800, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_red", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_white", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_black", "MaxPriceThreshold": 2365, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_green", "MaxPriceThreshold": 1335, "MinPriceThreshold": 800, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_yellow", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "darkmotohelmet_yellowscarred", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 11745, "MinPriceThreshold": 7045, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "zsh3pilothelmet", "MaxPriceThreshold": 11545, "MinPriceThreshold": 6925, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["zsh3pilothelmet_green", "zsh3pilothelmet_black"]}, {"ClassName": "zsh3pilothelmet_green", "MaxPriceThreshold": 270, "MinPriceThreshold": 135, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "zsh3pilothelmet_black", "MaxPriceThreshold": 270, "MinPriceThreshold": 135, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "firefightershelmet_red", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["firefightershelmet_white", "firefightershelmet_yellow"]}, {"ClassName": "mich2001helmet", "MaxPriceThreshold": 13055, "MinPriceThreshold": 7830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionmich2001desert"]}, {"ClassName": "gorkahelmetvisor", "MaxPriceThreshold": 9150, "MinPriceThreshold": 5490, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9550, "MinPriceThreshold": 5730, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["gorkahelmetvisor"], "Variants": ["gorkahel<PERSON>_black"]}, {"ClassName": "ssh68helmet", "MaxPriceThreshold": 1650, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ballistichelmet_un", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ballistichelmet_black", "ballistichelmet_green", "ballistichelmet_bdu", "ballistichelmet_desert", "ballistichelmet_woodland", "ballistichelmet_navy", "ballistichelmet_winter"]}, {"ClassName": "ballistichelmet_bdu", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ballistichelmet_woodland", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ballistichelmet_navy", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ballistichelmet_winter", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "chainmail_coif", "MaxPriceThreshold": 595, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}