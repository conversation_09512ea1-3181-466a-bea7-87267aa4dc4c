{"m_Version": 12, "DisplayName": "Paragon Gear And Armor", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "Paragon_Ballistic_H_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Ballistic_H_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Ballistic_H_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Ballistic_H_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Ballistic_H_Grey", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rubicon_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rubicon_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rubicon_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rubicon_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B13_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Digital_Flora", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Digital_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Flora", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_6B43_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_Black", "MaxPriceThreshold": 200000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Altyn_Blue", "MaxPriceThreshold": 200000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Altyn_Desert", "MaxPriceThreshold": 200000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Altyn_Green", "MaxPriceThreshold": 200000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Altyn_Fatigue", "MaxPriceThreshold": 200000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Altyn_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Armored_H_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON>_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Bottle_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Bottle_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>ttle_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Combat_H_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Combat_H_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Combat_H_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Combat_H_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Digital_Flora", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Digital_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Flora", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_C6B43_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Compact_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Compact_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Compact_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Compact_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Compact_Grey", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DCS_L_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>le_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Duffle_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>le_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BGoggles_Pink", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_B<PERSON>og<PERSON>_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BGoggles_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BGoggles_Red", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DGoggles_Pink", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DGoggles_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DGoggles_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DGoggles_Red", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GGoggles_Pink", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_G<PERSON>og<PERSON>_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GGoggles_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GGoggles_Red", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunSling_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunSling_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunSling_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunSling_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IOTV_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_JPC_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_Black", "MaxPriceThreshold": 100000000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Killa_Green", "MaxPriceThreshold": 100000000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Killa_Blue", "MaxPriceThreshold": 100000000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Killa_Desert", "MaxPriceThreshold": 100000000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "MaxPriceThreshold": 100000000, "MinPriceThreshold": 10000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mando_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IFAK_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IFAK_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IFAK_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IFAK_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_IFAK_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Black_B", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Black_P", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Black_G", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Green_B", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Green_P", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Green_G", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Blue_B", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Blue_P", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Blue_G", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Desert_B", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Desert_P", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NVG_Desert_G", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Desert_Camo", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Balaclava_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalGloves_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Purple", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scifi_H_Red", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_<PERSON>", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>lick_Blue", "MaxPriceThreshold": 200000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>lick_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Slick_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Slick_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Slick_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Slick_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Soviet_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacTec_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tac_H_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_EarCovers_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_EarCovers_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_EarCovers_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_EarCovers_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HP_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HP_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HP_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HP_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mandible_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mandible_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mandible_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mandible_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mandible_Desert_Camo", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Visor_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Visor_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Visor_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Visor_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_US_Military_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_US_Military_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_US_Military_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_US_Military_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_BRY", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_BY", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_Rainbow", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_Purple", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Karambit_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_KATT_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_KATT_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_KATT_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_USA", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Austria", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_France", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Germany", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Israel", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Russian", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Ukraine", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Canada", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Poland", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Dayz", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_China", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Japan", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Australia", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Patch_Britain", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BodyBelt_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CombatPants_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacPants_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Deployment_Bag_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Deployment_Bag_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Deployment_Bag_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Deployment_Bag_Desert_Camo", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Deployment_Bag_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunBag_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunBag_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunBag_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_GunBag_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_RaidBag_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_RaidBag_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_RaidBag_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_RaidBag_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_RaidBag_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rush_24_Bag_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rush_24_Bag_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rush_24_Bag_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rush_24_Bag_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tactical_Bag_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tactical_Bag_Red", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tactical_Bag_Tan", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tactical_Bag_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Tactical_Bag_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesL_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesL_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesL_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesL_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesM_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesM_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesM_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesM_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesS_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesS_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesS_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Leg_PouchesS_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BackPouch_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BackPouch_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BackPouch_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BackPouch_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON>gon_Back<PERSON><PERSON>ch_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtP<PERSON>ch_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_ButtPouch_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_FrontPouch_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_Mag_Pouch_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Pouch_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Pouch_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Pouch_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_Ma<PERSON>_<PERSON>uch_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MapPouch_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MapPouch_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MapPouch_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MapPouch_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON>_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Maska_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Maska_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Maska_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Maska_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON>a_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacBoots_Black_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Digital_Flora", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Digital_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Green", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Blue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Desert", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_White", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Fatigue", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_Navy", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacJacket_WL", "MaxPriceThreshold": 20000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Black", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Blue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Green", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Desert", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_White", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Fatigue", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Navy", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_Black_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TacticalVest_WL", "MaxPriceThreshold": 20000000, "MinPriceThreshold": 17000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}