=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-12_23-2-47
====================================================================================
23:2:47 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:2:47 | [PermissionManager] Perm (FreeCamera) has been registered!
23:2:47 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:2:47 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:2:47 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:2:47 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:2:47 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:2:47 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:2:47 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:2:47 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:2:47 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:2:47 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:2:47 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:2:47 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:2:47 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:2:47 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:2:47 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:2:47 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:2:47 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:2:47 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:2:47 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:2:47 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:2:47 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:2:47 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:2:47 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:2:47 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:2:47 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:2:47 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:2:47 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:2:47 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:2:47 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:2:47 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:2:47 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:2:47 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:2:47 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:2:47 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:2:47 | [PermissionManager] Adding Super Admin (76561199239979485)
23:2:47 | [PermissionManager] Adding Super Admin (76561197999250250)
23:2:47 | [PermissionManager] Adding Super Admin (76561198153347717)
23:2:47 | [PermissionManager] Loaded UserGroups.json
23:2:47 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:2:47 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:2:47 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:2:47 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:2:47 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:2:47 | Building Sets Loaded: 0
23:2:47 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
0:57:35 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
0:59:2 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1581.750000, 200.817795, 9021.230469>)
0:59:2 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1581.75,0,9021.23)
1:2:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1562.475220, 208.755630, 8972.059570>)
1:3:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1568.742188, 208.720581, 8976.090820>)
1:5:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1515.773438, 202.643127, 9001.500977>)
1:5:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1525.583740, 202.676529, 8986.656250>)
1:5:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1535.733154, 202.676498, 8971.185547>)
1:5:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1545.021973, 202.675613, 8958.929688>)
1:5:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1556.359497, 202.479965, 8950.110352>)
1:6:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1646.248413, 202.420868, 8764.664063>)
1:6:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1652.254272, 205.352234, 8744.246094>)
1:6:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1657.310425, 207.861938, 8729.198242>)
1:6:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1663.084717, 210.464783, 8716.194336>)
1:12:37 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<7172.209961, 962.059937, 9522.669922>)
1:12:37 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=7172.21,0,9522.67)
1:14:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7268.050293, 962.059998, 9555.744141>)
1:14:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7263.086914, 962.059937, 9569.297852>)
1:14:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7242.612793, 962.059937, 9577.680664>)
1:14:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7223.813965, 962.059937, 9583.453125>)
1:14:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) /heal used on: "(GB)BURT GUMMER" (steamid=76561197999250250)
1:14:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) /heal used on: "(GB)BURT GUMMER" (steamid=76561197999250250)
1:14:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7192.072266, 962.059937, 9539.633789>)
1:14:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7187.717773, 962.059937, 9553.289063>)
1:14:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7186.177734, 962.059937, 9566.776367>)
1:14:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7182.371582, 964.010498, 9620.338867>)
1:14:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7195.396973, 962.059937, 9599.099609>)
1:14:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7206.529297, 962.059937, 9588.215820>)
1:14:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7200.003906, 962.059875, 9574.240234>)
1:14:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7165.096680, 965.065674, 9584.027344>)
1:19:20 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<11439.500000, 301.033234, 10351.099609>)
1:19:20 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=11439.5,0,10351.1)
1:31:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11469.549805, 298.666962, 10285.844727>)
1:31:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11461.919922, 298.278656, 10286.386719>)
1:31:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11452.908203, 297.765594, 10288.371094>)
1:31:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11445.412109, 297.556305, 10292.783203>)
1:31:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11438.419922, 297.611481, 10297.110352>)
1:31:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11431.102539, 297.604889, 10299.014648>)
1:31:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11423.765625, 297.546936, 10299.787109>)
1:31:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11413.171875, 297.286072, 10298.582031>)
1:31:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11402.515625, 296.970154, 10297.026367>)
1:31:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11387.662109, 294.114899, 10273.636719>)
1:31:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11384.708008, 293.115875, 10265.954102>)
1:36:35 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<11929.099609, 295.769989, 10671.700195>)
1:36:35 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=11929.1,0,10671.7)
1:36:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11961.556641, 297.721649, 10691.399414>)
1:36:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11975.860352, 298.855377, 10696.753906>)
1:36:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11990.990234, 299.981506, 10700.560547>)
1:36:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<12003.546875, 300.727448, 10701.551758>)
1:36:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<12015.621094, 301.338501, 10702.111328>)
1:36:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<12028.023438, 301.909576, 10702.666992>)
1:36:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<12041.324219, 302.478943, 10703.286133>)
1:39:2 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<12031.599609, 355.749878, 6888.470215>)
1:39:2 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=12031.6,0,6888.47)
1:43:43 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<12013.599609, 471.820465, 1773.050049>)
1:43:43 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=12013.6,0,1773.05)
1:47:51 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<11876.799805, 476.053223, 2236.590088>)
1:47:51 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=11876.8,0,2236.59)
1:49:35 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<12036.000000, 356.297882, 6894.540039>)
1:49:35 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=12036,0,6894.54)
1:50:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:52:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CrSk_Land_Rover_Defender_110_Green)
1:52:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CrSk_Land_Rover_Defender_110_Green)
1:56:1 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
1:56:26 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
