{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_SNIPER_RIFLES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "vss", "MaxPriceThreshold": 3200, "MinPriceThreshold": 1600, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_vss_10rnd"], "Variants": []}, {"ClassName": "asval", "MaxPriceThreshold": 3200, "MinPriceThreshold": 1600, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_val_20rnd"], "Variants": []}, {"ClassName": "b95", "MaxPriceThreshold": 3400, "MinPriceThreshold": 1700, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "svd", "MaxPriceThreshold": 4000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_svd_10rnd"], "Variants": []}, {"ClassName": "scout_chernarus", "MaxPriceThreshold": 4200, "MinPriceThreshold": 2100, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_scout_5rnd"], "Variants": []}, {"ClassName": "expansion_awm", "MaxPriceThreshold": 10000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_expansion_awm_5rnd"], "Variants": []}, {"ClassName": "Expansion_AWM", "MaxPriceThreshold": 40000, "MinPriceThreshold": 35000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_expansion_awm_5rnd"], "Variants": ["Expansion_AWM_Black", "Expansion_AWM_Green"]}]}