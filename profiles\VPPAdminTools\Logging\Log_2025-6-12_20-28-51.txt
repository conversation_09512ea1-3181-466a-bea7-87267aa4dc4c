=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-12_20-28-51
====================================================================================
20:28:51 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
20:28:51 | [PermissionManager] Perm (FreeCamera) has been registered!
20:28:51 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
20:28:51 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
20:28:51 | [PermissionManager] Perm (MenuServerManager) has been registered!
20:28:51 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
20:28:51 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
20:28:51 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
20:28:51 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
20:28:51 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
20:28:51 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
20:28:51 | [PermissionManager] Perm (MenuObjectManager) has been registered!
20:28:51 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
20:28:51 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
20:28:51 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
20:28:51 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
20:28:51 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
20:28:51 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
20:28:51 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
20:28:51 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
20:28:51 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
20:28:51 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
20:28:51 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
20:28:51 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
20:28:51 | [PermissionManager] Perm (MenuBansManager) has been registered!
20:28:51 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
20:28:51 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
20:28:51 | [PermissionManager] Perm (MenuWebHooks) has been registered!
20:28:51 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
20:28:51 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
20:28:51 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
20:28:51 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
20:28:51 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
20:28:51 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
20:28:51 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
20:28:51 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
20:28:51 | [PermissionManager] Adding Super Admin (76561199239979485)
20:28:51 | [PermissionManager] Adding Super Admin (76561197999250250)
20:28:51 | [PermissionManager] Adding Super Admin (76561198153347717)
20:28:51 | [PermissionManager] Loaded UserGroups.json
20:28:51 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
20:28:51 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
20:28:51 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
20:28:51 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
20:28:51 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
20:28:51 | Building Sets Loaded: 0
20:28:51 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
20:53:4 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
20:54:20 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<9953.049805, 229.143051, 7127.560059>)
20:54:20 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=9953.05,0,7127.56)
20:54:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9921.645508, 224.844620, 7146.308594>)
20:54:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9895.649414, 222.468964, 7147.121094>)
20:54:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9877.288086, 221.307159, 7142.642090>)
20:54:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9859.032227, 220.462357, 7138.236328>)
20:54:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9842.571289, 220.134903, 7135.199707>)
20:54:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9823.937500, 220.134918, 7133.884766>)
20:54:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9809.140625, 220.134903, 7133.258301>)
20:54:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9802.919922, 220.318253, 7145.193359>)
20:54:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9801.201172, 220.134903, 7157.409180>)
20:54:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9799.701172, 220.226288, 7169.094727>)
20:54:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9800.776367, 220.134918, 7156.131348>)
20:54:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9800.219727, 220.134933, 7144.416504>)
20:54:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9802.135742, 220.134933, 7130.013672>)
20:54:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
20:59:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_kung)
20:59:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_kung)
21:0:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionCarKey)
21:52:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5639.044922, 592.915283, 11744.708008>)
22:0:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5635.104980, 590.162170, 11698.593750>)
22:0:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5660.800781, 590.162415, 11706.468750>)
22:0:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5672.868164, 590.048706, 11707.323242>)
22:0:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5688.379883, 589.070679, 11710.752930>)
22:0:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<5705.053711, 587.624695, 11714.562500>)
22:1:15 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<2112.159912, 385.985962, 11957.400391>)
22:1:15 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=2112.16,0,11957.4)
22:1:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2151.743652, 393.283844, 11958.143555>)
22:4:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2211.081055, 395.790375, 11998.797852>)
22:6:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2248.049805, 400.302643, 12034.864258>)
22:7:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2236.131592, 391.734680, 12038.365234>)
22:8:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2214.695557, 395.846771, 12001.877930>)
22:8:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2251.151367, 387.951172, 12008.622070>)
22:9:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2218.701172, 395.807617, 11999.473633>)
22:10:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2208.342041, 391.508545, 11927.125000>)
22:11:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2140.941895, 372.248077, 11770.513672>)
22:12:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2130.569092, 373.332245, 11758.529297>)
22:15:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Gun)
22:15:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
22:15:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
22:19:9 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1490.239990, 755.205444, 4500.459961>)
22:19:9 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1490.24,0,4500.46)
22:23:54 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<609.966003, 531.283569, 8884.370117>)
22:23:54 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=609.966,0,8884.37)
22:25:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<688.491516, 532.165283, 8773.416992>)
22:26:15 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1501.130005, 302.344879, 8035.020020>)
22:26:15 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1501.13,0,8035.02)
22:26:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (FishingRod)
22:27:11 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Hook)
22:28:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (HuntingKnife)
22:32:33 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
22:32:58 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
