{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_FISH", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "carpfilletmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mackerelfilletmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "carp", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sardines", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mackerel", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "steelheadtroutfilletmeat", "MaxPriceThreshold": 16, "MinPriceThreshold": 8, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "steelheadtrout", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "redcaviar", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shrimp", "MaxPriceThreshold": 40, "MinPriceThreshold": 20, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}