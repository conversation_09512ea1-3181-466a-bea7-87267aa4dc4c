{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_SKIRTS & #STR_EXPANSION_MARKET_CATEGORY_DRESSES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "skirt_blue", "MaxPriceThreshold": 1050, "MinPriceThreshold": 630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["skirt_red", "skirt_white", "skirt_yellow"]}, {"ClassName": "skirt_red", "MaxPriceThreshold": 1050, "MinPriceThreshold": 630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "skirt_white", "MaxPriceThreshold": 1050, "MinPriceThreshold": 630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "skirt_yellow", "MaxPriceThreshold": 1050, "MinPriceThreshold": 630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_pink", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["minidress_pinkchecker", "minidress_redchecker", "minidress_bluechecker", "minidress_bluewithdots", "minidress_whitechecker", "minidress_brownchecker", "minidress_greenchecker"]}, {"ClassName": "minidress_pinkchecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_redchecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_bluechecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_bluewithdots", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_whitechecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_brownchecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "minidress_greenchecker", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "nursedress_blue", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["nursedress_white"]}, {"ClassName": "nursedress_white", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}