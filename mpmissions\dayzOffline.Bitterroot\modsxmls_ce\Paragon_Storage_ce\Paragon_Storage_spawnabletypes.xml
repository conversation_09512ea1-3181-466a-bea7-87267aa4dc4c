<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>

<spawnabletypes>
	<type name="Paragon_Adoor_Black">

	</type>
	<type name="Paragon_Adoor_Gold">

	</type>
	<type name="Paragon_Adoor_Green">

	</type>
	<type name="Paragon_Adoor_Blue">

	</type>
	<type name="Paragon_Adoor_Rainbow">

	</type>
	<type name="Paragon_BigSafe_Black">

	</type>
	<type name="Paragon_BigSafe_Grey">

	</type>
	<type name="Paragon_BigSafe_Rainbow">

	</type>
	<type name="Paragon_BigTent_Black">

	</type>
	<type name="Paragon_BigTent_White">

	</type>
	<type name="Paragon_BigTent_Green">

	</type>
	<type name="Paragon_Bdoor">

	</type>
	<type name="Paragon_Compound_Gate">

	</type>
	<type name="Paragon_Compound_Wall">

	</type>
	<type name="Paragon_Container_Black">

	</type>
	<type name="Paragon_Container_Grey">

	</type>
	<type name="Paragon_Container_Tan">

	</type>
	<type name="Paragon_Container_Green">

	</type>
	<type name="Paragon_Container_Blue">

	</type>
	<type name="Paragon_Container_Red">

	</type>
	<type name="Paragon_DGunCase_Brown">

	</type>
	<type name="Paragon_DGunCase_Grey">

	</type>
	<type name="Paragon_DGunCase_Cherry">

	</type>
	<type name="Paragon_DGunRack_Black">

	</type>
	<type name="Paragon_DGunRack_Green">

	</type>
	<type name="Paragon_DGunRack_Tan">

	</type>
	<type name="Paragon_Dumpster">

	</type>
	<type name="Paragon_Dumpster_Static">

	</type>
	<type name="Paragon_Fridge_Black">

	</type>
	<type name="Paragon_Fridge_White">

	</type>
	<type name="Paragon_GearStand">

	</type>
	<type name="Paragon_GearStandC_B">

	</type>
	<type name="Paragon_GearStandC_G">

	</type>
	<type name="Paragon_GearStandC_C">

	</type>
	<type name="Paragon_GraffitiCan">

	</type>
	<type name="Paragon_GraffitiCan_Static">

	</type>
	<type name="Paragon_GunCase_Brown">

	</type>
	<type name="Paragon_GunCase_Grey">

	</type>
	<type name="Paragon_GunCase_Cherry">

	</type>
	<type name="Paragon_GunRack_Black">

	</type>
	<type name="Paragon_GunRack_Green">

	</type>
	<type name="Paragon_GunRack_Tan">

	</type>
	<type name="Paragon_GunWall_Black">

	</type>
	<type name="Paragon_GunWall_Green">

	</type>
	<type name="Paragon_GunWall_Tan">

	</type>
	<type name="Paragon_HeliPad">

	</type>
	<type name="Paragon_HotDog_Cart">

	</type>
	<type name="Paragon_IceBox">

	</type>
	<type name="Paragon_IC_Freezer">

	</type>
	<type name="Paragon_Locker_Black">

	</type>
	<type name="Paragon_Locker_Blue">

	</type>
	<type name="Paragon_Locker_Green">

	</type>
	<type name="Paragon_Locker_Purple">

	</type>
	<type name="Paragon_Locker_Red">

	</type>
	<type name="Paragon_Locker_White">

	</type>
	<type name="Paragon_Locker_Yellow">

	</type>
	<type name="Paragon_MediumCrate_Black">

	</type>
	<type name="Paragon_MediumCrate_Grey">

	</type>
	<type name="Paragon_MediumCrate_Tan">

	</type>
	<type name="Paragon_MediumCrate_Green">

	</type>
	<type name="Paragon_MediumCrate_Blue">

	</type>
	<type name="Paragon_MetalRack_Black">

	</type>
	<type name="Paragon_MetalRack_Tan">

	</type>
	<type name="Paragon_MetalRack_Green">

	</type>
	<type name="Paragon_Mcabinet_Black">

	</type>
	<type name="Paragon_Mcabinet_Tan">

	</type>
	<type name="Paragon_Mcabinet_Green">

	</type>
	<type name="Paragon_MiliCrate_Black">

	</type>
	<type name="Paragon_MiliCrate_Grey">

	</type>
	<type name="Paragon_MiliCrate_Tan">

	</type>
	<type name="Paragon_MiliCrate_Green">

	</type>
	<type name="Paragon_MiliCrate_Blue">

	</type>
	<type name="Paragon_Mlocker_Black">

	</type>
	<type name="Paragon_Mlocker_Grey">

	</type>
	<type name="Paragon_Mlocker_Green">

	</type>
	<type name="Paragon_Mlocker_Tan">

	</type>
	<type name="Paragon_Mlocker_Blue">

	</type>
	<type name="Paragon_Pallet_Black">

	</type>
	<type name="Paragon_Pallet_Tan">

	</type>
	<type name="Paragon_Pallet_Green">

	</type>
	<type name="Paragon_PlanterBox">

	</type>
	<type name="Paragon_GreenHouse">

	</type>
	<type name="Paragon_LargeGreenHouse">

	</type>
	<type name="Paragon_Rdoor_Black">

	</type>
	<type name="Paragon_Rdoor_Green">

	</type>
	<type name="Paragon_Safe_Black">

	</type>
	<type name="Paragon_Safe_Grey">

	</type>
	<type name="Paragon_Safe_Gold">

	</type>
	<type name="Paragon_Safe_Green">

	</type>
	<type name="Paragon_Safe_Blue">

	</type>
	<type name="Paragon_Safe_White">

	</type>
	<type name="Paragon_Safe_Rainbow">

	</type>
	<type name="Paragon_SmallSign_Guns">

	</type>
	<type name="Paragon_SmallSign_Attachments">

	</type>
	<type name="Paragon_SmallSign_Ammo">

	</type>
	<type name="Paragon_SmallSign_Building">

	</type>
	<type name="Paragon_SmallSign_Clothes">

	</type>
	<type name="Paragon_SmallSign_Food">

	</type>
	<type name="Paragon_SmallSign_Meds">

	</type>
	<type name="Paragon_SmallSign_Sellables">

	</type>
	<type name="Paragon_SmallSign_Armor">

	</type>
	<type name="Paragon_SmallSign_Guns_Glow">

	</type>
	<type name="Paragon_SmallSign_Attachments_Glow">

	</type>
	<type name="Paragon_SmallSign_Ammo_Glow">

	</type>
	<type name="Paragon_SmallSign_Building_Glow">

	</type>
	<type name="Paragon_SmallSign_Clothes_Glow">

	</type>
	<type name="Paragon_SmallSign_Food_Glow">

	</type>
	<type name="Paragon_SmallSign_Meds_Glow">

	</type>
	<type name="Paragon_SmallSign_Sellables_Glow">

	</type>
	<type name="Paragon_SmallSign_Armor_Clow">

	</type>
	<type name="Paragon_MedSign_Guns">

	</type>
	<type name="Paragon_MedSign_Attachments">

	</type>
	<type name="Paragon_MedSign_Ammo">

	</type>
	<type name="Paragon_MedSign_Building">

	</type>
	<type name="Paragon_MedSign_Clothes">

	</type>
	<type name="Paragon_MedSign_Food">

	</type>
	<type name="Paragon_MedSign_Meds">

	</type>
	<type name="Paragon_MedSign_Sellables">

	</type>
	<type name="Paragon_MedSign_Armor">

	</type>
	<type name="Paragon_MedSign_Guns_Glow">

	</type>
	<type name="Paragon_MedSign_Attachments_Glow">

	</type>
	<type name="Paragon_MedSign_Ammo_Glow">

	</type>
	<type name="Paragon_MedSign_Building_Glow">

	</type>
	<type name="Paragon_MedSign_Clothes_Glow">

	</type>
	<type name="Paragon_MedSign_Food_Glow">

	</type>
	<type name="Paragon_MedSign_Meds_Glow">

	</type>
	<type name="Paragon_MedSign_Sellables_Glow">

	</type>
	<type name="Paragon_MedSign_Armor_Glow">

	</type>
	<type name="Paragon_BigSign_Guns">

	</type>
	<type name="Paragon_BigSign_Attachments">

	</type>
	<type name="Paragon_BigSign_Ammo">

	</type>
	<type name="Paragon_BigSign_Building">

	</type>
	<type name="Paragon_BigSign_Clothes">

	</type>
	<type name="Paragon_BigSign_Food">

	</type>
	<type name="Paragon_BigSign_Meds">

	</type>
	<type name="Paragon_BigSign_Sellables">

	</type>
	<type name="Paragon_BigSign_Armor">

	</type>
	<type name="Paragon_BigSign_Guns_Glow">

	</type>
	<type name="Paragon_BigSign_Attachments_Glow">

	</type>
	<type name="Paragon_BigSign_Ammo_Glow">

	</type>
	<type name="Paragon_BigSign_Building_Glow">

	</type>
	<type name="Paragon_BigSign_Clothes_Glow">

	</type>
	<type name="Paragon_BigSign_Food_Glow">

	</type>
	<type name="Paragon_BigSign_Meds_Clow">

	</type>
	<type name="Paragon_BigSign_Sellables_Glow">

	</type>
	<type name="Paragon_BigSign_Armor_Glow">

	</type>
	<type name="Paragon_SmallCrate_Black">

	</type>
	<type name="Paragon_SmallCrate_Grey">

	</type>
	<type name="Paragon_SmallCrate_Tan">

	</type>
	<type name="Paragon_SmallCrate_Green">

	</type>
	<type name="Paragon_SmallCrate_Blue">

	</type>
	<type name="Paragon_SmallSafe_Black">

	</type>
	<type name="Paragon_SmallSafe_Grey">

	</type>
	<type name="Paragon_SmallSafe_Gold">

	</type>
	<type name="Paragon_SmallSafe_Green">

	</type>
	<type name="Paragon_SmallSafe_Blue">

	</type>
	<type name="Paragon_SmallSafe_White">

	</type>
	<type name="Paragon_SmallSafe_Rainbow">

	</type>
	<type name="StorageBox_Tcrate_Black">

	</type>
	<type name="StorageBox_Tcrate_Grey">

	</type>
	<type name="StorageBox_Tcrate_Tan">

	</type>
	<type name="StorageBox_Tcrate_Green">

	</type>
	<type name="StorageBox_Tcrate_Blue">

	</type>
	<type name="StorageBox_Scrate_Black">

	</type>
	<type name="StorageBox_Scrate_Grey">

	</type>
	<type name="StorageBox_Scrate_Tan">

	</type>
	<type name="StorageBox_Scrate_Green">

	</type>
	<type name="StorageBox_Scrate_Blue">

	</type>
	<type name="StorageBox_Mcrate_Black">

	</type>
	<type name="StorageBox_Mcrate_Grey">

	</type>
	<type name="StorageBox_Mcrate_Tan">

	</type>
	<type name="StorageBox_Mcrate_Green">

	</type>
	<type name="StorageBox_Mcrate_Blue">

	</type>
	<type name="StorageBox_GunRack_Black">

	</type>
	<type name="StorageBox_CunRack_Tan">

	</type>
	<type name="StorageBox_CunRack_Green">

	</type>
	<type name="StorageBox_DGunRack_Black">

	</type>
	<type name="StorageBox_DCunRack_Tan">

	</type>
	<type name="StorageBox_DGunRack_Green">

	</type>
	<type name="StorageBox_CunCase_Brown">

	</type>
	<type name="StorageBox_GunCase_Grey">

	</type>
	<type name="StorageBox_GunCase_Cherry">

	</type>
	<type name="StorageBox_DGunCase_Brown">

	</type>
	<type name="StorageBox_DGunCase_Grey">

	</type>
	<type name="StorageBox_DGunCase_Cherry">

	</type>
	<type name="StorageBox_GunWall_Black">

	</type>
	<type name="StorageBox_GunWall_Tan ">

	</type>
	<type name="StorageBox_GunWall_Green">

	</type>
	<type name=" StorageBox_IceBox   ">

	</type>
	<type name="StorageBox_Locker_Black ">

	</type>
	<type name="StorageBox_Locker_Green ">

	</type>
	<type name="StorageBox_Locker_Purple ">

	</type>
	<type name="StorageBox_Locker_Blue ">

	</type>
	<type name="StorageBox_Locker_Red ">

	</type>
	<type name="StorageBox_Locker_White ">

	</type>
	<type name="StorageBox_Locker_Yellow ">

	</type>
	<type name="StorageBox_MetalRack_Black ">

	</type>
	<type name="StorageBox_MetalRack_Tan">

	</type>
	<type name="StorageBox_MetalRack_Green">

	</type>
	<type name=" StorageBox_HotDog_Cart ">

	</type>
	<type name="StorageBox_Mcabinet_Black">

	</type>
	<type name=" StorageBox_Mcabinet_Tan">

	</type>
	<type name="StorageBox_Mcabinet_Green">

	</type>
	<type name="StorageBox_Mlocker_Black">

	</type>
	<type name="StorageBox_Mlocker_Grey ">

	</type>
	<type name="StorageBox_Mlocker_Tan ">

	</type>
	<type name="StorageBox_Mlocker_Green ">

	</type>
	<type name="StorageBox_Mlocker_Blue ">

	</type>
	<type name="StorageBox_Safe_Black ">

	</type>
	<type name="StorageBox_Safe_Green ">

	</type>
	<type name="StorageBox_Safe_Grey ">

	</type>
	<type name="StorageBox_Safe_Blue ">

	</type>
	<type name="StorageBox_Safe_Gold ">

	</type>
	<type name="StorageBox_Safe_White ">

	</type>
	<type name="StorageBox_Safe_Rainbow ">

	</type>
	<type name="StorageBox_SmallSafe_Black">

	</type>
	<type name="StorageBox_SmallSafe_Green ">

	</type>
	<type name="StorageBox_SmallSafe_Grey ">

	</type>
	<type name="StorageBox_SmallSafe_Blue ">

	</type>
	<type name="StorageBox_SmallSafe_Gold ">

	</type>
	<type name="StorageBox_SmallSafe_White ">

	</type>
	<type name="StorageBox_SmallSafe_Rainbow ">

	</type>
	<type name="StorageBox_BigSafe_Black ">

	</type>
	<type name="StorageBox_BigSafe_Grey ">

	</type>
	<type name="StorageBox_BigSafe_Rainbow ">

	</type>
	<type name="StorageBox_ToolB_Black ">

	</type>
	<type name="StorageBox_ToolB_Red ">

	</type>
	<type name="StorageBox_ToolB_White ">

	</type>
	<type name="StorageBox_ToolB_Blue ">

	</type>
	<type name="StorageBox_ToolB_Yellow ">

	</type>
	<type name="StorageBox_Wood_Crate ">

	</type>
	<type name="StorageBox_Pallet_Black ">

	</type>
	<type name="StorageBox_Pallet_Tan ">

	</type>
	<type name="StorageBox_Pallet_Green">

	</type>
	<type name="StorageBox_HeliPad ">

	</type>
	<type name="StorageBox_MiliCrate_Black ">

	</type>
	<type name="StorageBox_MiliCrate_Grey ">

	</type>
	<type name="StorageBox_MiliCrate_Tan ">

	</type>
	<type name="StorageBox_MiliCrate_Green ">

	</type>
	<type name="StorageBox_MiliCrate_Blue ">

	</type>
	<type name="StorageBox_Fridge_Black ">

	</type>
	<type name="StorageBox_Fridge_White ">

	</type>
	<type name="StorageBox_Container_Black ">

	</type>
	<type name="StorageBox_Container_Grey ">

	</type>
	<type name="StorageBox_Container_Tan ">

	</type>
	<type name="StorageBox_Container_Green ">

	</type>
	<type name="StorageBox_Container_Blue ">

	</type>
	<type name="StorageBox_Container_Red ">

	</type>
	<type name="StorageBox_IC_Freezer ">

	</type>
	<type name="StorageBox_GearStand ">

	</type>
	<type name="StorageBox_PlanterBox ">

	</type>
	<type name="StorageBox_P_GreenHouse">

	</type>
	<type name="StorageBox_LargeGreen House">

	</type>
	<type name="StorageBox_Adoor_Black ">

	</type>
	<type name="StorageBox_Adoor_Gold ">

	</type>
	<type name="StorageBox_Adoor_Green ">

	</type>
	<type name="StorageBox_Adoor_Blue ">

	</type>
	<type name="StorageBox_Adoor_Rainbow ">

	</type>
	<type name="StorageBox_Rdoor_Black ">

	</type>
	<type name="StorageBox_Rdoor_Green ">

	</type>
	<type name="StorageBox_GearStandC_B ">

	</type>
	<type name="StorageBox_GearStandC_G ">

	</type>
	<type name="StorageBox_GearStandC_C ">

	</type>
	<type name="StorageBox_Wlocker_Black ">

	</type>
	<type name="StorageBox_Wlocker_Grey ">

	</type>
	<type name="StorageBox_Wlocker_Green ">

	</type>
	<type name="StorageBox_Wlocker_Tan ">

	</type>
	<type name="StorageBox_Wlocker_Blue ">

	</type>
	<type name="StorageBox_Bdoor ">

	</type>
	<type name="StorageBox_Compound_Gate">

	</type>
	<type name="StorageBox_Compound_Wall">

	</type>
	<type name="StorageBox_BigTent_Black">

	</type>
	<type name="StorageBox_BigTent_White">

	</type>
	<type name="StorageBox_BigTent_Green">

	</type>
	<type name="StorageBox_Dumpster">

	</type>
	<type name="StorageBox_GraffitiCan">

	</type>
	<type name="StorageBox_TrashCan">

	</type>
	<type name="StorageBox_WoodStorage">

	</type>
	<type name="StorageBox_WallRack_Black">

	</type>
	<type name="StorageBox_WallRack_Green">

	</type>
	<type name="StorageBox_WallRack_Tan">

	</type>
	<type name="StorageBox_Weapons_Rack_Black">

	</type>
	<type name="StorageBox_Weapons_Rack_Green">

	</type>
	<type name="StorageBox_Weapons_Rack_Tan">

	</type>
	<type name="Paragon_TínyCrate_Black">

	</type>
	<type name="Paragon_TinyCrate_Grey">

	</type>
	<type name="Paragon_TinyCrate_Tan">

	</type>
	<type name="Paragon_TinyCrate_Blue">

	</type>
	<type name="Paragon_ToolB_Black">

	</type>
	<type name="Paragon_ToolB_Red">

	</type>
	<type name="Paragon_ToolB_White">

	</type>
	<type name="Paragon_ToolB_Blue">

	</type>
	<type name="Paragon_ToolB_Yellow">

	</type>
	<type name="Paragon_TrashCan">

	</type>
	<type name="Paragon_TrashCan_Static">

	</type>
	<type name="Paragon_WallRack_Black">

	</type>
	<type name="Paragon_WallRack_Green">

	</type>
	<type name="Paragon_WallRack_Tan">

	</type>
	<type name="Paragon_WaterBarrel_Blue">

	</type>
	<type name="Paragon_WaterBarrel_Black">

	</type>
	<type name="Paragon_WaterBarrel_Green">

	</type>
	<type name="Paragon_WaterBarrel_Yellow">

	</type>
	<type name="Paragon_WaterBarrel_Red">

	</type>
	<type name="Paragon_Wlocker_Black">

	</type>
	<type name="Paragon_Wlocker_Grey">

	</type>
	<type name="Paragon_Wlocker_Green">

	</type>
	<type name="Paragon_Wlocker_Tan">

	</type>
	<type name="Paragon_Wlocker_Blue">

	</type>
	<type name="Paragon_Weapons_Rack_Black">

	</type>
	<type name="Paragon_Weapons_Rack_Green">

	</type>
	<type name="Paragon_Weapons_Rack_Tan">

	</type>
	<type name="Paragon_Wood_Crate">
	</type>
</spawnabletypes>