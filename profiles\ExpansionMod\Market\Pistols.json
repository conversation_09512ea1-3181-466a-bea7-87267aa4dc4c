{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_PISTOLS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "makarovij70", "MaxPriceThreshold": 785, "MinPriceThreshold": 470, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_ij70_8rnd"], "Variants": []}, {"ClassName": "derringer_black", "MaxPriceThreshold": 610, "MinPriceThreshold": 365, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["derringer_grey", "derringer_pink"]}, {"ClassName": "cz75", "MaxPriceThreshold": 1230, "MinPriceThreshold": 735, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_cz75_15rnd"], "Variants": []}, {"ClassName": "fnx45", "MaxPriceThreshold": 7845, "MinPriceThreshold": 4705, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_fnx45_15rnd"], "Variants": []}, {"ClassName": "glock19", "MaxPriceThreshold": 850, "MinPriceThreshold": 510, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_glock_15rnd"], "Variants": []}, {"ClassName": "mkii", "MaxPriceThreshold": 1040, "MinPriceThreshold": 625, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_mkii_10rnd"], "Variants": []}, {"ClassName": "colt1911", "MaxPriceThreshold": 5345, "MinPriceThreshold": 3210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_1911_7rnd"], "Variants": []}, {"ClassName": "engraved1911", "MaxPriceThreshold": 5390, "MinPriceThreshold": 3235, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_1911_7rnd"], "Variants": []}, {"ClassName": "magnum", "MaxPriceThreshold": 390, "MinPriceThreshold": 235, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>le", "MaxPriceThreshold": 375, "MinPriceThreshold": 225, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_deagle_9rnd"], "Variants": ["deagle_gold"]}, {"ClassName": "deagle_gold", "MaxPriceThreshold": 400, "MinPriceThreshold": 240, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_deagle_9rnd"], "Variants": []}, {"ClassName": "p1", "MaxPriceThreshold": 865, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_p1_8rnd"], "Variants": []}, {"ClassName": "longhorn", "MaxPriceThreshold": 3270, "MinPriceThreshold": 1960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_m9", "MaxPriceThreshold": 7800, "MinPriceThreshold": 4600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["mag_expansion_m9_15rnd"], "Variants": []}]}