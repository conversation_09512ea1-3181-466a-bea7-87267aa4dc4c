=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-20_5-2-39
====================================================================================
5:2:39 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
5:2:39 | [PermissionManager] Perm (FreeCamera) has been registered!
5:2:39 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
5:2:39 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
5:2:39 | [PermissionManager] Perm (MenuServerManager) has been registered!
5:2:39 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
5:2:39 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
5:2:39 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
5:2:39 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
5:2:39 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
5:2:39 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
5:2:39 | [PermissionManager] Perm (MenuObjectManager) has been registered!
5:2:39 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
5:2:39 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
5:2:39 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
5:2:39 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
5:2:39 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
5:2:39 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
5:2:39 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
5:2:39 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
5:2:39 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
5:2:39 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
5:2:39 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
5:2:39 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
5:2:39 | [PermissionManager] Perm (MenuBansManager) has been registered!
5:2:39 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
5:2:39 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
5:2:39 | [PermissionManager] Perm (MenuWebHooks) has been registered!
5:2:39 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
5:2:39 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
5:2:39 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
5:2:39 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
5:2:39 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
5:2:39 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
5:2:39 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
5:2:39 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
5:2:39 | [PermissionManager] Adding Super Admin (76561199239979485)
5:2:39 | [PermissionManager] Adding Super Admin (76561197999250250)
5:2:39 | [PermissionManager] Adding Super Admin (76561198153347717)
5:2:39 | [PermissionManager] Loaded UserGroups.json
5:2:39 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
5:2:39 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
5:2:39 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
5:2:39 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
5:2:39 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
5:2:39 | Building Sets Loaded: 0
5:2:39 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
7:38:8 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
7:39:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9853.693359, 232.065552, 7258.746094>)
7:39:15 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
7:39:23 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9890.365234, 225.459961, 7222.487793>)
7:40:12 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9806.334961, 236.885056, 7248.281738>)
7:44:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9865.121094, 230.035172, 7248.705078>)
7:45:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9863.321289, 221.387177, 7343.355469>)
7:45:14 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9889.590820, 223.954056, 7329.011230>)
7:45:28 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9863.633789, 225.134918, 7305.109375>)
7:46:6 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9860.877930, 229.484741, 7294.151367>)
7:46:46 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9846.415039, 230.035172, 7263.432129>)
7:47:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9806.734375, 236.954956, 7250.918945>)
7:47:13 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9807.764648, 236.898285, 7249.609375>)
7:47:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9847.213867, 227.799042, 7260.004395>)
7:48:4 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9855.890625, 231.641815, 7255.970703>)
7:48:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9855.947266, 233.075104, 7255.899414>)
7:48:12 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9852.329102, 227.113235, 7253.541992>)
7:48:18 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9855.778320, 231.411270, 7256.112305>)
7:48:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9856.042969, 232.694977, 7255.778809>)
7:48:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9861.638672, 230.035172, 7253.169434>)
7:48:34 | "BNeaveill99" (steamid=76561198153347717) deleted object "Paragon_XR80_White" (pos=<9866.773438, 230.085541, 7252.635742>)
7:48:39 | "BNeaveill99" (steamid=76561198153347717) deleted object "Paragon_XR80_Black" (pos=<9866.773438, 230.105057, 7252.635742>)
7:49:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9823.453125, 225.804077, 7189.058594>)
7:49:35 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9820.017578, 227.246384, 7172.504395>)
7:49:42 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9801.480469, 226.966583, 7173.419922>)
7:49:55 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9772.787109, 223.891922, 7169.303223>)
7:52:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9689.291016, 220.134933, 7154.214844>)
7:52:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9676.952148, 220.149918, 7152.460449>)
7:52:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9667.091797, 220.153152, 7146.557129>)
7:52:21 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9659.670898, 220.145111, 7137.670410>)
7:52:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9682.234375, 220.205719, 7160.465332>)
7:52:24 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9692.165039, 220.180344, 7171.301758>)
7:52:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9707.562500, 220.187607, 7183.601074>)
7:52:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9723.685547, 220.134903, 7196.170410>)
7:52:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9738.971680, 220.134933, 7208.025879>)
7:52:26 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9757.094727, 220.134918, 7214.879395>)
7:52:27 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9778.947266, 220.134903, 7217.475098>)
7:52:27 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9793.981445, 220.134918, 7216.804688>)
7:52:28 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9811.839844, 220.134933, 7211.633301>)
7:52:28 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9823.541016, 220.316467, 7209.490723>)
7:52:29 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9834.801758, 220.134918, 7210.960449>)
7:52:29 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9844.998047, 220.134903, 7216.378906>)
7:52:30 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9853.744141, 220.134933, 7224.134277>)
7:52:30 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9860.864258, 220.282761, 7230.811523>)
7:52:31 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9869.085938, 220.134918, 7238.844238>)
7:52:31 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9878.794922, 220.134888, 7246.827148>)
7:52:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9885.219727, 220.134918, 7254.129883>)
7:52:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9875.277344, 220.134918, 7273.398438>)
7:52:35 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9864.571289, 220.134903, 7285.389648>)
7:55:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9914.414063, 220.134903, 7249.769043>)
7:55:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9919.225586, 220.168579, 7239.024902>)
7:55:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9920.518555, 221.270432, 7208.196289>)
7:55:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9918.478516, 221.623749, 7195.394043>)
7:55:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9915.975586, 222.018326, 7183.128906>)
7:55:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9915.855469, 222.636490, 7171.564941>)
7:55:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9916.005859, 223.391708, 7160.782715>)
7:55:35 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9919.013672, 224.165222, 7155.020020>)
7:55:35 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9925.856445, 224.903763, 7150.050293>)
7:55:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9934.249023, 225.401428, 7144.841309>)
7:55:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9940.112305, 226.441940, 7140.316895>)
7:55:37 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9945.305664, 227.554214, 7135.550781>)
7:55:37 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9950.272461, 228.588623, 7130.625000>)
7:55:38 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9955.098633, 229.427292, 7125.964844>)
7:55:39 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<9959.442383, 230.025940, 7121.823242>)
7:56:46 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GAZ_66)
7:58:48 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GAZ_66)
7:59:23 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
7:59:25 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
