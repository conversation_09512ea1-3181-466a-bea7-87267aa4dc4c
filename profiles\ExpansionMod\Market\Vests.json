{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_VESTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "reflexvest", "MaxPriceThreshold": 870, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "policevest", "MaxPriceThreshold": 1100, "MinPriceThreshold": 660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pressvest_blue", "MaxPriceThreshold": 4205, "MinPriceThreshold": 2525, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["pressvest_lightblue"]}, {"ClassName": "pressvest_lightblue", "MaxPriceThreshold": 4205, "MinPriceThreshold": 2525, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ukassvest_black", "MaxPriceThreshold": 1710, "MinPriceThreshold": 1025, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ukassvest_khaki", "ukassvest_olive", "ukassvest_camo", "ukassvest_winter"]}, {"ClassName": "ukassvest_camo", "MaxPriceThreshold": 1770, "MinPriceThreshold": 1060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ukassvest_winter", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "smershvest", "MaxPriceThreshold": 9150, "MinPriceThreshold": 5490, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "highcapacityvest_black", "MaxPriceThreshold": 11845, "MinPriceThreshold": 7105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["highcapacityvest_olive"]}, {"ClassName": "highcapacityvest_olive", "MaxPriceThreshold": 13055, "MinPriceThreshold": 7830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "platecarriervest", "MaxPriceThreshold": 12045, "MinPriceThreshold": 7230, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["platecarriervest_green", "platecarriervest_black", "platecarriervest_camo", "platecarriervest_winter"]}, {"ClassName": "platecarriervest_camo", "MaxPriceThreshold": 11845, "MinPriceThreshold": 7105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "platecarriervest_winter", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingvest", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["huntingvest_winter"]}, {"ClassName": "chestplate", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}