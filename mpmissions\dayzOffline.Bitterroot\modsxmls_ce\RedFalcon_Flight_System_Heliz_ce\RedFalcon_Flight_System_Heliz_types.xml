<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<types>
	<!--RedFalcon_Flight_System_Heliz-->
    <type name="RFFSHeli_Katran_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_hoodie">
        <nominal>5</nominal>
        <lifetime>14400</lifetime>
        <restock>0</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Town"/>
        <usage name="Village"/>
    </type>
    <type name="RFFSHeli_hoodie_black">
        <nominal>5</nominal>
        <lifetime>14400</lifetime>
        <restock>0</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Town"/>
        <usage name="Village"/>
    </type>
    <type name="RFFSHeli_Flag_RedFalcon">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>0</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="1" count_in_hoarder="1" count_in_map="1" count_in_player="1" crafted="0" deloot="0"/>
        <category name="tools"/>
        <usage name="Office"/>
        <usage name="School"/>
	<!--	DontUse
    </type>
    <type name="RFFSHeli_Osprey_Static">
        <nominal>0</nominal>
        <lifetime>0</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
	-->
    </type>
    <type name="RFFSHeli_PilotHelmet">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotHelmet_Black">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotHelmet_Desert">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotGloves">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotShirt">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotPants">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_PilotVest">
        <nominal>2</nominal>
        <lifetime>14400</lifetime>
        <restock>600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="clothes"/>
        <usage name="Military"/>
    </type>
    <type name="RFFSHeli_hydraulic_fluid">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>50</quantmin>
        <quantmax>100</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_hydraulic_hoses">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
	<!--	DontUse
    </type>
    <type name="RFFSHeli_Vehicle_Container">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools"/>
	-->
    </type>
    <type name="RFFSHeli_wiring_harness">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_igniter_plug">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_aviation_battery">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>50</quantmin>
        <quantmax>100</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_avionics_unit">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_aviation_toolbox">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>50</quantmin>
        <quantmax>100</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_flight_case">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>	
    <type name="RFFSHeli_flight_case_red">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>	
    <type name="RFFSHeli_flight_case_blue">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_batterycharger">
        <nominal>3</nominal>
        <lifetime>28800</lifetime>
        <restock>3600</restock>
        <min>1</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="tools" />
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo3">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo4">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_EC135">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_EC135_BlackCamo">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_EC135_DriverDoor_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_CodriverDoor_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo1_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo2_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo3_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_Cargo4_BlackCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_EC135_BlackCamo_Wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Ka26_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Ka26_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Ka26_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Ka26_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Ka26_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Ka26">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_LittleBird_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_LittleBird">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_LittleBird_Camo">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_LittleBird_Desert">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2_Hornet">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2_DriverDoor_Hornet">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_CodriverDoor_Hornet">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_Cargo1_Hornet">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_Hornet_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2_Military">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Mi2_DriverDoor_Military">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_CodriverDoor_Military">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_Cargo1_Military">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Mi2_Military_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22_Black">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22_DriverDoor_Black">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_CodriverDoor_Black">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_Black_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22_Red">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_R22_DriverDoor_Red">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_CodriverDoor_Red">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_R22_Red_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_S76_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_S76">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_S76_WoodlandCamo">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_S76_DriverDoor_WoodlandCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_CodriverDoor_WoodlandCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_Cargo1_WoodlandCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_Cargo2_WoodlandCamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_S76_WoodlandCamo_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_UH1H_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Cargo1a">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Cargo2a">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_UH1H">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_UH1H_Combat_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_UH1H_Combat_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Combat_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_UH1H_Combat">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache_Desert">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache_DriverDoor_Desert">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_CodriverDoor_Desert">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_Desert_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache_Winter">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Apache_DriverDoor_Winter">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_CodriverDoor_Winter">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Apache_Winter_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_AS350_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_AS350_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_AS350_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_AS350_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_AS350_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_AS350">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo3">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo4">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_Medic">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_DriverDoor_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_CodriverDoor_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo1_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo2_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo3_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo4_Medic">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Medic_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_Police">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_DriverDoor_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_CodriverDoor_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo1_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo2_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo3_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo4_Police">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Police_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_Uganda">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_DriverDoor_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_CodriverDoor_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo1_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo2_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo3_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo4_Uganda">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Uganda_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_ZS">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bell429_DriverDoor_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_CodriverDoor_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo1_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo2_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo3_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_Cargo4_ZS">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bell429_ZS_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1a">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2a">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo3">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo4">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_DriverDoor_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_CodriverDoor_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1a_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2a_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo3_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo4_blackblackhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_blackblackhawk_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Budhawk">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_DriverDoor_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_CodriverDoor_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1a_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2a_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo3_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo4_Budhawk">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Budhawk_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Woodland">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Blackhawk_DriverDoor_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_CodriverDoor_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo1a_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo2a_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo3_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Cargo4_Woodland">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Blackhawk_Woodland_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bo105m_DriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_CodriverDoor">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo3">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo4">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>		
    <type name="RFFSHeli_Bo105m">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>		
    <type name="RFFSHeli_Bo105m_blackcamo">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_Bo105m_DriverDoor_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_CodriverDoor_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo1_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo2_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo3_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_Cargo4_blackcamo">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_Bo105m_blackcamo_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_CH47_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_CH47_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_CH47_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_CH47">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_CH53e_Cargo1">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
    </type>
    <type name="RFFSHeli_CH53e_Cargo2">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>14400</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
        <category name="vehiclesparts"/>
        <tag name="floor"/>
        <usage name="Industrial"/>
			<!--	DontUse
    </type>
    <type name="RFFSHeli_CH53e_Tailgate_Blocker">
        <nominal>0</nominal>
        <lifetime>28800</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
		-->
    </type>
    <type name="RFFSHeli_CH53e_wreck">
        <nominal>0</nominal>
        <lifetime>1550</lifetime>
        <restock>0</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>
    <type name="RFFSHeli_CH53e">
        <nominal>0</nominal>
        <lifetime>3888000</lifetime>
        <restock>1800</restock>
        <min>0</min>
        <quantmin>-1</quantmin>
        <quantmax>-1</quantmax>
        <cost>100</cost>
        <flags count_in_cargo="0" count_in_hoarder="0" count_in_map="1" count_in_player="0" crafted="0" deloot="0"/>
    </type>				
</types>
