{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_ELECTRONICS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "personalradio", "MaxPriceThreshold": 1180, "MinPriceThreshold": 710, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "megaphone", "MaxPriceThreshold": 5050, "MinPriceThreshold": 3030, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "electronicrepairkit", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cablereel", "MaxPriceThreshold": 915, "MinPriceThreshold": 550, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "batterycharger", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseradio", "MaxPriceThreshold": 16160, "MinPriceThreshold": 9695, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["carbattery"], "Variants": []}, {"ClassName": "rangefinder", "MaxPriceThreshold": 1355, "MinPriceThreshold": 815, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "nvgoggles", "MaxPriceThreshold": 9025, "MinPriceThreshold": 5415, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "alarmclock_blue", "MaxPriceThreshold": 615, "MinPriceThreshold": 370, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["alarmc<PERSON>_green", "alarmclock_red"]}, {"ClassName": "kitchentimer", "MaxPriceThreshold": 475, "MinPriceThreshold": 285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}