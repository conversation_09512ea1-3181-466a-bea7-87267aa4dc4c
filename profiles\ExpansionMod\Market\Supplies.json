{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_SUPPLIES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "paper", "MaxPriceThreshold": 4, "MinPriceThreshold": 2, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pen_black", "MaxPriceThreshold": 570, "MinPriceThreshold": 340, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["pen_blue", "pen_green", "pen_red"]}, {"ClassName": "rope", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "metalwire", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "epoxyputty", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ducttape", "MaxPriceThreshold": 545, "MinPriceThreshold": 330, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pot", "MaxPriceThreshold": 475, "MinPriceThreshold": 285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "handcuffkeys", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "handcuffs", "MaxPriceThreshold": 1025, "MinPriceThreshold": 615, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "netting", "MaxPriceThreshold": 830, "MinPriceThreshold": 495, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "burlapsack", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woodenplank", "MaxPriceThreshold": 250, "MinPriceThreshold": 50, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "metalplate", "MaxPriceThreshold": 505, "MinPriceThreshold": 305, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "smallgascanister", "MaxPriceThreshold": 210, "MinPriceThreshold": 125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mediumgascanister", "MaxPriceThreshold": 210, "MinPriceThreshold": 125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "largegascanister", "MaxPriceThreshold": 210, "MinPriceThreshold": 125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tripod", "MaxPriceThreshold": 895, "MinPriceThreshold": 540, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "beartrap", "MaxPriceThreshold": 1490, "MinPriceThreshold": 895, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bear_beige", "MaxPriceThreshold": 355, "MinPriceThreshold": 215, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["bear_dark", "bear_pink", "bear_white"]}, {"ClassName": "nailbox", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "nail", "MaxPriceThreshold": 10, "MinPriceThreshold": 5, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "barbedwire", "MaxPriceThreshold": 825, "MinPriceThreshold": 495, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "fabric", "MaxPriceThreshold": 500, "MinPriceThreshold": 300, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "camonet", "MaxPriceThreshold": 2100, "MinPriceThreshold": 1260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hescobox", "MaxPriceThreshold": 2250, "MinPriceThreshold": 1300, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "powergenerator", "MaxPriceThreshold": 455, "MinPriceThreshold": 275, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionlumber1", "MaxPriceThreshold": 300, "MinPriceThreshold": 50, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionlumber1_5", "MaxPriceThreshold": 300, "MinPriceThreshold": 50, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionlumber3", "MaxPriceThreshold": 300, "MinPriceThreshold": 50, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}