=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-26_23-51-56
====================================================================================
23:51:56 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:51:56 | [PermissionManager] Perm (FreeCamera) has been registered!
23:51:56 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:51:56 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:51:56 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:51:56 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:51:56 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:51:56 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:51:56 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:51:56 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:51:56 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:51:56 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:51:56 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:51:56 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:51:56 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:51:56 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:51:56 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:51:56 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:51:56 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:51:56 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:51:56 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:51:56 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:51:56 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:51:56 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:51:56 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:51:56 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:51:56 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:51:56 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:51:56 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:51:56 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:51:56 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:51:56 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:51:56 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:51:56 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:51:56 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:51:56 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:51:56 | [PermissionManager] Adding Super Admin (76561199239979485)
23:51:56 | [PermissionManager] Adding Super Admin (76561197999250250)
23:51:56 | [PermissionManager] Adding Super Admin (76561198153347717)
23:51:56 | [PermissionManager] Loaded UserGroups.json
23:51:56 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:51:56 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:51:56 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:51:56 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:51:56 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:51:56 | Building Sets Loaded: 0
23:51:56 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
0:7:16 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
0:7:34 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
0:7:58 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10466.750977, 128.473236, 1020.452637>)
0:7:59 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10484.741211, 129.848831, 1040.717041>)
0:7:59 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10502.822266, 129.436661, 1059.489258>)
0:8:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10519.752930, 129.080521, 1077.021606>)
0:8:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10532.628906, 129.081619, 1089.285889>)
0:8:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10546.395508, 129.405472, 1103.292969>)
0:8:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10558.389648, 129.948593, 1114.473877>)
0:8:2 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10568.569336, 130.704117, 1123.812622>)
0:8:3 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10579.918945, 131.464615, 1133.857178>)
0:8:3 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10593.160156, 131.908737, 1145.504028>)
0:8:4 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10605.064453, 132.294617, 1156.076660>)
0:8:5 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10616.290039, 132.411835, 1167.379883>)
0:8:5 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10628.304688, 131.754059, 1180.217407>)
0:8:6 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10640.022461, 131.053436, 1192.635620>)
0:8:15 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10601.461914, 131.002914, 1203.285767>)
0:8:16 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10585.712891, 131.106323, 1204.134644>)
0:8:16 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10572.725586, 131.452957, 1204.835938>)
0:8:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10552.390625, 132.496979, 1190.095459>)
0:8:20 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10550.224609, 132.796097, 1185.027954>)
0:13:3 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GCGN_Ammo_408Chey)
0:13:3 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GCGN_Ammo_408Chey)
0:13:13 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GCGN_Ammo_408Chey)
0:16:3 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:16:4 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:16:4 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:16:4 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:16:5 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:16:5 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_SRS_10Rnd)
0:17:43 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10613.917969, 130.477600, 1116.221924>)
0:17:55 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10582.203125, 144.888962, 1169.875366>)
0:18:10 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10626.896484, 131.667068, 1154.294067>)
0:19:29 | "BNeaveill99" (steamid=76561198153347717) /heal used on: "BNeaveill99" (steamid=76561198153347717)
0:19:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10644.040039, 171.322525, 1223.397949>)
0:22:20 | Player "Survivor" (steamId=76561199239979485) connected to server!
0:23:38 | "76561199239979485" (steamid=76561199239979485) teleported to (pos=<9953.690430, 228.383423, 7138.319824>)
0:23:38 | "Survivor": (steamid=76561199239979485) teleported to (pos=9953.69,0,7138.32)
0:24:2 | "Survivor" (steamid=76561199239979485) teleported to crosshair (pos=<9869.152344, 220.135590, 7161.206543>)
0:25:10 | Player "Survivor" (steamId=76561199239979485) initiated disconnect process...
0:25:30 | Player "Survivor" (steamId=76561199239979485) disconnected from server.
0:35:40 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
0:35:41 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
