=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-22_17-2-53
====================================================================================
17:2:53 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
17:2:53 | [PermissionManager] Perm (FreeCamera) has been registered!
17:2:53 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
17:2:53 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
17:2:53 | [PermissionManager] Perm (MenuServerManager) has been registered!
17:2:53 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
17:2:53 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
17:2:53 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
17:2:53 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
17:2:53 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
17:2:53 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
17:2:53 | [PermissionManager] Perm (MenuObjectManager) has been registered!
17:2:53 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
17:2:53 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
17:2:53 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
17:2:53 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
17:2:53 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
17:2:53 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
17:2:53 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
17:2:53 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
17:2:53 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
17:2:53 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
17:2:53 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
17:2:53 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
17:2:53 | [PermissionManager] Perm (MenuBansManager) has been registered!
17:2:53 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
17:2:53 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
17:2:53 | [PermissionManager] Perm (MenuWebHooks) has been registered!
17:2:53 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
17:2:53 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
17:2:53 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
17:2:53 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
17:2:53 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
17:2:53 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
17:2:53 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
17:2:53 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
17:2:53 | [PermissionManager] Adding Super Admin (76561199239979485)
17:2:53 | [PermissionManager] Adding Super Admin (76561197999250250)
17:2:53 | [PermissionManager] Adding Super Admin (76561198153347717)
17:2:53 | [PermissionManager] Loaded UserGroups.json
17:2:53 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
17:2:53 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
17:2:53 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
17:2:53 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
17:2:53 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
17:2:53 | Building Sets Loaded: 0
17:2:53 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
17:18:23 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
17:18:29 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
17:18:32 | [WeatherManager] Send Data Count is 0
17:23:53 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
17:24:2 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
17:44:56 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
17:45:3 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
17:48:37 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GAZ_66)
17:48:50 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (GAZ_66_zima_camo)
17:50:3 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_BlackAdminBag)
17:51:0 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (BarrelHoles_Blue)
17:51:13 | "BNeaveill99" (steamid=76561198153347717) deleted object "BarrelHoles_Blue" (pos=<5921.058594, 305.967834, 11133.147461>)
17:51:28 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_WolfBarrel)
17:51:56 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Barrel_Red)
17:52:8 | "BNeaveill99" (steamid=76561198153347717) deleted object "Barrel_Red" (pos=<5920.596680, 305.933136, 11134.086914>)
17:52:39 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_TRexBarrel)
17:53:34 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_MediumCrate_Black)
17:53:55 | "BNeaveill99" (steamid=76561198153347717) deleted object "Paragon_MediumCrate_Black" (pos=<5920.320313, 305.994965, 11133.258789>)
17:54:26 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (MD_DarkWoodenCrate)
17:54:53 | "BNeaveill99" (steamid=76561198153347717) deleted object "MD_DarkWoodenCrate" (pos=<5920.770020, 306.256073, 11133.079102>)
17:55:11 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_MediumCrate_Black)
17:56:24 | "BNeaveill99" (steamid=76561198153347717) deleted object "Paragon_MediumCrate_Black" (pos=<5919.795898, 305.941620, 11133.791016>)
17:56:39 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
17:56:46 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
17:56:47 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
17:56:48 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
17:57:33 | "BNeaveill99" (steamid=76561198153347717) deleted object "WoodenCrate" (pos=<5923.556641, 305.903046, 11135.306641>)
17:57:47 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (ExpansionCarKey)
18:7:11 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
18:7:16 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
