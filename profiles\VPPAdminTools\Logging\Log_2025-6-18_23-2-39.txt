=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-18_23-2-39
====================================================================================
23:2:39 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:2:39 | [PermissionManager] Perm (FreeCamera) has been registered!
23:2:39 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:2:39 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:2:39 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:2:39 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:2:39 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:2:39 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:2:39 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:2:39 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:2:39 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:2:39 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:2:39 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:2:39 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:2:39 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:2:39 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:2:39 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:2:39 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:2:39 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:2:39 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:2:39 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:2:39 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:2:39 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:2:39 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:2:39 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:2:39 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:2:39 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:2:39 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:2:39 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:2:39 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:2:39 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:2:39 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:2:39 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:2:39 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:2:39 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:2:39 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:2:39 | [PermissionManager] Adding Super Admin (76561199239979485)
23:2:39 | [PermissionManager] Adding Super Admin (76561197999250250)
23:2:39 | [PermissionManager] Adding Super Admin (76561198153347717)
23:2:39 | [PermissionManager] Loaded UserGroups.json
23:2:39 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:2:39 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:2:39 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:2:39 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:2:39 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:2:39 | Building Sets Loaded: 0
23:2:39 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
0:15:7 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
0:34:58 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<6335.569824, 316.272736, 2985.639893>)
0:34:58 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=6335.57,0,2985.64)
0:39:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6458.151367, 318.005829, 2951.253906>)
0:39:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
0:39:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Expansion_Landrover_Green" (pos=<6477.915527, 311.820587, 2962.579590>)
0:39:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6518.895996, 298.762665, 2969.398193>)
0:39:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6537.338379, 292.643860, 2974.957031>)
0:39:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6543.169922, 292.355194, 2977.460938>)
0:39:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6669.091309, 256.705872, 3023.856934>)
0:40:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6669.929688, 256.503784, 3023.270752>)
0:41:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6674.228027, 254.521820, 3051.908691>)
0:41:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6672.703125, 256.156982, 3045.545898>)
0:41:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6671.819336, 256.195953, 3033.152588>)
0:41:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6668.176758, 256.903107, 3023.731689>)
0:43:37 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<943.833984, 699.559937, 2511.810059>)
0:43:37 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=943.834,0,2511.81)
0:44:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
0:51:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (HatchbackWheel)
0:51:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (HatchbackWheel)
0:51:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CarRadiator)
0:52:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
0:54:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<880.643188, 693.814331, 2452.056885>)
1:0:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1067.597046, 559.769470, 734.188232>)
1:0:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1079.651489, 550.300964, 733.101257>)
1:0:11 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1076.144043, 550.318176, 735.905457>)
1:0:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1072.536987, 552.566833, 739.744690>)
1:0:49 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1059.420044, 579.027954, 879.638000>)
1:0:49 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1059.42,0,879.638)
1:1:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
1:2:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1246.927124, 587.923584, 539.994263>)
1:2:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1152.580322, 543.810242, 690.837585>)
1:2:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1010.905396, 613.966980, 647.418030>)
1:2:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
1:2:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
1:2:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1132.411011, 543.810059, 731.529724>)
1:3:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1092.670654, 545.178223, 716.459229>)
1:3:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1086.778564, 547.716919, 710.435059>)
1:3:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1109.315674, 553.353333, 624.464783>)
1:3:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1832.022583, 1283.872925, 231.637115>)
1:3:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1711.051025, 591.127991, 473.718079>)
1:4:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_DarkCamoGorkaPants)
1:5:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamo_2GorkaJacket)
1:6:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamo_2GhillieArmband)
1:7:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminProtectorCase)
1:7:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminProtectorCase)
1:7:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_AdminFirstAidKit)
1:7:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (RenegadeAdmin_BlackTortillaBag)
1:8:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (WitchHat)
1:8:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "WitchHat" (pos=<1698.436401, 587.426819, 485.291565>)
1:8:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (WitchHood_Black)
1:9:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (NVGHeadstrap)
1:9:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_NVG_Blue_G)
1:9:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:9:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:9:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:10:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminJungleBoots)
1:10:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminGloves)
1:11:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_BodyBelt_Green)
1:12:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (VitaminBottle)
1:12:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_Bandage)
1:12:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_Bandage)
1:12:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
1:12:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
1:12:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
1:13:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Bottle_Desert)
1:13:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1743.534668, 584.118896, 522.596741>)
1:13:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1754.896973, 584.338928, 530.914124>)
1:13:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1778.287598, 585.471802, 556.972595>)
1:14:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1873.233887, 588.742554, 617.128723>)
1:14:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1884.060669, 584.026672, 609.529297>)
1:14:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_FoodCan_NatesCheese)
1:14:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_FoodCan_NatesCheese)
1:15:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Honey)
1:15:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SaltySticks)
1:15:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Crackers)
1:15:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Chips)
1:15:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (BakedBeansCan)
1:15:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:15:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:15:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:15:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan_Opened)
1:15:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SpaghettiCan)
1:15:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (PorkCan)
1:23:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:23:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Kvass)
1:24:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Fronta)
1:24:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_MooseLoose)
1:24:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_MooseLoose)
1:24:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_MooseLoose)
1:24:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_MooseLoose)
1:24:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_MooseLoose)
1:24:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MF_Beer_Fudweiser)
1:29:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2012.430176, 589.932983, 745.245911>)
1:29:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2025.012695, 584.363159, 768.838318>)
1:30:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (M1903Optic)
1:31:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_308Win_20Rnd)
1:31:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_308Win_20Rnd)
1:34:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2050.277588, 588.647827, 708.301880>)
1:34:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2058.654541, 584.299133, 737.505005>)
1:35:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2037.578979, 587.069702, 758.097717>)
1:36:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2014.875366, 590.113281, 749.937378>)
1:36:52 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1858.550049, 584.383606, 1092.689941>)
1:36:52 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1858.55,0,1092.69)
1:38:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (WeaponCleaningKit)
1:40:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1558.158203, 615.247131, 1341.232422>)
1:40:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1544.339233, 625.161377, 1380.944702>)
1:40:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1540.692993, 630.049255, 1398.937378>)
1:42:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1546.139160, 652.076599, 1546.175903>)
1:45:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1577.188354, 653.895264, 1599.705322>)
1:45:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1592.088623, 655.152344, 1597.188110>)
1:45:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1606.681030, 656.495300, 1594.926392>)
1:45:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1626.288208, 657.322937, 1600.884521>)
1:48:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1638.763672, 665.448486, 1729.841431>)
1:49:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1644.190063, 660.506042, 1729.327393>)
1:51:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1706.208740, 665.970276, 1697.589966>)
1:51:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1712.279541, 659.976685, 1740.791382>)
1:52:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1664.087158, 664.843750, 1781.208252>)
1:53:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:53:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_Camo)
1:54:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_Camo_Buttstock)
1:55:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_Camo_HuntingOptic)
2:0:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1680.471069, 655.994568, 1754.330933>)
2:0:29 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
