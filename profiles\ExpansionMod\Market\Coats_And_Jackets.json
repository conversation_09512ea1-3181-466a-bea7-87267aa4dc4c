{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_COATS & #STR_EXPANSION_MARKET_CATEGORY_JACKETS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "labcoat", "MaxPriceThreshold": 1065, "MinPriceThreshold": 640, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tracksuitjacket_black", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["tracksuitjacket_blue", "tracksuitjacket_green", "tracksuitjacket_lightblue", "tracksuitjacket_red"]}, {"ClassName": "tracksuitjacket_blue", "MaxPriceThreshold": 1060, "MinPriceThreshold": 635, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tracksuitjacket_green", "MaxPriceThreshold": 885, "MinPriceThreshold": 530, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tracksuitjacket_lightblue", "MaxPriceThreshold": 885, "MinPriceThreshold": 530, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tracksuitjacket_red", "MaxPriceThreshold": 1060, "MinPriceThreshold": 635, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "downjacket_blue", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["downjacket_green", "downjacket_orange", "downjacket_red"]}, {"ClassName": "denimjacket", "MaxPriceThreshold": 350, "MinPriceThreshold": 210, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_red", "MaxPriceThreshold": 1350, "MinPriceThreshold": 810, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["woolcoat_blue", "woolcoat_beige", "woolcoat_redcheck", "woolcoat_bluecheck", "woolcoat_greycheck", "woolcoat_browncheck", "woolcoat_black", "woolcoat_blackcheck", "woolcoat_green"]}, {"ClassName": "woolcoat_blue", "MaxPriceThreshold": 1675, "MinPriceThreshold": 1005, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_beige", "MaxPriceThreshold": 1910, "MinPriceThreshold": 1145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_redcheck", "MaxPriceThreshold": 1350, "MinPriceThreshold": 810, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_bluecheck", "MaxPriceThreshold": 1675, "MinPriceThreshold": 1005, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_greycheck", "MaxPriceThreshold": 1080, "MinPriceThreshold": 650, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_browncheck", "MaxPriceThreshold": 2410, "MinPriceThreshold": 1445, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_black", "MaxPriceThreshold": 2430, "MinPriceThreshold": 1460, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_blackcheck", "MaxPriceThreshold": 2410, "MinPriceThreshold": 1445, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woolcoat_green", "MaxPriceThreshold": 1690, "MinPriceThreshold": 1015, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ridersjacket_black", "MaxPriceThreshold": 1390, "MinPriceThreshold": 835, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionridersjacketbrown", "expansionridersjacketdarkblue"]}, {"ClassName": "firefighterjacket_beige", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["firefighterjacket_black"]}, {"ClassName": "firefighterjacket_black", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "jumpsuitjacket_blue", "MaxPriceThreshold": 3030, "MinPriceThreshold": 1815, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["jumpsuitjacket_gray", "jumpsuitjacket_green", "jumpsuitjacket_red"]}, {"ClassName": "jumpsuitjacket_gray", "MaxPriceThreshold": 1955, "MinPriceThreshold": 1170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "jumpsuitjacket_red", "MaxPriceThreshold": 1955, "MinPriceThreshold": 1170, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bomberjacket_brown", "MaxPriceThreshold": 1675, "MinPriceThreshold": 1005, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["bomberjacket_blue", "bomberjacket_grey", "bomberjacket_maroon", "bomberjacket_skyblue", "bomberjacket_black", "bomberjacket_olive"]}, {"ClassName": "bomberjacket_olive", "MaxPriceThreshold": 11745, "MinPriceThreshold": 7045, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_blue", "MaxPriceThreshold": 1875, "MinPriceThreshold": 1125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["quiltedjacket_red", "quiltedjacket_grey", "quiltedjacket_orange", "quiltedjacket_yellow", "quiltedjacket_violet", "quiltedjacket_black", "quiltedjacket_green"]}, {"ClassName": "quiltedjacket_red", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_orange", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_yellow", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_violet", "MaxPriceThreshold": 1645, "MinPriceThreshold": 990, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_black", "MaxPriceThreshold": 2370, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "quiltedjacket_green", "MaxPriceThreshold": 2370, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "prisonuniformjacket", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "policejacketorel", "MaxPriceThreshold": 1100, "MinPriceThreshold": 660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "policejacket", "MaxPriceThreshold": 1100, "MinPriceThreshold": 660, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bushlatpolicejacket_blue", "MaxPriceThreshold": 7895, "MinPriceThreshold": 4740, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "paramedicjacket_blue", "MaxPriceThreshold": 1075, "MinPriceThreshold": 645, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["paramedicjacket_crimson", "paramedicjacket_green"]}, {"ClassName": "hikingjacket_black", "MaxPriceThreshold": 1875, "MinPriceThreshold": 1125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["hikingjacket_red", "hikingjacket_blue", "hikingjacket_green"]}, {"ClassName": "hikingjacket_green", "MaxPriceThreshold": 2370, "MinPriceThreshold": 1420, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "raincoat_pink", "MaxPriceThreshold": 405, "MinPriceThreshold": 245, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["raincoat_orange", "raincoat_yellow", "raincoat_red", "raincoat_blue", "raincoat_black", "raincoat_green"]}, {"ClassName": "ttskojacket_camo", "MaxPriceThreshold": 1725, "MinPriceThreshold": 1035, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bdujacket", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingjacket_autumn", "MaxPriceThreshold": 7180, "MinPriceThreshold": 4310, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["huntingjacket_brown", "huntingjacket_spring", "huntingjacket_summer", "huntingjacket_winter"]}, {"ClassName": "huntingjacket_brown", "MaxPriceThreshold": 7055, "MinPriceThreshold": 4235, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingjacket_spring", "MaxPriceThreshold": 7430, "MinPriceThreshold": 4455, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingjacket_summer", "MaxPriceThreshold": 5595, "MinPriceThreshold": 3355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "huntingjacket_winter", "MaxPriceThreshold": 7305, "MinPriceThreshold": 4385, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m65jacket_black", "MaxPriceThreshold": 11845, "MinPriceThreshold": 7105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["m65jacket_khaki", "m65jacket_tan", "m65jacket_olive"]}, {"ClassName": "m65jacket_olive", "MaxPriceThreshold": 13055, "MinPriceThreshold": 7830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "gorkaejacket_summer", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["gorkaejacket_flat", "gorkaejacket_autumn", "gorkaejacket_pautrev", "gorkaejacket_winter"]}, {"ClassName": "usmcjacket_desert", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["usmcjacket_woodland"]}, {"ClassName": "usmcjacket_woodland", "MaxPriceThreshold": 1755, "MinPriceThreshold": 1055, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "nbcjacketgray", "MaxPriceThreshold": 13165, "MinPriceThreshold": 7900, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["nbc<PERSON><PERSON><PERSON><PERSON>", "nbcjacketwhite"]}, {"ClassName": "chainmail", "MaxPriceThreshold": 595, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "omkjacket_navy", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "navyuniformjacket", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}