=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-15_20-2-46
====================================================================================
20:2:46 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
20:2:46 | [PermissionManager] Perm (FreeCamera) has been registered!
20:2:46 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
20:2:46 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
20:2:46 | [PermissionManager] Perm (MenuServerManager) has been registered!
20:2:46 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
20:2:46 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
20:2:46 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
20:2:46 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
20:2:46 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
20:2:46 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
20:2:46 | [PermissionManager] Perm (MenuObjectManager) has been registered!
20:2:46 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
20:2:46 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
20:2:46 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
20:2:46 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
20:2:46 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
20:2:46 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
20:2:46 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
20:2:46 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
20:2:46 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
20:2:46 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
20:2:46 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
20:2:46 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
20:2:46 | [PermissionManager] Perm (MenuBansManager) has been registered!
20:2:46 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
20:2:46 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
20:2:46 | [PermissionManager] Perm (MenuWebHooks) has been registered!
20:2:46 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
20:2:46 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
20:2:46 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
20:2:46 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
20:2:46 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
20:2:46 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
20:2:46 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
20:2:46 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
20:2:46 | [PermissionManager] Adding Super Admin (76561199239979485)
20:2:46 | [PermissionManager] Adding Super Admin (76561197999250250)
20:2:46 | [PermissionManager] Adding Super Admin (76561198153347717)
20:2:46 | [PermissionManager] Loaded UserGroups.json
20:2:46 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
20:2:46 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
20:2:46 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
20:2:46 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
20:2:46 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
20:2:46 | Building Sets Loaded: 0
20:2:46 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
21:12:48 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
21:15:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6579.030273, 234.701141, 6440.512695>)
21:15:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6557.113281, 233.824356, 6458.351074>)
21:15:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6537.655762, 234.859344, 6476.928223>)
21:15:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6522.556641, 235.986511, 6483.083008>)
21:15:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6508.407715, 236.080536, 6482.570801>)
21:15:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6492.382813, 235.821716, 6480.143066>)
21:15:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6438.947266, 233.235870, 6523.643555>)
21:15:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6420.079590, 233.165649, 6555.276367>)
21:15:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6409.137207, 233.861847, 6572.732910>)
21:15:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6391.100586, 254.845047, 6766.650391>)
21:15:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6336.407227, 253.710297, 6694.679199>)
21:15:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6338.089844, 253.655914, 6635.824707>)
21:20:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6455.917969, 233.167816, 6503.226074>)
21:20:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6445.252930, 232.994141, 6511.443848>)
21:20:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6440.911621, 233.269897, 6521.512207>)
21:20:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6436.507813, 233.500229, 6532.878418>)
21:20:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6422.514648, 232.987778, 6547.966309>)
21:20:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6400.025391, 232.993835, 6559.151855>)
21:20:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6223.458496, 239.285751, 6527.332031>)
21:21:11 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6208.772461, 232.488754, 6515.068848>)
21:21:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6214.161133, 231.268021, 6491.743164>)
21:21:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6220.681152, 231.697372, 6483.507324>)
21:21:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6227.498047, 232.157349, 6477.352539>)
21:21:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6235.567383, 232.710373, 6471.395020>)
21:21:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6239.710449, 233.267563, 6463.003418>)
21:21:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6243.197754, 233.757462, 6454.105957>)
21:21:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6246.801758, 234.105743, 6444.125488>)
21:21:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6255.422363, 233.997314, 6435.049316>)
21:22:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6387.035645, 231.486465, 6352.949707>)
21:22:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6396.708984, 231.778137, 6337.269043>)
21:22:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6410.545410, 230.825256, 6313.017578>)
21:22:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6424.750488, 230.496964, 6290.250977>)
21:22:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6430.412598, 230.495575, 6271.895020>)
21:23:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6438.708496, 230.500000, 6272.232422>)
21:23:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6429.640137, 230.496994, 6260.615723>)
21:24:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6419.882324, 230.496979, 6249.338867>)
21:24:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6397.459961, 230.232208, 6226.246582>)
21:24:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6363.267090, 229.226700, 6191.089844>)
21:24:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6349.002441, 229.859955, 6176.432129>)
21:24:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6348.659668, 229.912735, 6176.072266>)
21:24:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6303.562500, 227.996872, 6128.949707>)
21:25:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
21:25:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) healed player (steamid=76561197999250250)
21:25:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6323.303223, 228.164932, 6138.526367>)
21:25:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6330.808594, 229.157791, 6148.041992>)
21:25:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6337.767090, 229.986588, 6155.414551>)
21:25:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6355.506348, 228.998077, 6174.520020>)
21:25:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6383.121582, 228.301193, 6204.257324>)
21:25:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6396.095703, 228.977112, 6217.960938>)
21:25:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6406.460938, 230.618576, 6227.855957>)
21:25:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6494.794922, 235.209778, 6197.276367>)
21:26:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6453.239746, 229.450531, 6179.680664>)
21:29:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6421.043945, 229.306488, 6214.072754>)
21:31:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6423.392578, 230.631485, 6310.894531>)
21:31:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6417.322266, 244.189133, 6393.973145>)
21:31:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6335.027344, 243.477234, 6325.158203>)
21:33:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6458.074219, 242.500122, 6367.640625>)
21:35:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6437.175781, 234.623550, 6412.000977>)
21:35:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6383.506836, 233.314621, 6489.107422>)
21:35:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6372.612305, 233.847153, 6505.256836>)
21:35:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6363.360352, 234.518280, 6521.609863>)
21:35:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6350.399414, 233.911072, 6538.026855>)
21:35:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
21:37:6 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1188.979980, 443.140045, 11855.400391>)
21:37:6 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1188.98,0,11855.4)
21:37:46 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<1187.050049, 490.390045, 11808.500000>)
21:37:46 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=1187.05,0,11808.5)
21:41:45 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<2205.340088, 212.969986, 7934.419922>)
21:41:45 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=2205.34,0,7934.42)
21:44:26 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<8448.469727, 172.543701, 3120.030029>)
21:44:26 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=8448.47,0,3120.03)
21:44:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8369.242188, 183.807831, 3199.784668>)
21:45:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8328.807617, 184.615845, 3238.004883>)
21:45:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8299.896484, 177.534210, 3276.725586>)
21:45:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8281.799805, 177.774673, 3299.618652>)
21:45:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8222.244141, 179.578598, 3418.061035>)
21:46:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8140.201660, 191.782410, 3477.787598>)
21:47:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:47:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:47:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:47:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:47:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:47:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:48:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:48:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
21:49:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8162.200684, 180.232483, 3409.828125>)
21:50:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8166.311523, 179.020386, 3371.925049>)
21:50:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8101.488770, 183.131317, 3502.738037>)
21:50:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8134.207031, 191.412552, 3479.076904>)
21:52:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8155.855469, 181.573303, 3436.697021>)
21:54:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8157.841309, 181.599442, 3425.129395>)
21:54:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8154.619629, 181.631363, 3439.360107>)
21:54:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8152.455078, 182.008133, 3447.990967>)
21:54:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<8150.675781, 182.671036, 3455.154785>)
21:54:40 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
21:54:47 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected early from server (EXIT NOW).
