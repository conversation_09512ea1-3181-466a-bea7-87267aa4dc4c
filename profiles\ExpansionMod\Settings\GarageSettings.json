{"m_Version": 6, "Enabled": 1, "AllowStoringDEVehicles": 0, "GarageMode": 0, "GarageStoreMode": 0, "GarageRetrieveMode": 0, "MaxStorableVehicles": 2, "VehicleSearchRadius": 20.0, "MaxDistanceFromStoredPosition": 150.0, "CanStoreWithCargo": 1, "UseVirtualStorageForCargo": 0, "NeedKeyToStore": 1, "EntityWhitelist": ["ExpansionParkingMeter"], "EnableGroupFeatures": 1, "GroupStoreMode": 2, "EnableMarketFeatures": 1, "StorePricePercent": 5.0, "StaticStorePrice": 0, "MaxStorableTier1": 2, "MaxStorableTier2": 4, "MaxStorableTier3": 6, "MaxRangeTier1": 20.0, "MaxRangeTier2": 30.0, "MaxRangeTier3": 40.0, "ParkingMeterEnableFlavor": 1}