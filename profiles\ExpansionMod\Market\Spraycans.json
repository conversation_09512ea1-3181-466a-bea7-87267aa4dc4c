{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_SPRAYCANS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "expansionspraycanblue", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionspraycanred", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanredrust"]}, {"ClassName": "expansionspraycangreen", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycangreenrust", "expansionspraycanlightgreen"]}, {"ClassName": "expansionspraycanblack", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanblackrust"]}, {"ClassName": "expansionspraycanorange", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionspraycanwhite", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanwhiterust"]}, {"ClassName": "expansionspraycangrey", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycangreyrust"]}, {"ClassName": "expansionspraycanyellow", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanyellowrust"]}, {"ClassName": "expansionspraycanwine", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanwinerust"]}, {"ClassName": "expansionspraycanbanditkitty", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionspraycancamo", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionspraycancamomedical", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionspraycanmh6blueline", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanmh6digital", "expansionspraycanmh6elliptical", "expansionspraycanmh6furious", "expansionspraycanmh6graywatcher", "expansionspraycanmh6jeans", "expansionspraycanmh6resistance", "expansionspraycanmh6shadow", "expansionspraycanmh6sheriff", "expansionspraycanmh6speedy", "expansionspraycanmh6sunset", "expansionspraycanmh6vrana", "expansionspraycanmh6wasp", "expansionspraycanmh6wave"]}, {"ClassName": "expansionspraycanuh1hmedical", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanuh1huber", "expansionspraycanuh1hresistance", "expansionspraycanuh1hcivilian"]}, {"ClassName": "expansionspraycanmerlindahoman", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["expansionspraycanmerlinion", "expansionspraycanmerlinpresident", "expansionspraycanmerlinvrana", "expansionspraycanmerlinwave"]}, {"ClassName": "expansionspraycanbusweeb", "MaxPriceThreshold": 200, "MinPriceThreshold": 100, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}