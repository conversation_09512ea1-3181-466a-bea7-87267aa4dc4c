=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-20_23-2-48
====================================================================================
23:2:48 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:2:48 | [PermissionManager] Perm (FreeCamera) has been registered!
23:2:48 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:2:48 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:2:48 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:2:48 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:2:48 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:2:48 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:2:48 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:2:48 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:2:48 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:2:48 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:2:48 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:2:48 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:2:48 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:2:48 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:2:48 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:2:48 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:2:48 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:2:48 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:2:48 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:2:48 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:2:48 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:2:48 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:2:48 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:2:48 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:2:48 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:2:48 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:2:48 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:2:48 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:2:48 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:2:48 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:2:48 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:2:48 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:2:48 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:2:48 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:2:48 | [PermissionManager] Adding Super Admin (76561199239979485)
23:2:48 | [PermissionManager] Adding Super Admin (76561197999250250)
23:2:48 | [PermissionManager] Adding Super Admin (76561198153347717)
23:2:48 | [PermissionManager] Loaded UserGroups.json
23:2:48 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:2:48 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:2:48 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:2:48 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:2:48 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:2:48 | Building Sets Loaded: 0
23:2:48 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
1:20:22 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
1:26:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
1:27:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamo_2GorkaJacket)
1:28:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamo_2GorkaPants)
1:28:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_ForestGreenGhillieArmband)
1:29:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_GreenCamo_2GhillieArmband)
1:29:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Renegade_ForestGreenGhillieArmband" (pos=<9541.952148, 138.507858, 1145.620728>)
1:30:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminProtectorCase)
1:30:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminProtectorCase)
1:30:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminProtectorCase)
1:30:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminJungleBoots)
1:30:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_BlackAdminGloves)
1:30:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (RenegadeAdmin_BlackTortillaBag)
1:32:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (GhillieHood_Mossy)
1:33:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (WitchHood_Black)
1:33:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_NVG_Blue_P)
1:33:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (NVGHeadstrap)
1:33:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:33:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:33:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Battery9V)
1:34:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9515.147461, 142.729324, 1160.500244>)
1:34:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9572.143555, 143.961685, 1150.459229>)
1:35:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9531.885742, 147.130295, 1134.940674>)
1:35:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9526.056641, 138.631241, 1126.247437>)
1:36:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Canteen)
1:36:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:36:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:36:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:36:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:36:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Cola)
1:36:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (BoxCerealCrunchin)
1:36:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SodaCan_Kvass)
1:36:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Honey)
1:36:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (BakedBeansCan_Opened)
1:36:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (BakedBeansCan)
1:36:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (BakedBeansCan)
1:36:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:36:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:36:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:36:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:36:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TacticalBaconCan)
1:36:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (SpaghettiCan)
1:37:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_Camo)
1:37:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_Camo)
1:37:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_Camo_Buttstock)
1:38:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (HuntingOptic)
1:39:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:39:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_FAL_300Rnd_Mag)
1:40:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_Camo_HuntingOptic)
1:41:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Renegade_MilitaryBelt)
1:41:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (NylonKnifeSheath)
1:41:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CombatKnife)
1:43:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Renegade_FAL_Camo" (pos=<9513.187500, 139.415741, 1132.584351>)
1:45:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (OrienteeringCompass)
1:46:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AlarmClock_Blue)
1:50:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9511.775391, 143.358734, 1158.772217>)
1:50:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9586.471680, 132.829086, 1239.309570>)
1:50:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9523.830078, 132.839996, 1297.547485>)
1:50:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9513.211914, 132.912155, 1310.875854>)
1:50:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9509.899414, 132.975067, 1314.097900>)
1:50:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9499.239258, 133.605118, 1326.399658>)
1:50:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9489.321289, 134.081543, 1337.812134>)
1:50:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9480.755859, 134.720917, 1347.465576>)
1:50:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9470.856445, 136.002960, 1365.585083>)
1:50:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9467.974609, 136.720627, 1375.371826>)
1:50:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9459.949219, 138.016663, 1394.947388>)
1:55:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
1:55:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
1:55:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (TetracyclineAntibiotics)
2:0:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (VitaminBottle)
2:0:32 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
