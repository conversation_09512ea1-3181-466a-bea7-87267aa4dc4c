{"ConfigVersion": 22, "ID": 3, "Type": 1, "Title": "<PERSON>'s package", "Descriptions": ["Oh right, here's what <PERSON> wants. Bring it to him and he will be satisfied. Don't even think about opening the package! Just go and bring it to him.", "What are you still doing here? Go to Peter!", "Why did this take so long?... You got what I wanted? ** (He looks at the package with wide eyes and rips it out of your hands) ** Good!... What? Your way out? Well what should I say, but I lied to you. There is no fucking way out of this hell, and you will die here like all of us. You can have this backpack and try your luck out there. I am sorry I lied to you, but we all have to survive here somehow. I wish you good luck out there... You will need it."], "ObjectiveText": "Go back to <PERSON> and give him the package from <PERSON>.", "FollowUpQuest": -1, "Repeatable": 0, "IsDailyQuest": 0, "IsWeeklyQuest": 0, "CancelQuestOnPlayerDeath": 0, "Autocomplete": 0, "IsGroupQuest": 0, "ObjectSetFileName": "", "QuestItems": [], "Rewards": [{"ClassName": "TaloonBag_Blue", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}, {"ClassName": "TaloonBag_Green", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}, {"ClassName": "TaloonBag_Orange", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}, {"ClassName": "TaloonBag_Violet", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}], "NeedToSelectReward": 1, "RandomReward": 0, "RandomRewardAmount": -1, "RewardsForGroupOwnerOnly": 1, "RewardBehavior": 0, "QuestGiverIDs": [2], "QuestTurnInIDs": [1], "IsAchievement": 0, "Objectives": [{"ConfigVersion": 28, "ID": 2, "ObjectiveType": 5}], "QuestColor": 0, "ReputationReward": 0, "ReputationRequirement": -1, "PreQuestIDs": [2], "RequiredFaction": "", "FactionReward": "", "PlayerNeedQuestItems": 1, "DeleteQuestItems": 1, "SequentialObjectives": 1, "FactionReputationRequirements": {}, "FactionReputationRewards": {}, "SuppressQuestLogOnCompetion": 0, "Active": 1}