=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-15_17-2-51
====================================================================================
17:2:51 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
17:2:51 | [PermissionManager] Perm (FreeCamera) has been registered!
17:2:51 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
17:2:51 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
17:2:51 | [PermissionManager] Perm (MenuServerManager) has been registered!
17:2:51 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
17:2:51 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
17:2:51 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
17:2:51 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
17:2:51 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
17:2:51 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
17:2:51 | [PermissionManager] Perm (MenuObjectManager) has been registered!
17:2:51 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
17:2:51 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
17:2:51 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
17:2:51 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
17:2:51 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
17:2:51 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
17:2:51 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
17:2:51 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
17:2:51 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
17:2:51 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
17:2:51 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
17:2:51 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
17:2:51 | [PermissionManager] Perm (MenuBansManager) has been registered!
17:2:51 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
17:2:51 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
17:2:51 | [PermissionManager] Perm (MenuWebHooks) has been registered!
17:2:51 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
17:2:51 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
17:2:51 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
17:2:51 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
17:2:51 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
17:2:51 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
17:2:51 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
17:2:51 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
17:2:51 | [PermissionManager] Adding Super Admin (76561199239979485)
17:2:51 | [PermissionManager] Adding Super Admin (76561197999250250)
17:2:51 | [PermissionManager] Adding Super Admin (76561198153347717)
17:2:51 | [PermissionManager] Loaded UserGroups.json
17:2:51 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
17:2:51 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
17:2:51 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
17:2:51 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
17:2:51 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
17:2:51 | Building Sets Loaded: 0
17:2:51 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
17:33:54 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
17:35:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2903.698242, 322.956635, 11275.205078>)
17:35:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2909.248047, 323.000397, 11261.247070>)
17:35:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2915.281738, 322.978790, 11246.969727>)
17:35:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2923.658203, 322.889343, 11232.059570>)
17:35:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2931.954834, 322.801270, 11217.541016>)
17:35:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2942.986572, 322.731293, 11204.570313>)
17:39:10 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<9934.940430, 225.350784, 7146.040039>)
17:39:10 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=9934.94,0,7146.04)
17:39:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9889.948242, 222.223633, 7142.834473>)
17:39:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9877.588867, 221.623734, 7138.138184>)
17:39:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9861.263672, 220.841125, 7133.872559>)
17:39:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9845.146484, 220.134949, 7130.157227>)
17:39:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9832.938477, 220.134903, 7127.499023>)
17:39:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9820.365234, 220.134918, 7125.865723>)
17:39:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9808.371094, 220.134918, 7127.029297>)
17:39:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9803.754883, 220.134903, 7139.984375>)
17:39:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9803.623047, 220.134903, 7149.247070>)
17:39:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9802.750000, 220.134918, 7159.451172>)
17:39:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9801.525391, 220.203613, 7169.668945>)
17:39:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9802.754883, 220.134918, 7157.330566>)
17:39:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9803.452148, 220.297058, 7145.394531>)
17:39:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9804.347656, 220.134903, 7129.111816>)
17:40:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9730.537109, 220.134933, 7124.834961>)
17:40:21 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9738.688477, 220.134918, 7131.115723>)
17:40:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9749.888672, 220.134918, 7138.091309>)
17:40:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9757.746094, 220.134918, 7148.727539>)
17:40:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9760.942383, 221.462616, 7151.293945>)
17:40:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9768.961914, 220.134903, 7155.422363>)
17:41:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9806.502930, 220.134918, 7219.528809>)
17:41:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9823.712891, 223.748779, 7196.154785>)
17:41:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9759.610352, 220.542404, 7240.897949>)
17:42:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9816.378906, 224.986496, 7186.314453>)
17:46:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionTractor_Red)
17:46:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionTractor_Red)
17:46:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
17:48:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionTractor_Red" (pos=<9891.376953, 220.758621, 7170.392578>)
17:54:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_schoolbus)
18:0:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (PAZ_672)
18:0:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (LIAZ_677RF)
18:0:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (KAVZ685_RF_beige)
18:3:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9604.110352, 222.265472, 7123.951172>)
18:15:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9849.811523, 223.500687, 7168.447754>)
18:15:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9850.812500, 223.437363, 7169.701172>)
18:15:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9853.310547, 223.508575, 7171.446777>)
18:16:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "PAZ_672" (pos=<9790.504883, 220.170105, 7113.406738>)
18:17:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "KAVZ685_RF_beige" (pos=<9816.912109, 220.170761, 7121.128418>)
18:17:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "LIAZ_677RF" (pos=<9826.915039, 220.242477, 7120.765137>)
18:17:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "rag_schoolbus" (pos=<9821.651367, 220.502426, 7110.673828>)
18:20:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (F1_Pickup_green)
18:22:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (KAVZ685_RF_red)
18:22:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (LIAZ_677RF_Auto)
18:22:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionBus_Weeb)
18:23:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ZAZ_968M)
18:28:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Volkswagen_Golf_RUS_rally)
18:29:12 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_3962)
18:30:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9961.260742, 220.799042, 7307.329590>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.744141, 220.384079, 7344.806152>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.785156, 220.386505, 7344.672852>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.821289, 220.389008, 7344.560547>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.871094, 220.392593, 7344.426270>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.917969, 220.395386, 7344.299805>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9963.967773, 220.398926, 7344.171875>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.017578, 220.402313, 7344.051758>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.076172, 220.405228, 7343.958008>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.126953, 220.406860, 7343.891602>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.186523, 220.408966, 7343.814941>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.246094, 220.411179, 7343.746094>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.300781, 220.413635, 7343.694824>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.353516, 220.415985, 7343.649414>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.416016, 220.419006, 7343.600098>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.481445, 220.422470, 7343.558594>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.544922, 220.425659, 7343.528320>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.608398, 220.429428, 7343.501953>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.661133, 220.432465, 7343.487793>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.722656, 220.436295, 7343.476074>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.785156, 220.440247, 7343.473145>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.861328, 221.456818, 7365.074707>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.926758, 221.469742, 7365.191406>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9964.977539, 221.481140, 7365.304199>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.038086, 221.495880, 7365.452637>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.086914, 221.509964, 7365.599121>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.192383, 221.543869, 7365.969238>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.241211, 221.561264, 7366.160645>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.273438, 221.574036, 7366.308105>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.316406, 221.590332, 7366.494141>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.357422, 221.607452, 7366.689453>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.397461, 221.624741, 7366.888672>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.440430, 221.642715, 7367.098633>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.473633, 221.658417, 7367.279297>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.514648, 221.677734, 7367.508301>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.555664, 221.697708, 7367.744141>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.594727, 221.718246, 7367.989746>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.636719, 221.740097, 7368.240234>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.669922, 221.757660, 7368.468262>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.728516, 221.788574, 7368.918457>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.769531, 221.810165, 7369.229492>)
18:30:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<9965.808594, 221.832443, 7369.553223>)
18:45:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) /refuel used on self.
19:8:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11065.014648, 487.501160, 4961.575684>)
19:8:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11070.609375, 487.096832, 4971.648926>)
19:8:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11076.222656, 487.078674, 4981.085938>)
19:8:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11079.822266, 486.994354, 4988.141113>)
19:8:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11083.424805, 486.930298, 4995.175781>)
19:8:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11088.059570, 486.926941, 5001.890137>)
19:8:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11093.661133, 486.577515, 5009.637207>)
19:9:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11092.456055, 485.173523, 5019.496094>)
19:9:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11085.024414, 483.608856, 5027.295898>)
19:9:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11077.472656, 481.997345, 5034.695313>)
19:9:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11074.060547, 482.252655, 5038.251465>)
19:9:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11072.937500, 483.505859, 5039.697266>)
19:9:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11060.417969, 478.945068, 5051.642090>)
19:9:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11057.006836, 478.823181, 5045.738281>)
19:9:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11058.382813, 479.479187, 5036.102539>)
19:9:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11060.125000, 480.148438, 5027.060059>)
19:9:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11061.688477, 480.931763, 5018.434570>)
19:9:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11063.003906, 481.822876, 5010.573242>)
19:9:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11064.221680, 482.827240, 5003.102051>)
19:9:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11065.372070, 483.796936, 4995.990723>)
19:9:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11066.574219, 484.774200, 4988.590332>)
19:9:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11067.831055, 485.757935, 4980.932617>)
19:9:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11069.134766, 486.746002, 4973.048828>)
19:9:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11070.452148, 487.675262, 4965.096680>)
19:18:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11043.521484, 484.053894, 4953.316895>)
19:18:0 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11033.541016, 481.964294, 4947.973145>)
19:18:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<11014.472656, 477.031799, 4938.349121>)
19:18:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<10994.125977, 471.712860, 4928.086426>)
19:18:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_3962_green)
19:31:1 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7826.796387, 269.041138, 11939.632813>)
19:31:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7837.449219, 269.041138, 11936.830078>)
19:31:2 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7848.466309, 269.041138, 11933.500977>)
19:31:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7860.265137, 268.880554, 11929.901367>)
19:31:3 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7873.277832, 268.914795, 11925.205078>)
19:31:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7886.308105, 269.041168, 11919.994141>)
19:31:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7894.398926, 269.041138, 11909.391602>)
19:31:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7901.278809, 269.041199, 11900.030273>)
19:31:7 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7907.715820, 268.909973, 11887.648438>)
19:44:4 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7907.966309, 268.810547, 11877.543945>)
19:45:47 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<9958.259766, 229.937668, 7121.979980>)
19:45:47 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=9958.26,0,7121.98)
19:53:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
19:57:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<6702.700195, 263.472565, 6380.329102>)
19:58:32 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
19:58:57 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
