{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_EXCHANGE", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 1, "InitStockPercent": 75, "Items": [{"ClassName": "expansionbanknotehryvnia", "MaxPriceThreshold": 1, "MinPriceThreshold": 1, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionBanknoteUSD", "MaxPriceThreshold": 1, "MinPriceThreshold": 1, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionBanknoteEuro", "MaxPriceThreshold": 1, "MinPriceThreshold": 1, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionGoldBar", "MaxPriceThreshold": 100000, "MinPriceThreshold": 100000, "SellPricePercent": 100, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionSilverBar", "MaxPriceThreshold": 90000, "MinPriceThreshold": 90000, "SellPricePercent": 100, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionGoldNugget", "MaxPriceThreshold": 50000, "MinPriceThreshold": 50000, "SellPricePercent": 100, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ExpansionSilverNugget", "MaxPriceThreshold": 30000, "MinPriceThreshold": 30000, "SellPricePercent": 100, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}