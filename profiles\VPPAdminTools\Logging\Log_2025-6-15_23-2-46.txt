=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-15_23-2-46
====================================================================================
23:2:46 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:2:46 | [PermissionManager] Perm (FreeCamera) has been registered!
23:2:46 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:2:46 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:2:46 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:2:46 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:2:46 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:2:46 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:2:46 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:2:46 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:2:46 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:2:46 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:2:46 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:2:46 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:2:46 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:2:46 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:2:46 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:2:46 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:2:46 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:2:46 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:2:46 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:2:46 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:2:46 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:2:46 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:2:46 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:2:46 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:2:46 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:2:46 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:2:46 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:2:46 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:2:46 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:2:46 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:2:46 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:2:46 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:2:46 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:2:46 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:2:46 | [PermissionManager] Adding Super Admin (76561199239979485)
23:2:46 | [PermissionManager] Adding Super Admin (76561197999250250)
23:2:46 | [PermissionManager] Adding Super Admin (76561198153347717)
23:2:46 | [PermissionManager] Loaded UserGroups.json
23:2:46 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:2:46 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:2:46 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:2:46 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:2:46 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:2:46 | Building Sets Loaded: 0
23:2:46 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
0:53:45 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
0:54:44 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<8001.060059, 197.008499, 4047.860107>)
0:54:44 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=8001.06,0,4047.86)
0:57:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:57:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AmmoBox_12gaSlug_10Rnd)
0:59:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<7778.230469, 206.648438, 4118.455566>)
1:7:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_gaz69)
1:7:53 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_gaz69)
1:13:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "rag_gaz69" (pos=<6986.909180, 189.407379, 3867.711914>)
1:14:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_jeep_willys)
1:22:11 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "rag_jeep_willys" (pos=<5741.025879, 214.260422, 4228.806641>)
1:22:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_27722_ambulancia)
1:29:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "UAZ_27722_ambulancia" (pos=<3711.429688, 328.145233, 5919.359863>)
1:29:34 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3675.255371, 360.433167, 5870.772461>)
1:29:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3666.559570, 368.226074, 5866.925781>)
1:29:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3659.003418, 375.292419, 5864.635742>)
1:29:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3650.583252, 382.949524, 5863.049805>)
1:29:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3640.885986, 391.278015, 5861.353516>)
1:29:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3627.490967, 401.924133, 5858.747559>)
1:29:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3591.642822, 432.560059, 5839.866699>)
1:29:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3583.089355, 441.638855, 5835.371094>)
1:29:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3575.079590, 450.155426, 5831.931641>)
1:29:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3566.661377, 458.870544, 5828.447754>)
1:29:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3557.173828, 467.962067, 5824.540039>)
1:29:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3548.163574, 475.083862, 5821.977539>)
1:29:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3534.574463, 483.934357, 5820.883301>)
1:29:41 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3489.015869, 514.254089, 5818.610840>)
1:29:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3479.606934, 522.434570, 5818.381348>)
1:29:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3469.825439, 530.977173, 5817.709961>)
1:29:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3461.638916, 538.707458, 5815.973633>)
1:29:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3452.812500, 546.476318, 5813.500977>)
1:29:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3432.264404, 558.949158, 5806.834961>)
1:29:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3420.977783, 564.771423, 5801.629883>)
1:29:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3414.139648, 569.262878, 5797.958008>)
1:29:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3397.971191, 576.941101, 5791.469727>)
1:29:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3378.978516, 588.043091, 5785.626465>)
1:29:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3363.399658, 598.225525, 5778.046387>)
1:31:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3288.216797, 615.996094, 5770.676758>)
1:31:8 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3262.329590, 613.508911, 5790.147949>)
1:31:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3248.281494, 612.818359, 5802.535156>)
1:31:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3243.772217, 612.261292, 5814.277832>)
1:31:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3260.156982, 605.574707, 5852.717773>)
1:31:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3271.939941, 602.157227, 5867.767090>)
1:31:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3268.269043, 605.661804, 5841.436035>)
1:31:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3271.444092, 602.157227, 5870.053711>)
1:32:57 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3274.164063, 649.869385, 5877.493164>)
1:34:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3252.425537, 613.614807, 5742.062988>)
1:34:14 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3236.191650, 612.193604, 5737.616211>)
1:34:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3221.409912, 611.060852, 5733.617676>)
1:34:15 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3205.035645, 609.697876, 5729.156250>)
1:34:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3185.363525, 607.456421, 5722.422363>)
1:34:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3183.570557, 607.266724, 5721.807129>)
1:34:17 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3167.070068, 606.008484, 5715.745117>)
1:34:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3152.343750, 605.364990, 5710.351074>)
1:34:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3130.837891, 603.480530, 5702.693359>)
1:34:20 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3094.100830, 599.912415, 5704.051758>)
1:36:58 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3027.042969, 598.452881, 5697.019531>)
1:48:42 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2857.625000, 599.911621, 5657.573730>)
1:49:42 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<2261.739990, 703.021484, 5654.759766>)
1:49:42 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=2261.74,0,5654.76)
1:50:6 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<2260.822266, 720.363647, 5668.667480>)
1:50:16 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<1989.709717, 643.175415, 5766.622070>)
1:51:21 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<4319.740234, 550.439819, 3613.770020>)
1:51:21 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=4319.74,0,3613.77)
1:51:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4321.901855, 551.917908, 3614.704102>)
1:51:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4323.119141, 554.893799, 3622.947021>)
1:52:57 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<4380.310059, 528.187744, 3884.100098>)
1:52:57 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=4380.31,0,3884.1)
1:53:27 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4335.259277, 539.785583, 3920.547852>)
1:53:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4319.539063, 542.620850, 3940.317383>)
1:53:28 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4300.843750, 545.969238, 3965.716797>)
1:53:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4283.611328, 549.167725, 3989.227295>)
1:53:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4271.843262, 551.984314, 4005.447510>)
1:53:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4248.952148, 556.923340, 4034.659912>)
1:53:31 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4236.810547, 560.133118, 4050.307129>)
1:53:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4227.677734, 562.069824, 4062.132813>)
1:53:32 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4216.948730, 563.573608, 4075.924316>)
1:53:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4202.630859, 564.548645, 4095.864502>)
1:53:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4189.801270, 564.754028, 4111.719238>)
1:53:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4179.845215, 564.869629, 4122.978516>)
1:53:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4169.001465, 564.972961, 4131.731445>)
1:53:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4159.295898, 565.007690, 4139.019043>)
1:53:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4146.817871, 565.001404, 4146.973145>)
1:53:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4101.095215, 564.255554, 4160.285156>)
1:53:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4069.949219, 564.410095, 4166.032715>)
1:53:48 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4049.065674, 565.423096, 4169.493164>)
1:53:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4029.142090, 566.200317, 4171.510742>)
1:55:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3961.060059, 566.341187, 4170.532715>)
1:55:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3951.876709, 566.451050, 4174.491211>)
1:55:45 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3943.495850, 566.544922, 4177.834473>)
1:55:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3933.850586, 566.617554, 4179.851074>)
1:55:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3924.092041, 566.647156, 4181.541016>)
1:58:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (UAZ_33094_green)
1:59:39 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
2:0:4 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
