{"m_Version": 12, "DisplayName": "Base Building Plus", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "BBP_Concrete_<PERSON>_Pile", "MaxPriceThreshold": 30000, "MinPriceThreshold": 29000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Cement", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Mortar_Mix", "MaxPriceThreshold": 15000, "MinPriceThreshold": 12500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Blueprint", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_TapeMeasure", "MaxPriceThreshold": 4000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Cement_Mixer_Kit", "MaxPriceThreshold": 50000, "MinPriceThreshold": 30000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Wall_Kit", "MaxPriceThreshold": 25000, "MinPriceThreshold": 19000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Workbench_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 3000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Barbwire_Fence_Kit", "MaxPriceThreshold": 10000, "MinPriceThreshold": 9000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Door_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Floor_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Floor_Mesh_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Floor_Hatch_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Hatch_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Foundation_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Triangle_Foundation_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Gate_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_SGate_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Half_Wall_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BB<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Mesh_Gate_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Pillar_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Ramp_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BB<PERSON>_Roof_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BB<PERSON>_<PERSON>lope_Roof_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BB<PERSON>_<PERSON>lope_Wall_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Stair_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Metal_Stair_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BB<PERSON>_<PERSON>_Ladder_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Triangle_Floor_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Triangle_Roof_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Window_Kit", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Carpet_1", "MaxPriceThreshold": 5000, "MinPriceThreshold": 5000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Carpet_2", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Plaster_1", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_Plaster_2", "MaxPriceThreshold": 15000, "MinPriceThreshold": 14000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_WallPaper_1", "MaxPriceThreshold": 15000, "MinPriceThreshold": 14000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_WallPaper_2", "MaxPriceThreshold": 15000, "MinPriceThreshold": 14000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_WallPaper_Mural_1", "MaxPriceThreshold": 15000, "MinPriceThreshold": 14000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "BBP_WallPaper_Mural_2", "MaxPriceThreshold": 15000, "MinPriceThreshold": 14000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}