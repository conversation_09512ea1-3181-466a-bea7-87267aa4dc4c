{"m_Version": 12, "DisplayName": "RedFalcon Flight System Heliz", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "RFFSHeli_EC135", "MaxPriceThreshold": 320000, "MinPriceThreshold": 320000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7", "HeadlightH7"], "Variants": ["RFFSHeli_EC135_BlackCamo"]}, {"ClassName": "RFFSHeli_Ka26", "MaxPriceThreshold": 320000, "MinPriceThreshold": 320000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": []}, {"ClassName": "RFFSHeli_LittleBird", "MaxPriceThreshold": 180000, "MinPriceThreshold": 180000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_LittleBird_Camo", "RFFSHeli_LittleBird_Desert"]}, {"ClassName": "RFFSHeli_Mi2", "MaxPriceThreshold": 324555, "MinPriceThreshold": 324555, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_Mi2_Hornet", "RFFSHeli_Mi2_Military"]}, {"ClassName": "RFFSHeli_R22", "MaxPriceThreshold": 290000, "MinPriceThreshold": 289000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_R22_Black", "RFFSHeli_R22_Red"]}, {"ClassName": "RFFSHeli_S76", "MaxPriceThreshold": 250000, "MinPriceThreshold": 250000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_S76_WoodlandCamo"]}, {"ClassName": "RFFSHeli_UH1H", "MaxPriceThreshold": 230000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7", "HeadlightH7"], "Variants": ["RFFSHeli_UH1H_Combat"]}, {"ClassName": "RFFSHeli_Apache", "MaxPriceThreshold": 1500000, "MinPriceThreshold": 1500000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_Apache_Desert", "RFFSHeli_Apache_Winter"]}, {"ClassName": "RFFSHeli_AS350", "MaxPriceThreshold": 1000000, "MinPriceThreshold": 1000000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7", "HeadlightH7"], "Variants": []}, {"ClassName": "RFFSHeli_Bell429", "MaxPriceThreshold": 200000, "MinPriceThreshold": 200000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7"], "Variants": ["RFFSHeli_Bell429_Medic", "RFFSHeli_Bell429_Police", "RFFSHeli_Bell429_Uganda", "RFFSHeli_Bell429_ZS"]}, {"ClassName": "RFFSHeli_Blackhawk", "MaxPriceThreshold": 923000, "MinPriceThreshold": 923000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7", "HeadlightH7"], "Variants": ["RFFSHeli_Blackhawk_Cargo", "RFFSHeli_Blackhawk_blackblackhawk", "RFFSHeli_Blackhawk_Budhawk", "RFFSHeli_Blackhawk_Woodland"]}, {"ClassName": "RFFSHeli_Bo105m", "MaxPriceThreshold": 160000, "MinPriceThreshold": 160000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["RFFSHeli_aviation_battery", "RFFSHeli_igniter_plug", "RFFSHeli_hydraulic_hoses", "RFFSHeli_wiring_harness", "HeadlightH7", "HeadlightH7"], "Variants": ["RFFSHeli_Bo105m_blackcamo"]}]}