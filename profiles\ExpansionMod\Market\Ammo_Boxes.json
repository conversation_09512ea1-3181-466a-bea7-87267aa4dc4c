{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_AMMOBOXES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "ammobox_00buck_10rnd", "MaxPriceThreshold": 9965, "MinPriceThreshold": 5980, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_12gaslug_10rnd", "MaxPriceThreshold": 9965, "MinPriceThreshold": 5980, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_12garubberslug_10rnd", "MaxPriceThreshold": 1940, "MinPriceThreshold": 1165, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_22_50rnd", "MaxPriceThreshold": 550, "MinPriceThreshold": 330, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_308win_20rnd", "MaxPriceThreshold": 9595, "MinPriceThreshold": 5755, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_308wintracer_20rnd"]}, {"ClassName": "ammobox_308wintracer_20rnd", "MaxPriceThreshold": 19795, "MinPriceThreshold": 11875, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_357_20rnd", "MaxPriceThreshold": 715, "MinPriceThreshold": 430, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_380_35rnd", "MaxPriceThreshold": 1955, "MinPriceThreshold": 1175, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_45acp_25rnd", "MaxPriceThreshold": 2930, "MinPriceThreshold": 1760, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_545x39_20rnd", "MaxPriceThreshold": 19625, "MinPriceThreshold": 11775, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_545x39tracer_20rnd"]}, {"ClassName": "ammobox_545x39tracer_20rnd", "MaxPriceThreshold": 19795, "MinPriceThreshold": 11875, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_556x45_20rnd", "MaxPriceThreshold": 58875, "MinPriceThreshold": 35325, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_556x45tracer_20rnd"]}, {"ClassName": "ammobox_556x45tracer_20rnd", "MaxPriceThreshold": 59385, "MinPriceThreshold": 35630, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_762x39_20rnd", "MaxPriceThreshold": 15230, "MinPriceThreshold": 9140, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_762x39tracer_20rnd"]}, {"ClassName": "ammobox_762x39tracer_20rnd", "MaxPriceThreshold": 19795, "MinPriceThreshold": 11875, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_762x54_20rnd", "MaxPriceThreshold": 12075, "MinPriceThreshold": 7245, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_762x54tracer_20rnd"]}, {"ClassName": "ammobox_762x54tracer_20rnd", "MaxPriceThreshold": 19795, "MinPriceThreshold": 11875, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_9x19_25rnd", "MaxPriceThreshold": 2245, "MinPriceThreshold": 1345, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_9x39_20rnd", "MaxPriceThreshold": 2960, "MinPriceThreshold": 1775, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ammobox_9x39ap_20rnd"]}, {"ClassName": "ammobox_9x39ap_20rnd", "MaxPriceThreshold": 22005, "MinPriceThreshold": 13200, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_expansion_46x30_25rnd", "MaxPriceThreshold": 80, "MinPriceThreshold": 40, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox_expansion_338_15rnd", "MaxPriceThreshold": 48, "MinPriceThreshold": 24, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_ammobox_8mm_15rnd", "MaxPriceThreshold": 48, "MinPriceThreshold": 24, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}