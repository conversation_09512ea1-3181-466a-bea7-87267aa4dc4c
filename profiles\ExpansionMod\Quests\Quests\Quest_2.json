{"ConfigVersion": 22, "ID": 2, "Type": 1, "Title": "A favor for <PERSON>...", "Descriptions": ["So, <PERSON> sends you, hmm? Well I have what he wants, although he still owes me something... But I'm also not a bad guy. Let's say if you do me a favor, too, I'll give you what <PERSON> wants and even more. I gotta keep watch and make sure no shit happens around here. I want you to take this sledgehammer and clean up the village with it. There are a few Infected that have to be eliminated before they start moving into the camp.", "You are not done yet? How hard can it be to smash some heads with that hammer... Come back when the job is done!", "Oh there you are! I thought the Infected got your ass and killed you... Well, here is your reward."], "ObjectiveText": "Kill 10 civilian Infected with <PERSON>'s sledgehammer.", "FollowUpQuest": 3, "Repeatable": 0, "IsDailyQuest": 0, "IsWeeklyQuest": 0, "CancelQuestOnPlayerDeath": 0, "Autocomplete": 0, "IsGroupQuest": 0, "ObjectSetFileName": "", "QuestItems": [{"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Amount": 1}], "Rewards": [{"ClassName": "WaterBottle", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}, {"ClassName": "TunaCan", "Amount": 1, "Attachments": [], "DamagePercent": 0, "HealthPercent": 0, "QuestID": -1, "Chance": 1.0}], "NeedToSelectReward": 0, "RandomReward": 0, "RandomRewardAmount": -1, "RewardsForGroupOwnerOnly": 1, "RewardBehavior": 0, "QuestGiverIDs": [2], "QuestTurnInIDs": [2], "IsAchievement": 0, "Objectives": [{"ConfigVersion": 28, "ID": 2, "ObjectiveType": 3}, {"ConfigVersion": 28, "ID": 1, "ObjectiveType": 2}], "QuestColor": 0, "ReputationReward": 0, "ReputationRequirement": -1, "PreQuestIDs": [1], "RequiredFaction": "", "FactionReward": "", "PlayerNeedQuestItems": 1, "DeleteQuestItems": 1, "SequentialObjectives": 1, "FactionReputationRequirements": {}, "FactionReputationRewards": {}, "SuppressQuestLogOnCompetion": 0, "Active": 1}