{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_MASKS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "surgicalmask", "MaxPriceThreshold": 1045, "MinPriceThreshold": 625, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "niosh<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 935, "MinPriceThreshold": 560, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hockeymask", "MaxPriceThreshold": 340, "MinPriceThreshold": 205, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shemag_brown", "MaxPriceThreshold": 115, "MinPriceThreshold": 70, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["shemag_green", "shemag_red", "shemag_white"]}, {"ClassName": "balaclavamask_beige", "MaxPriceThreshold": 2070, "MinPriceThreshold": 1245, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["balaclavamask_blue", "balaclavamask_pink", "balaclavamask_white", "balaclavamask_blackskull", "balaclavamask_black", "balaclavamask_green", "balaclavamask_bdu", "balaclavamask_chain", "balaclavamask_pig"]}, {"ClassName": "balaclavamask_blackskull", "MaxPriceThreshold": 11845, "MinPriceThreshold": 7105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclavamask_black", "MaxPriceThreshold": 3525, "MinPriceThreshold": 2115, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclavamask_green", "MaxPriceThreshold": 2905, "MinPriceThreshold": 1745, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclavamask_bdu", "MaxPriceThreshold": 11745, "MinPriceThreshold": 7045, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclava3holes_beige", "MaxPriceThreshold": 1675, "MinPriceThreshold": 1005, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["balaclava3holes_blue", "balaclava3holes_black", "balaclava3holes_green", "balaclava3holes_white"]}, {"ClassName": "balaclava3holes_black", "MaxPriceThreshold": 3065, "MinPriceThreshold": 1840, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclava3holes_green", "MaxPriceThreshold": 2410, "MinPriceThreshold": 1445, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "balaclava3holes_white", "MaxPriceThreshold": 1090, "MinPriceThreshold": 655, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "weldingmask", "MaxPriceThreshold": 835, "MinPriceThreshold": 500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "gasmask", "MaxPriceThreshold": 5120, "MinPriceThreshold": 3070, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "gp5gasmask", "MaxPriceThreshold": 11845, "MinPriceThreshold": 7105, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["gasmask_filter"], "Variants": []}, {"ClassName": "airbornemask", "MaxPriceThreshold": 765, "MinPriceThreshold": 460, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["gasmask_filter"], "Variants": []}, {"ClassName": "mimemask_female", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["mimemask_male"]}]}