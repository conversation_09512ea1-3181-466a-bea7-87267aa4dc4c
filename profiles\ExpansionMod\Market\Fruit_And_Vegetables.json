{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_FRUIT_AND_VEGETABLES", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "apple", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "greenbellpepper", "MaxPriceThreshold": 1570, "MinPriceThreshold": 940, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 1690, "MinPriceThreshold": 1015, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pumpkin", "MaxPriceThreshold": 17, "MinPriceThreshold": 15, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "slicedpumpkin", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "potatoseed", "MaxPriceThreshold": 1585, "MinPriceThreshold": 950, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "potato", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "tomato", "MaxPriceThreshold": 1570, "MinPriceThreshold": 940, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sambucusberry", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "caninaberry", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "plum", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pear", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "agaricus<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "amanitamushroom", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "macrolepiotamushroom", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "lactari<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "psilocybemushroom", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "auriculariamushroom", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "bole<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pleuro<PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MaxPriceThreshold": 9, "MinPriceThreshold": 7, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}