{"m_Version": 12, "DisplayName": "Snafu_Optics_And_Scopes", "Icon": "Deliver", "Color": "FBFCFEFF", "InitStockPercent": 75, "Items": [{"ClassName": "SNAFU_Aimpoint_ACO", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKAimpoint_ACO", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HAimpoint_ACO", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MAimpoint_ACO", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Aimpoint_M5", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKAimpoint_M5", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HAimpoint_M5", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MAimpoint_M5", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Docter", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Elcan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKElcan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HElcan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MElcan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_EOTech_EXPS3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKEOTech_EXPS3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HEOTech_EXPS3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MEOTech_EXPS3", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_<PERSON>", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AK<PERSON>ahles", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HKahles", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MKahles", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Leupold_Mark8", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKLeupold_Mark8", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HLeupold_Mark8", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MLeupold_Mark8", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Nightforce", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKNightforce", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HNightforce", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MNightforce", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Kobra", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Tango6T_Black", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Tango6T_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Tango6T_Wood", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Tango6T_Snow", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTango6T_Black", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTango6T_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTango6T_Wood", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTango6T_Snow", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTango6T_Black", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTango6T_Tan", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTango6T_Wood", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTango6T_Snow", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Trijicon", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Trijicon_Docter", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTrijicon", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKTrijicon_Docter", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HTrijicon", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HTrijicon_Docter", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTrijicon", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MTrijicon_Docter", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAF<PERSON>_<PERSON><PERSON>", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKWalther", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HWalther", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MWalther", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M84_Optic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HuntingOptic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AKHuntingOptic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_HHuntingOptic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MHuntingOptic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_IRONMODOptic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M14Optic", "MaxPriceThreshold": 3500, "MinPriceThreshold": 3500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}