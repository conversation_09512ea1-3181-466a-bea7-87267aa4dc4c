=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-12_20-2-51
====================================================================================
20:2:51 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
20:2:51 | [PermissionManager] Perm (FreeCamera) has been registered!
20:2:51 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
20:2:51 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
20:2:51 | [PermissionManager] Perm (MenuServerManager) has been registered!
20:2:51 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
20:2:51 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
20:2:51 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
20:2:51 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
20:2:51 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
20:2:51 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
20:2:51 | [PermissionManager] Perm (MenuObjectManager) has been registered!
20:2:51 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
20:2:51 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
20:2:51 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
20:2:51 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
20:2:51 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
20:2:51 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
20:2:51 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
20:2:51 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
20:2:51 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
20:2:51 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
20:2:51 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
20:2:51 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
20:2:51 | [PermissionManager] Perm (MenuBansManager) has been registered!
20:2:51 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
20:2:51 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
20:2:51 | [PermissionManager] Perm (MenuWebHooks) has been registered!
20:2:51 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
20:2:51 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
20:2:51 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
20:2:51 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
20:2:51 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
20:2:51 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
20:2:51 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
20:2:51 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
20:2:51 | [PermissionManager] Adding Super Admin (76561199239979485)
20:2:51 | [PermissionManager] Adding Super Admin (76561197999250250)
20:2:51 | [PermissionManager] Adding Super Admin (76561198153347717)
20:2:51 | [PermissionManager] Loaded UserGroups.json
20:2:51 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
20:2:51 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
20:2:51 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
20:2:51 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
20:2:51 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
20:2:51 | Building Sets Loaded: 0
20:2:51 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
20:8:21 | Player "{TITAN TESLA}" (steamId=76561197999250250) connected to server!
20:10:3 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1457.159668, 214.230820, 10472.757813>)
20:13:34 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1089.057739, 215.513977, 10531.250977>)
20:13:35 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1075.677856, 215.519073, 10531.574219>)
20:13:36 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1061.286987, 215.519073, 10531.208984>)
20:13:37 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1045.356079, 215.513977, 10531.350586>)
20:13:37 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1029.325317, 215.513977, 10531.541016>)
20:13:38 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<1014.244141, 215.513977, 10531.758789>)
20:14:31 | "{TITAN TESLA}" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
20:16:42 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<873.549255, 215.481812, 10495.890625>)
20:16:52 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<835.716125, 215.483383, 10495.766602>)
20:16:54 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<813.108337, 215.541077, 10499.103516>)
20:17:0 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<784.353943, 214.230835, 10501.474609>)
20:17:0 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<765.791260, 214.456177, 10503.550781>)
20:17:14 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<818.804810, 215.481125, 10502.695313>)
20:17:16 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<834.296997, 215.481125, 10502.380859>)
20:17:17 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<852.705994, 215.483383, 10501.666992>)
20:17:18 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<876.623901, 215.483383, 10501.638672>)
20:17:18 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<903.576782, 214.230820, 10503.747070>)
20:17:26 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<912.845825, 214.230820, 10538.824219>)
20:17:27 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<912.755798, 214.230820, 10554.875000>)
20:17:32 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<927.409180, 214.230804, 10569.801758>)
20:18:24 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<988.302368, 214.230820, 10566.262695>)
20:18:24 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<988.730957, 214.230804, 10552.073242>)
20:18:25 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<990.470093, 214.230804, 10531.841797>)
20:18:26 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<991.493896, 214.230835, 10504.575195>)
20:18:27 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<989.404419, 214.230820, 10479.752930>)
20:18:27 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<988.185669, 214.230835, 10458.276367>)
20:18:29 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<986.857239, 214.230835, 10434.856445>)
20:18:29 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<985.546082, 214.230835, 10416.357422>)
20:18:47 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<986.726990, 214.230820, 10360.353516>)
20:18:47 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<985.967224, 214.230820, 10349.143555>)
20:18:48 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<986.430359, 214.230835, 10337.661133>)
20:18:52 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<989.595886, 214.230896, 10315.662109>)
20:19:21 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<978.165039, 214.230835, 10267.045898>)
20:19:22 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<975.335693, 214.230835, 10250.910156>)
20:20:8 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<967.093079, 214.230820, 10215.887695>)
20:20:9 | "{TITAN TESLA}" (steamid=76561197999250250) teleported to crosshair (pos=<966.349365, 214.230835, 10207.250000>)
20:21:47 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_Anzio_Black)
20:23:5 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_Anzio_3Rnd)
20:23:6 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_Anzio_3Rnd)
20:23:52 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (MF_HexTec_Mich2001)
20:23:54 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_20mmBox_10Rnd)
20:23:54 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_20mmBox_10Rnd)
20:23:55 | "{TITAN TESLA}" (steamid=76561197999250250) Spawned Item: (Paragon_20mmBox_10Rnd)
20:24:33 | "{TITAN TESLA}" (steamid=76561197999250250) deleted object "MF_HexTec_Mich2001" (pos=<971.452942, 214.235504, 10168.837891>)
20:24:53 | "{TITAN TESLA}" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
20:25:36 | Player "{TITAN TESLA}" (steamId=76561197999250250) initiated disconnect process...
