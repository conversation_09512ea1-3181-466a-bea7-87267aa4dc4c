{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_BUTTSTOCKS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "mp5k_stockbttstck", "MaxPriceThreshold": 2150, "MinPriceThreshold": 1290, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "fal_oebttstck", "MaxPriceThreshold": 2840, "MinPriceThreshold": 1705, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "fal_foldingbttstck", "MaxPriceThreshold": 2840, "MinPriceThreshold": 1705, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "saiga_bttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "aks74u_bttstck", "MaxPriceThreshold": 9915, "MinPriceThreshold": 5950, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ak74_woodbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ak_plasticbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ak_woodbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ak_foldingbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m4_oebttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m4_mpbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m4_cqbbttstck", "MaxPriceThreshold": 2130, "MinPriceThreshold": 1280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pp19_bttstck", "MaxPriceThreshold": 2150, "MinPriceThreshold": 1290, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ghillie<PERSON>_tan", "MaxPriceThreshold": 4390, "MinPriceThreshold": 2630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["ghillieatt_woodland", "ghillieatt_mossy", "ghillieatt_winter"]}]}