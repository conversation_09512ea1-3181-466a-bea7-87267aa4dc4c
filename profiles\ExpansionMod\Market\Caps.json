{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_CAPS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "baseballcap_cmmg_pink", "MaxPriceThreshold": 420, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["baseballcap_pink", "baseballcap_blue", "baseballcap_beige", "baseballcap_red", "baseballcap_cmmg_black", "baseballcap_black", "baseballcap_olive", "baseballcap_camo"]}, {"ClassName": "baseballcap_pink", "MaxPriceThreshold": 420, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseballcap_blue", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseballcap_beige", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseballcap_cmmg_black", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseballcap_black", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "baseballcap_camo", "MaxPriceThreshold": 1020, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "prisonercap", "MaxPriceThreshold": 590, "MinPriceThreshold": 355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pilotkacap", "MaxPriceThreshold": 9150, "MinPriceThreshold": 5490, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "policecap", "MaxPriceThreshold": 1150, "MinPriceThreshold": 690, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "flatcap_blue", "MaxPriceThreshold": 455, "MinPriceThreshold": 275, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["flatcap_red", "flatcap_brown", "flatcap_grey", "flatcap_browncheck", "flatcap_greycheck", "flatcap_black", "flatcap_blackcheck"]}, {"ClassName": "zmijovkacap_blue", "MaxPriceThreshold": 2420, "MinPriceThreshold": 1450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["zmijov<PERSON><PERSON>_brown", "zmijovkacap_red", "zmijovkacap_black", "zmijovkacap_green"]}, {"ClassName": "zmijovkacap_black", "MaxPriceThreshold": 4385, "MinPriceThreshold": 2630, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "zmijovkacap_green", "MaxPriceThreshold": 5595, "MinPriceThreshold": 3355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "radarcap_blue", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["radarcap_brown", "radarcap_red", "radarcap_black", "radarcap_green"]}, {"ClassName": "radarcap_brown", "MaxPriceThreshold": 455, "MinPriceThreshold": 275, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "radarcap_red", "MaxPriceThreshold": 455, "MinPriceThreshold": 275, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "radarcap_green", "MaxPriceThreshold": 455, "MinPriceThreshold": 275, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}