# Market Optimization Implementation Examples

## Example 1: Variants System Optimization

### BEFORE (Current TTC Attachments - 6 separate entries):
```json
{
  "ClassName": "TTC_EotechVudu_Optic",
  "MaxPriceThreshold": 12000,
  "MinPriceThreshold": 8000,
  // ... rest of config
},
{
  "ClassName": "TTC_EotechVudu_AK_Optic", 
  "MaxPriceThreshold": 12000,
  "MinPriceThreshold": 8000,
  // ... identical config
},
{
  "ClassName": "TTC_EotechVudu_Tan_Optic",
  "MaxPriceThreshold": 12000,
  "MinPriceThreshold": 8000,
  // ... identical config
}
// ... 3 more identical entries
```

### AFTER (Optimized with Variants):
```json
{
  "ClassName": "TTC_EotechVudu_Optic",
  "MaxPriceThreshold": 12000,
  "MinPriceThreshold": 8000,
  "SellPricePercent": -1,
  "MaxStockThreshold": 3,
  "MinStockThreshold": 1,
  "QuantityPercent": -1,
  "SpawnAttachments": [],
  "Variants": [
    "TTC_EotechVudu_AK_Optic",
    "TTC_EotechVudu_Tan_Optic", 
    "TTC_EotechVudu_Tan_AK_Optic",
    "TTC_EotechVudu_White_Optic",
    "TTC_EotechVudu_White_AK_Optic"
  ]
}
```
**Result**: 6 entries → 1 entry (83% reduction in file size)

---

## Example 2: Spawn Attachments Optimization

### BEFORE (Weapon without attachments):
```json
{
  "ClassName": "TTC_AK74",
  "MaxPriceThreshold": 15000,
  "MinPriceThreshold": 10000,
  "SpawnAttachments": [],
  "Variants": []
}
```

### AFTER (Weapon with realistic loadout):
```json
{
  "ClassName": "TTC_AK74",
  "MaxPriceThreshold": 18000,
  "MinPriceThreshold": 12000,
  "SpawnAttachments": [
    "TTC_AK74_Mag_30Rnd",
    "TTC_AKMod_Buttstock"
  ],
  "Variants": []
}
```
**Result**: Better player experience, realistic weapon loadouts, justified higher price

---

## Example 3: Consolidated Mod Files

### BEFORE (Multiple small files):
- `Snafu_Bipods_And_Grips.json` (8 items)
- `Snafu_Butt_Stocks.json` (12 items)  
- `Snafu_Hand_Gaurds.json` (15 items)
- `Snafu_Misc_And_Other.json` (6 items)
- `Snafu_Mounts.json` (4 items)
- `Snafu_Muzzles.json` (18 items)
- `Snafu_Optics_And_Scopes.json` (22 items)
- `Snafu_Suppressors.json` (11 items)

### AFTER (Single consolidated file):
`Snafu_Complete.json` (96 items total)
```json
{
  "m_Version": 12,
  "DisplayName": "SNAFU Weapons & Attachments",
  "Icon": "Deliver", 
  "Color": "FBFCFEFF",
  "IsExchange": 0,
  "InitStockPercent": 75,
  "Items": [
    // All 96 items organized by category
    // Optics section (22 items)
    // Suppressors section (11 items)  
    // Stocks section (12 items)
    // etc.
  ]
}
```
**Result**: 8 files → 1 file, easier management, faster loading

---

## Example 4: Pricing Tier Standardization

### BEFORE (Inconsistent pricing):
```json
// Basic food item
{"ClassName": "apple", "MaxPriceThreshold": 45, "MinPriceThreshold": 25}
// Similar food item  
{"ClassName": "pear", "MaxPriceThreshold": 180, "MinPriceThreshold": 95}
// Basic tool
{"ClassName": "hammer", "MaxPriceThreshold": 2500, "MinPriceThreshold": 1200}
```

### AFTER (Standardized tiers):
```json
// Tier 1: Basic consumables (100-500 credits)
{"ClassName": "apple", "MaxPriceThreshold": 300, "MinPriceThreshold": 200}
{"ClassName": "pear", "MaxPriceThreshold": 350, "MinPriceThreshold": 250}

// Tier 2: Basic tools (1000-3000 credits)  
{"ClassName": "hammer", "MaxPriceThreshold": 2000, "MinPriceThreshold": 1500}
```

---

## Example 5: Bundle Implementation

### New Bundle Category: "Weapon_Bundles.json"
```json
{
  "m_Version": 12,
  "DisplayName": "Weapon Bundles",
  "Icon": "Deliver",
  "Color": "FBFCFEFF", 
  "IsExchange": 0,
  "InitStockPercent": 50,
  "Items": [
    {
      "ClassName": "TTC_AK74",
      "MaxPriceThreshold": 25000,
      "MinPriceThreshold": 20000,
      "SellPricePercent": 60,
      "MaxStockThreshold": 3,
      "MinStockThreshold": 1,
      "SpawnAttachments": [
        "TTC_AK74_Mag_30Rnd",
        "TTC_AK74_Mag_30Rnd", 
        "TTC_AKMod_Buttstock",
        "TTC_Aimpoint2000",
        "Ammo_545x39"
      ],
      "Variants": []
    }
  ]
}
```
**Result**: Complete weapon system for premium price, better value for players

---

## Example 6: Reputation-Gated Trader

### Enhanced Black Market Trader:
```json
{
  "m_Version": 12,
  "DisplayName": "Black Market (Elite)",
  "MinRequiredReputation": 15000,
  "MaxRequiredReputation": **********,
  "RequiredFaction": "",
  "RequiredCompletedQuestID": -1,
  "TraderIcon": "Grenade",
  "Currencies": ["expansionbanknotehryvnia"],
  "DisplayCurrencyValue": 1,
  "DisplayCurrencyName": "",
  "Categories": [
    "Elite_Weapons",
    "Rare_Attachments", 
    "Special_Equipment",
    "Limited_Edition_Items"
  ]
}
```

---

## Example 7: Seasonal Rotation System

### Market Settings Enhancement:
```json
{
  "SeasonalRotation": {
    "Enabled": true,
    "RotationPeriodDays": 30,
    "SeasonalCategories": [
      {
        "CategoryName": "Winter_Gear",
        "ActiveMonths": [12, 1, 2],
        "PriceMultiplier": 1.2
      },
      {
        "CategoryName": "Summer_Clothing", 
        "ActiveMonths": [6, 7, 8],
        "PriceMultiplier": 0.8
      }
    ]
  }
}
```

---

## Implementation Priority Order

### Week 1: Quick Wins
1. Implement Variants system for TTC attachments
2. Add SpawnAttachments to weapons
3. Standardize stock levels

### Week 2: Consolidation  
1. Merge small mod files
2. Rename confusing categories
3. Standardize pricing tiers

### Week 3: Advanced Features
1. Create bundle categories
2. Add reputation requirements
3. Implement seasonal pricing

### Week 4: Testing & Refinement
1. Test all changes in-game
2. Adjust pricing based on player feedback
3. Monitor server performance
4. Fine-tune stock levels

---

## Expected File Reduction
- **Before**: 113 market files
- **After**: ~75 market files (33% reduction)
- **Performance**: 20-30% faster loading
- **Maintenance**: 50% easier to manage
