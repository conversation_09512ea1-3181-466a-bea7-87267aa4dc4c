=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-21_2-2-43
====================================================================================
2:2:43 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
2:2:43 | [PermissionManager] Perm (FreeCamera) has been registered!
2:2:43 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
2:2:43 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
2:2:43 | [PermissionManager] Perm (MenuServerManager) has been registered!
2:2:43 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
2:2:43 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
2:2:43 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
2:2:43 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
2:2:43 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
2:2:43 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
2:2:43 | [PermissionManager] Perm (MenuObjectManager) has been registered!
2:2:43 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
2:2:43 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
2:2:43 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
2:2:43 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
2:2:43 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
2:2:43 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
2:2:43 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
2:2:43 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
2:2:43 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
2:2:43 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
2:2:43 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
2:2:43 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
2:2:43 | [PermissionManager] Perm (MenuBansManager) has been registered!
2:2:43 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
2:2:43 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
2:2:43 | [PermissionManager] Perm (MenuWebHooks) has been registered!
2:2:43 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
2:2:43 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
2:2:43 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
2:2:43 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
2:2:43 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
2:2:43 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
2:2:43 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
2:2:43 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
2:2:43 | [PermissionManager] Adding Super Admin (76561199239979485)
2:2:43 | [PermissionManager] Adding Super Admin (76561197999250250)
2:2:43 | [PermissionManager] Adding Super Admin (76561198153347717)
2:2:43 | [PermissionManager] Loaded UserGroups.json
2:2:43 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
2:2:43 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
2:2:43 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
2:2:43 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
2:2:43 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
2:2:43 | Building Sets Loaded: 0
2:2:43 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
2:3:59 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
2:7:11 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (M79)
2:7:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:38 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:7:40 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Ammo_40mm_Explosive)
2:9:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Gun)
2:10:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:10:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (AA12_Mag)
2:12:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_PTRD41)
2:13:26 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "Paragon_PTRD41" (pos=<9440.648438, 139.490738, 1430.660645>)
2:13:43 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_CSR_Black)
2:14:36 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Ammo_20mm)
2:14:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Ammo_20mm)
2:14:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Ammo_20mm)
2:14:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_Ammo_20mm)
2:15:39 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Paragon_PMII_Optic)
2:21:13 | "(GB)BURT GUMMER" (steamid=76561197999250250) gave godmode to (steamid=76561197999250250)
2:21:36 | "76561197999250250" (steamid=76561197999250250) teleported to (pos=<3131.830078, 592.996033, 4121.959961>)
2:21:36 | "(GB)BURT GUMMER": (steamid=76561197999250250) teleported to (pos=3131.83,0,4121.96)
2:25:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<3004.132080, 573.147644, 4129.370117>)
2:28:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:28:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:28:54 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Firewood)
2:29:35 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MouthRag)
2:29:55 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Matchbox)
2:30:46 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CowSteakMeat)
2:30:47 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (CowSteakMeat)
2:44:44 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kraz_255B_kung)
3:15:50 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionZodiacBoat_Green)
3:16:10 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionZodiacBoat_Green" (pos=<4179.438477, 915.338196, 2327.072998>)
3:16:18 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionZodiacBoat_Green)
3:16:25 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionZodiacBoat_Green" (pos=<4180.424316, 915.435486, 2325.789307>)
3:16:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (ExpansionZodiacBoat_Green)
3:19:30 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (FishingRod)
3:19:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Hook)
3:21:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Worm)
3:21:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Worm)
3:21:9 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Worm)
3:25:29 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionZodiacBoat_Green" (pos=<4182.575195, 915.936768, 2344.591309>)
3:42:49 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "kraz_255B_kung" (pos=<5683.290527, 331.755219, 3642.277588>)
3:42:53 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
3:43:13 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
