{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_TENTS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "partytent", "MaxPriceThreshold": 5105, "MinPriceThreshold": 3060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["partytent_blue", "partytent_brown", "partytent_lunapark"]}, {"ClassName": "partytent_blue", "MaxPriceThreshold": 5105, "MinPriceThreshold": 3060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "partytent_brown", "MaxPriceThreshold": 5105, "MinPriceThreshold": 3060, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "partytent_lunapark", "MaxPriceThreshold": 6380, "MinPriceThreshold": 3830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mediumtent", "MaxPriceThreshold": 3925, "MinPriceThreshold": 2355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["mediumtent_orange", "mediumtent_green"]}, {"ClassName": "mediumtent_orange", "MaxPriceThreshold": 3925, "MinPriceThreshold": 2355, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "mediumtent_green", "MaxPriceThreshold": 5025, "MinPriceThreshold": 3015, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "largetent", "MaxPriceThreshold": 19140, "MinPriceThreshold": 11485, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cartent", "MaxPriceThreshold": 9250, "MinPriceThreshold": 5550, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncamotentkit", "MaxPriceThreshold": 5200, "MinPriceThreshold": 2600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncamoboxkit", "MaxPriceThreshold": 5200, "MinPriceThreshold": 2600, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}