{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_BAYONETS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "mosin_bayonet", "MaxPriceThreshold": 2315, "MinPriceThreshold": 1390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sks_bayonet", "MaxPriceThreshold": 5985, "MinPriceThreshold": 3590, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "m9a1_bayonet", "MaxPriceThreshold": 5245, "MinPriceThreshold": 3150, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ak_bayonet", "MaxPriceThreshold": 5245, "MinPriceThreshold": 3150, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansion_kar98_bayonet", "MaxPriceThreshold": 2315, "MinPriceThreshold": 1390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}