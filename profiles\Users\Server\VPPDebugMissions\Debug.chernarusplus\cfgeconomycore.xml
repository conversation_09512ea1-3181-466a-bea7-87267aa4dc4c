<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<economycore>
	<classes>
<!--
These are rootclasses to be used by economy.
Do not forget to add attribute act="character", if root class represents character (player, infected, animal)
Do not forget to add attribute act="car", if root class represents moveable vehicles
-->
		<rootclass name="DefaultWeapon" /> <!-- weapons -->
		<rootclass name="DefaultMagazine" /> <!-- magazines -->
		<rootclass name="Inventory_Base" /> <!-- inventory items -->
		<rootclass name="HouseNoDestruct" reportMemoryLOD="no" /> <!-- houses, wrecks -->
		<rootclass name="SurvivorBase" act="character" reportMemoryLOD="no" /> <!-- player characters -->
		<rootclass name="DZ_LightAI" act="character" reportMemoryLOD="no" /> <!-- infected, animals -->
		<rootclass name="CarScript" act="car" reportMemoryLOD="no" /> <!-- cars (sedan, hatchback, transitBus, V3S, ...) -->
		<rootclass name="BoatScript" act="car" reportMemoryLOD="no" /> <!-- boats -->
	</classes>
	<defaults>
		<default name="dyn_radius" value="30" />
		<default name="dyn_smin" value="0" />
		<default name="dyn_smax" value="0" />
		<default name="dyn_dmin" value="1" />
		<default name="dyn_dmax" value="5" />
		<default name="log_ce_loop" value="false"/>
		<default name="log_ce_dynamicevent" value="false"/>
		<default name="log_ce_vehicle" value="false"/>
		<default name="log_ce_lootspawn" value="false"/>
		<default name="log_ce_lootcleanup" value="false"/>
		<default name="log_ce_lootrespawn" value="false"/>
		<default name="log_ce_statistics" value="false"/>
		<default name="log_ce_zombie" value="false"/>
		<default name="log_storageinfo" value="false"/>
		<default name="log_hivewarning" value="true"/>
		<default name="log_missionfilewarning" value="true"/>
		<default name="save_events_startup" value="true"/>
		<default name="save_types_startup" value="true"/>
	</defaults>
</economycore>