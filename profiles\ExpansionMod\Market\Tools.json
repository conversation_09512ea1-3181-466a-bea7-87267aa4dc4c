{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_TOOLS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "screwdriver", "MaxPriceThreshold": 545, "MinPriceThreshold": 325, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "wrench", "MaxPriceThreshold": 545, "MinPriceThreshold": 325, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pliers", "MaxPriceThreshold": 545, "MinPriceThreshold": 325, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "whetstone", "MaxPriceThreshold": 625, "MinPriceThreshold": 375, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hammer", "MaxPriceThreshold": 865, "MinPriceThreshold": 520, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hacksaw", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "handsaw", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "canopener", "MaxPriceThreshold": 430, "MinPriceThreshold": 260, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "hatchet", "MaxPriceThreshold": 420, "MinPriceThreshold": 250, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "lockpick", "MaxPriceThreshold": 295, "MinPriceThreshold": 175, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "lugwrench", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pipewrench", "MaxPriceThreshold": 735, "MinPriceThreshold": 440, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "fryingpan", "MaxPriceThreshold": 450, "MinPriceThreshold": 270, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sickle", "MaxPriceThreshold": 1875, "MinPriceThreshold": 1125, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "crowbar", "MaxPriceThreshold": 1015, "MinPriceThreshold": 610, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "shovel", "MaxPriceThreshold": 3980, "MinPriceThreshold": 2390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pickaxe", "MaxPriceThreshold": 4495, "MinPriceThreshold": 2700, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "sledgehammer", "MaxPriceThreshold": 7470, "MinPriceThreshold": 4485, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "farminghoe", "MaxPriceThreshold": 440, "MinPriceThreshold": 265, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woodaxe", "MaxPriceThreshold": 3255, "MinPriceThreshold": 1955, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "firefighteraxe", "MaxPriceThreshold": 820, "MinPriceThreshold": 490, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "pitchfork", "MaxPriceThreshold": 655, "MinPriceThreshold": 395, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "blowtorch", "MaxPriceThreshold": 1115, "MinPriceThreshold": 670, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["smallgascanister"], "Variants": []}, {"ClassName": "fieldshovel", "MaxPriceThreshold": 2375, "MinPriceThreshold": 1425, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "meattenderizer", "MaxPriceThreshold": 475, "MinPriceThreshold": 285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "iceaxe", "MaxPriceThreshold": 410, "MinPriceThreshold": 245, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionpropanetorch", "MaxPriceThreshold": 1500, "MinPriceThreshold": 950, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionboltcutters", "MaxPriceThreshold": 620, "MinPriceThreshold": 390, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}