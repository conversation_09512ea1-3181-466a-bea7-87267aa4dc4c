{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_BLOUSES & #STR_EXPANSION_MARKET_CATEGORY_SUITS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "blouse_blue", "MaxPriceThreshold": 470, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["blouse_violet", "blouse_white", "blouse_green"]}, {"ClassName": "mansuit_beige", "MaxPriceThreshold": 1380, "MinPriceThreshold": 830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["mansuit_black", "mansuit_blue", "mansuit_brown", "mansuit_darkgrey", "mansuit_khaki", "mansuit_lightgrey", "mansuit_white"]}, {"ClassName": "mansuit_white", "MaxPriceThreshold": 1150, "MinPriceThreshold": 690, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_beige", "MaxPriceThreshold": 1380, "MinPriceThreshold": 830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["womansuit_black", "womansuit_blue", "womansuit_brown", "womansuit_darkgrey", "womansuit_khaki", "womansuit_lightgrey", "womansuit_white"]}, {"ClassName": "womansuit_black", "MaxPriceThreshold": 2140, "MinPriceThreshold": 1285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_blue", "MaxPriceThreshold": 2140, "MinPriceThreshold": 1285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_brown", "MaxPriceThreshold": 2140, "MinPriceThreshold": 1285, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_darkgrey", "MaxPriceThreshold": 1380, "MinPriceThreshold": 830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_khaki", "MaxPriceThreshold": 1380, "MinPriceThreshold": 830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_lightgrey", "MaxPriceThreshold": 1380, "MinPriceThreshold": 830, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "womansuit_white", "MaxPriceThreshold": 1150, "MinPriceThreshold": 690, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}