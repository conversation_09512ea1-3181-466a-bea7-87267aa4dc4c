=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-13_16-56-53
====================================================================================
16:56:53 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
16:56:53 | [PermissionManager] Perm (FreeCamera) has been registered!
16:56:53 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
16:56:53 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
16:56:53 | [PermissionManager] Perm (MenuServerManager) has been registered!
16:56:53 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
16:56:53 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
16:56:53 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
16:56:53 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
16:56:53 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
16:56:53 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
16:56:53 | [PermissionManager] Perm (MenuObjectManager) has been registered!
16:56:53 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
16:56:53 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
16:56:53 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
16:56:53 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
16:56:53 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
16:56:53 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
16:56:53 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
16:56:53 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
16:56:53 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
16:56:53 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
16:56:53 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
16:56:53 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
16:56:53 | [PermissionManager] Perm (MenuBansManager) has been registered!
16:56:53 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
16:56:53 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
16:56:53 | [PermissionManager] Perm (MenuWebHooks) has been registered!
16:56:53 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
16:56:53 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
16:56:53 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
16:56:53 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
16:56:53 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
16:56:53 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
16:56:53 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
16:56:53 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
16:56:53 | [PermissionManager] Adding Super Admin (76561199239979485)
16:56:53 | [PermissionManager] Adding Super Admin (76561197999250250)
16:56:53 | [PermissionManager] Adding Super Admin (76561198153347717)
16:56:53 | [PermissionManager] Loaded UserGroups.json
16:56:53 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
16:56:53 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
16:56:53 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
16:56:53 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
16:56:53 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
16:56:53 | Building Sets Loaded: 0
16:56:53 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
16:57:52 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
16:58:14 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:58:28 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<11074.099609, 435.678436, 11376.000000>)
16:58:28 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=11074.1,0,11376)
16:58:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11080.166016, 435.678375, 11358.791016>)
16:58:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11084.221680, 435.678436, 11350.481445>)
16:58:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11089.722656, 435.678436, 11333.495117>)
16:58:35 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11110.739258, 435.678436, 11320.719727>)
16:58:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11121.166992, 435.678406, 11302.374023>)
16:58:36 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11127.108398, 435.678375, 11293.032227>)
16:58:37 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11143.667969, 435.678436, 11279.385742>)
16:58:38 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11170.584961, 435.678589, 11265.134766>)
16:58:39 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11183.752930, 435.759430, 11259.256836>)
16:58:40 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11199.408203, 435.759430, 11253.920898>)
16:58:50 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11162.407227, 435.678986, 11261.544922>)
16:58:51 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11123.266602, 435.678436, 11269.655273>)
16:58:51 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11082.992188, 435.678467, 11278.000977>)
16:58:52 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11042.709961, 435.678436, 11286.342773>)
16:58:52 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11003.544922, 435.678467, 11294.430664>)
16:58:53 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10955.343750, 435.678528, 11299.833984>)
16:58:53 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10955.796875, 435.678436, 11297.807617>)
16:58:54 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10906.038086, 435.678497, 11300.911133>)
16:58:55 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10886.711914, 435.759430, 11301.703125>)
16:58:59 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10892.531250, 435.759430, 11301.556641>)
16:59:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10905.254883, 435.678467, 11297.335938>)
16:59:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10914.539063, 435.678406, 11309.551758>)
16:59:1 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10917.565430, 435.678436, 11324.762695>)
16:59:2 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10915.858398, 435.678436, 11338.408203>)
16:59:3 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10906.541016, 435.759430, 11343.096680>)
16:59:3 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10895.637695, 435.759430, 11345.974609>)
16:59:4 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10884.754883, 435.759430, 11348.380859>)
16:59:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10894.005859, 435.759430, 11355.355469>)
16:59:8 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10903.266602, 435.759430, 11350.305664>)
16:59:9 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10912.638672, 435.759430, 11344.961914>)
16:59:9 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10922.352539, 435.678406, 11339.428711>)
16:59:10 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10931.905273, 435.678467, 11333.583008>)
16:59:10 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10941.473633, 435.678467, 11327.820313>)
16:59:11 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10952.607422, 435.678436, 11324.347656>)
16:59:12 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11227.747070, 439.354584, 11248.894531>)
16:59:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11214.693359, 435.678436, 11230.792969>)
16:59:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11202.285156, 435.678436, 11231.082031>)
16:59:25 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11189.722656, 435.678589, 11231.481445>)
17:0:31 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11098.117188, 435.678406, 11236.581055>)
17:0:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11013.530273, 435.678375, 11242.300781>)
17:0:32 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10985.170898, 435.678436, 11244.232422>)
17:0:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10959.861328, 435.678467, 11246.632813>)
17:0:33 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10934.558594, 435.678497, 11249.089844>)
17:0:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10909.257813, 435.678467, 11251.545898>)
17:0:34 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10888.515625, 435.678467, 11253.662109>)
17:0:40 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
