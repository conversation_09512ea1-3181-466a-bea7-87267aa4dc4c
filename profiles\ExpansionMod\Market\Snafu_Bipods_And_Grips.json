{"m_Version": 12, "DisplayName": "Snafu_Bipods_And_Grips", "Icon": "Deliver", "Color": "FBFCFEFF", "InitStockPercent": 75, "Items": [{"ClassName": "SNAFU_M249Grip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M249Bipod", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_VSSKbipod", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SR3Grip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMPX_Grip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PKPBIPOD", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_NPKPBIPOD", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AtlasBipod", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_ModBipod", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AFG_02_Black", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AFG_02_Tan", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AFG_02_OD", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAR15_AFG", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUScar_AFG_BK", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUScar_AFG_Tan", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_GIGripColtA2", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_TACGripColtA2_BK", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_TACGripColtA2_Grey", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_TACGripColtA2_OD", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_TACGripColtA2_Tan", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_FGKAC_Grip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_FGCR_Grip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Bipod", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M200B_BK", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PistolGripColtA2", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SA58_FGrip", "MaxPriceThreshold": 2000, "MinPriceThreshold": 2000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}