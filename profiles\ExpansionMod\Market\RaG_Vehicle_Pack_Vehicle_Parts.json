{"m_Version": 12, "DisplayName": "RaG_Vehicle_Pack_Vehicle_Parts", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "rag_atv_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_atv_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_driverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_driverdoor_green", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_driverdoor_ryg", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_driverdoor_camo", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_codriverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_codriverdoor_green", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_codriverdoor_ryg", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_codriverdoor_camo", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_wheel_big", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_baja_wheel_big_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_driverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_driverdoor_green", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_driverdoor_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_driverdoor_yellow", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_codriverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_codriverdoor_green", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_codriverdoor_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_codriverdoor_yellow", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_derby_car_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_driverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_codriverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_trunk", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_gaz69_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_willy_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_willy_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_willy_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_driverdoor_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_codriverdoor_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_hood_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo1_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo2_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_cargo3_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_grey", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_un", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_black", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_pink", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_landrover_110_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_door_1_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_door_snow_1_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_door_1_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_door_snow_1_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_hood_snow", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_cargo1door", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_cargo1door_snow", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_wheel_snow", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_mtvr_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_schoolbus_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_schoolbus_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_schoolbus_wheel_double", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_schoolbus_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_schoolbus_wheel_double_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_driverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_codriverdoor", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_trunk", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_wheel_double", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_zil_130_wheel_double_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_olive_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_tan_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_camo_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_purple_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_winter_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_anarchy_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_gold_1", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_olive_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_tan_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_camo_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_purple_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_winter_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_anarchy_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_gold_2", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_olive_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_tan_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_camo_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_purple_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_winter_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_anarchy_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_gold_3", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_olive_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_tan_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_camo_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_purple_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_winter_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_anarchy_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_door_gold_4", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_tan", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_camo", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_purple", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_winter", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_anarchy", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_hood_gold", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_olive", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_tan", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_camo", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_purple", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_red", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_winter", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_anarchy", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_trunk_gold", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_wheel", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "rag_hummer_wheel_destroyed", "MaxPriceThreshold": 500, "MinPriceThreshold": 500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}