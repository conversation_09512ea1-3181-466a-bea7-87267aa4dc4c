{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_EVENT", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 510, "MinPriceThreshold": 305, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "crookednose", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "cauldron", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "witchhat", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "witchhood_black", "MaxPriceThreshold": 1800, "MinPriceThreshold": 1080, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["witchhood_brown", "witchhood_red"]}, {"ClassName": "paydaymask_dallas", "MaxPriceThreshold": 240, "MinPriceThreshold": 145, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["paydaymask_wolf", "paydaymask_hoxton", "paydaymask_chains"]}, {"ClassName": "candycane_green", "MaxPriceThreshold": 510, "MinPriceThreshold": 305, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["candycane_red", "candycane_redgreen", "candycane_yellow"]}, {"ClassName": "christmasheadband_antlers", "MaxPriceThreshold": 600, "MinPriceThreshold": 360, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["christmasheadband_gingerbread", "christmasheadband_trees"]}, {"ClassName": "pumpkinhelmet", "MaxPriceThreshold": 2370, "MinPriceThreshold": 1425, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "santasbeard", "MaxPriceThreshold": 425, "MinPriceThreshold": 255, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "santa<PERSON>t", "MaxPriceThreshold": 2470, "MinPriceThreshold": 1485, "SellPricePercent": -1.0, "MaxStockThreshold": 250, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "anniversarybox", "MaxPriceThreshold": 660, "MinPriceThreshold": 395, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "anniversary_fireworkslauncher", "MaxPriceThreshold": 335, "MinPriceThreshold": 200, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "giftbox_large_1", "MaxPriceThreshold": 465, "MinPriceThreshold": 280, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["giftbox_large_2", "giftbox_large_3", "giftbox_large_4", "giftbox_medium_1", "giftbox_medium_2", "giftbox_medium_3", "giftbox_medium_4", "giftbox_small_1", "giftbox_small_2", "giftbox_small_3", "giftbox_small_4"]}]}