=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-13_14-2-50
====================================================================================
14:2:50 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
14:2:50 | [PermissionManager] Perm (FreeCamera) has been registered!
14:2:50 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
14:2:50 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
14:2:50 | [PermissionManager] Perm (MenuServerManager) has been registered!
14:2:50 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
14:2:50 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
14:2:50 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
14:2:50 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
14:2:50 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
14:2:50 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
14:2:50 | [PermissionManager] Perm (MenuObjectManager) has been registered!
14:2:50 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
14:2:50 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
14:2:50 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
14:2:50 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
14:2:50 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
14:2:50 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
14:2:50 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
14:2:50 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
14:2:50 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
14:2:50 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
14:2:50 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
14:2:50 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
14:2:50 | [PermissionManager] Perm (MenuBansManager) has been registered!
14:2:50 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
14:2:50 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
14:2:50 | [PermissionManager] Perm (MenuWebHooks) has been registered!
14:2:50 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
14:2:50 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
14:2:50 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
14:2:50 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
14:2:50 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
14:2:50 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
14:2:50 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
14:2:50 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
14:2:50 | [PermissionManager] Adding Super Admin (76561199239979485)
14:2:50 | [PermissionManager] Adding Super Admin (76561197999250250)
14:2:50 | [PermissionManager] Adding Super Admin (76561198153347717)
14:2:50 | [PermissionManager] Loaded UserGroups.json
14:2:50 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
14:2:50 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
14:2:50 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
14:2:50 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
14:2:50 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
14:2:50 | Building Sets Loaded: 0
14:2:50 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
16:22:44 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
16:26:49 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
16:27:14 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
16:49:37 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
16:51:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10202.250000, 284.860138, 10342.282227>)
16:51:58 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:53:20 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:53:21 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:53:25 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:53:26 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
16:53:32 | "BNeaveill99" (steamid=76561198153347717) healed player (steamid=76561198153347717)
16:53:34 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
16:53:37 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
16:53:38 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
16:53:40 | "BNeaveill99" (steamid=76561198153347717) just updated a health stat on (steamid=76561198153347717)
16:53:53 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10119.012695, 261.950623, 10304.359375>)
16:53:54 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10102.464844, 258.566803, 10293.251953>)
16:54:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<10075.377930, 249.960724, 10219.375977>)
16:54:30 | "76561198153347717" (steamid=76561198153347717) teleported to (pos=<11067.700195, 435.678436, 11402.799805>)
16:54:30 | "BNeaveill99": (steamid=76561198153347717) teleported to (pos=11067.7,0,11402.8)
16:54:56 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11054.518555, 435.678436, 11342.789063>)
16:55:2 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11077.208008, 435.678467, 11322.058594>)
16:55:12 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<11197.069336, 435.759430, 11244.698242>)
16:55:35 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
16:55:36 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
