{"ClassName": "", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Body", "Items": [{"ClassName": "GorkaEJacket_Autumn", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_Flat", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_PautRev", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_Summer", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTsKOJacket_Camo", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Legs", "Items": [{"ClassName": "GorkaPants_Autumn", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaPants_Flat", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaPants_Summer", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTSKOPants", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Feet", "Items": [{"ClassName": "CombatBoots_Beige", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Brown", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Grey", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTSKOBoots", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Back", "Items": [{"ClassName": "TortillaBag", "Include": "", "Chance": 0.6000000238418579, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [{"ClassName": "MakarovIJ70", "Include": "", "Chance": 0.3400000035762787, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_Black", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_White", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x39", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AssaultBag_Ttsko", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [{"ClassName": "MakarovIJ70", "Include": "", "Chance": 0.3400000035762787, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_Black", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_White", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x39", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "SmershBag", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [{"ClassName": "MakarovIJ70", "Include": "", "Chance": 0.3400000035762787, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Chemlight_Green", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_Black", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "RDG2SmokeGrenade_White", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x39", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Vest", "Items": [{"ClassName": "SmershVest", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "SmershBag", "Include": "", "Chance": 0.800000011920929, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "UKAssVest_Olive", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "UKAssVest_Camo", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HighCapacityVest_Olive", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Headgear", "Items": [{"ClassName": "Ssh68Helmet", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TankerHelmet", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PilotkaCap", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "OfficerHat", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaHelmet", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [{"SlotName": "Glass", "Items": [{"ClassName": "GorkaHelmetVisor", "Include": "", "Chance": 0.4000000059604645, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Gloves", "Items": [{"ClassName": "OMNOGloves_Brown", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "OMNOGloves_Gray", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Hips", "Items": [{"ClassName": "MilitaryBelt", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Apple", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BandageDressing", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": [{"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AK101", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AK74", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_Hndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AKS74U", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AKS74U_Bttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AKM", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 0.8999999761581421, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AKM_Drum75Rnd", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "SVD", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_SVD_10Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_SVD_10Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_SVD_10Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "PP19", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_PP19_64Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PP19_Bttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PistolSuppressor", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_PP19_64Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_PP19_64Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "FAL", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_FAL_20Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Fal_FoldingBttstck", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Fal_OeBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "ACOGOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "ReflexOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_FAL_20Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_FAL_20Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}]}