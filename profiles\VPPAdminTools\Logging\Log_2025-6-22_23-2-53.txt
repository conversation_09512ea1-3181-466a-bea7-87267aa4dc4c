=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-22_23-2-53
====================================================================================
23:2:53 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
23:2:53 | [PermissionManager] Perm (FreeCamera) has been registered!
23:2:53 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
23:2:53 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
23:2:53 | [PermissionManager] Perm (MenuServerManager) has been registered!
23:2:53 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
23:2:53 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
23:2:53 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
23:2:53 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
23:2:53 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
23:2:53 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
23:2:53 | [PermissionManager] Perm (MenuObjectManager) has been registered!
23:2:53 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
23:2:53 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
23:2:53 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
23:2:53 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
23:2:53 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
23:2:53 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
23:2:53 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
23:2:53 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
23:2:53 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
23:2:53 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
23:2:53 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
23:2:53 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
23:2:53 | [PermissionManager] Perm (MenuBansManager) has been registered!
23:2:53 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
23:2:53 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
23:2:53 | [PermissionManager] Perm (MenuWebHooks) has been registered!
23:2:53 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
23:2:53 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
23:2:53 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
23:2:53 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
23:2:53 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
23:2:53 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
23:2:53 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
23:2:53 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
23:2:53 | [PermissionManager] Adding Super Admin (76561199239979485)
23:2:53 | [PermissionManager] Adding Super Admin (76561197999250250)
23:2:53 | [PermissionManager] Adding Super Admin (76561198153347717)
23:2:53 | [PermissionManager] Loaded UserGroups.json
23:2:53 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
23:2:53 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
23:2:53 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
23:2:53 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
23:2:53 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
23:2:53 | Building Sets Loaded: 0
23:2:53 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
23:32:48 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) connected to server!
23:33:6 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
23:43:19 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionUAZDoorCargo1_LightGreen" (pos=<10003.067383, 380.133362, 4038.751709>)
23:43:24 | "(GB)BURT GUMMER" (steamid=76561197999250250) deleted object "ExpansionUAZ_LightGreen" (pos=<10002.156250, 378.812744, 4038.683594>)
23:44:52 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_kung)
23:44:59 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (kamaz_4310RUS_kung)
0:24:59 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
0:25:0 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<3692.790527, 1008.138916, 2162.296387>)
0:32:24 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (PetrolLighter)
0:32:56 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (StorageBox_BigTent_Green)
0:33:2 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (PetrolLighter)
0:34:1 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (RenegadeHatchet)
0:35:11 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Pickaxe)
0:35:57 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Pickaxe)
0:36:37 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (rag_baseitems_medieval_fireplace_kit)
0:37:17 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4162.400391, 916.537415, 2363.212646>)
0:37:19 | "BNeaveill99" (steamid=76561198153347717) teleported to crosshair (pos=<4173.430664, 915.508057, 2353.302734>)
0:38:5 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (MouthRag)
0:39:57 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Stone)
0:41:17 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paper)
0:41:18 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paper)
0:41:18 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paper)
0:41:18 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paper)
0:44:51 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Boat_01_Blue)
0:45:33 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (FishingRod)
0:45:42 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Worm)
0:46:40 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Hook)
0:46:41 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Hook)
0:46:42 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Hook)
0:46:42 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Hook)
0:48:22 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Worm)
0:48:23 | "(GB)BURT GUMMER" (steamid=76561197999250250) Spawned Item: (Worm)
0:50:33 | "(GB)BURT GUMMER" (steamid=76561197999250250) teleported to crosshair (pos=<4183.033203, 915.208801, 2343.977783>)
0:50:39 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) initiated disconnect process...
0:50:59 | Player "(GB)BURT GUMMER" (steamId=76561197999250250) disconnected from server.
0:51:41 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
0:51:43 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
