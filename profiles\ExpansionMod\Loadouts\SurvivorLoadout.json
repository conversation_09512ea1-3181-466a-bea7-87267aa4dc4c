{"ClassName": "", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Body", "Items": [{"ClassName": "GorkaEJacket_Autumn", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_Flat", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_PautRev", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaEJacket_Summer", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTsKOJacket_Camo", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_Blue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJack<PERSON>_Brown", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_Grey", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_Maroon", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_Olive", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "BomberJacket_SkyBlue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "DenimJacket", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HikingJacket_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HikingJacket_Blue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HikingJacket_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HikingJacket_Red", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Blue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Quilted<PERSON><PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Orange", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Red", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Violet", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "QuiltedJacket_Yellow", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>Check", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Shirt_PlaneBlack", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Legs", "Items": [{"ClassName": "GorkaPants_Autumn", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaPants_Flat", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "GorkaPants_Summer", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTSKOPants", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON>s_BlueDark", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TrackSuitPants_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TrackSuitPants_Blue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TrackSuitPants_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TrackSuitPants_LightBlue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TrackSuitPants_Red", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CargoPants_Beige", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CargoPants_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CargoPants_Blue", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CargoPants_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CargoPants_Grey", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Feet", "Items": [{"ClassName": "CombatBoots_Beige", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Brown", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Green", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CombatBoots_Grey", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "TTSKOBoots", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingBoots_Beige", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingBoots_Grey", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingBoots_Yellow", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Gloves", "Items": [{"ClassName": "WorkingGloves_Beige", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingGloves_Black", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingGlove<PERSON>_<PERSON>", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WorkingGloves_Yellow", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "OMNOGloves_Brown", "Include": "", "Chance": 0.019999999552965164, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "OMNOGloves_Gray", "Include": "", "Chance": 0.019999999552965164, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "Hips", "Items": [{"ClassName": "MilitaryBelt", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "CivilianBelt", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}, {"SlotName": "<PERSON><PERSON>", "Items": [{"ClassName": "BaseballBat", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "FirefighterAxe", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON><PERSON>", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Pipe<PERSON><PERSON>", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "<PERSON><PERSON>", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Pickaxe", "Include": "", "Chance": 0.05000000074505806, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Crowbar", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [{"Min": 0.699999988079071, "Max": 1.0, "Zone": ""}], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": [{"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AK101", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK101_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AK74", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_Hndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AKS74U", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AKS74U_Bttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AK74_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "AKM", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 0.8999999761581421, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AKM_Drum75Rnd", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_FoldingBttstck", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK74_WoodBttstck", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodBttstck", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_PlasticHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "AK_WoodHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KashtanOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "KobraOptic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "PSO1Optic", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_AKM_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "MP5K", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_MP5_30Rnd", "Include": "", "Chance": 0.800000011920929, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_MP5_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "MP5k_StockBttstck", "Include": "", "Chance": 0.800000011920929, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "MP5_PlasticHndgrd", "Include": "", "Chance": 0.800000011920929, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "MP5_RailHndgrd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "MP5_Compensator", "Include": "", "Chance": 0.20000000298023224, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "ReflexOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_MP5_15Rnd", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_MP5_15Rnd", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_MP5_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "Mosin9130", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "PUScopeOptic", "Include": "", "Chance": 0.4000000059604645, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Ammo_762x54", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x54", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x54", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "SKS", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "PUScopeOptic", "Include": "", "Chance": 0.30000001192092896, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Ammo_762x39", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x39", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_762x39", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "Ruger1022", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_Ruger1022_30Rnd", "Include": "", "Chance": 0.4000000059604645, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_Ruger1022_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_Ruger1022_15Rnd", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_Ruger1022_15Rnd", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_Ruger1022_30Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "CZ550", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_CZ550_4rnd", "Include": "", "Chance": 0.6000000238418579, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_CZ550_10rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HuntingOptic", "Include": "", "Chance": 0.4000000059604645, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_CZ550_10rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_CZ550_10rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "P1", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_P1_8Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_P1_8Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_P1_8Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "Mp133Shotgun", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Ammo_12gaSlug", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_12gaPellets", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_12gaSlug", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Ammo_12gaPellets", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "CZ527", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_CZ527_5rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "HuntingOptic", "Include": "", "Chance": 0.5, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_CZ527_5rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_CZ527_5rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "UMP45", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_UMP_25Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "ReflexOptic", "Include": "", "Chance": 0.10000000149011612, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Battery9V", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_UMP_25Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_UMP_25Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Shoulder", "Items": [{"ClassName": "CZ61", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_CZ61_20Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_CZ61_20Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_CZ61_20Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "FNX45", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_FNX45_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_FNX45_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_FNX45_15Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "MKII", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_MKII_10Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_MKII_10Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_MKII_10Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "CZ75", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_CZ75_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_CZ75_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_CZ75_15Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "MakarovIJ70", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_IJ70_8Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "WEAPON", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "Hands", "Items": [{"ClassName": "Glock19", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [{"SlotName": "", "Items": [{"ClassName": "Mag_Glock_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}]}], "InventoryCargo": [{"ClassName": "Mag_Glock_15Rnd", "Include": "", "Chance": 1.0, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}, {"ClassName": "Mag_Glock_15Rnd", "Include": "", "Chance": 0.15000000596046448, "Quantity": {"Min": 0.0, "Max": 0.0}, "Health": [], "InventoryAttachments": [], "InventoryCargo": [], "ConstructionPartsBuilt": [], "Sets": []}], "ConstructionPartsBuilt": [], "Sets": []}]}