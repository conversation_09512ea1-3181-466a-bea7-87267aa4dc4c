{"m_Version": 12, "DisplayName": "Paragon Arsenal", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75, "Items": [{"ClassName": "Paragon_50BMGBox_10Rnd", "MaxPriceThreshold": 43500, "MinPriceThreshold": 33000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_12x55Box_10Rnd", "MaxPriceThreshold": 23500, "MinPriceThreshold": 23000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_408Box_15Rnd", "MaxPriceThreshold": 23500, "MinPriceThreshold": 23000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_338Box_20Rnd", "MaxPriceThreshold": 22500, "MinPriceThreshold": 22000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_BeoWulfBox_25Rnd", "MaxPriceThreshold": 15000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_300BlkBox_30Rnd", "MaxPriceThreshold": 13500, "MinPriceThreshold": 13000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AFG_Black", "MaxPriceThreshold": 420000, "MinPriceThreshold": 320000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_AFG_Tan", "Paragon_AFG_Green"]}, {"ClassName": "Paragon_Bipod_Black", "MaxPriceThreshold": 8000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Bipod_Tan", "Paragon_Bipod_Green"]}, {"ClassName": "Paragon_ForeGrip_Black", "MaxPriceThreshold": 8000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_ForeGrip_Tan", "Paragon_ForeGrip_Green"]}, {"ClassName": "Paragon_StubbyGrip_Black", "MaxPriceThreshold": 7000, "MinPriceThreshold": 4000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_StubbyGrip_Tan", "Paragon_StubbyGrip_Green"]}, {"ClassName": "Paragon_338_<PERSON><PERSON>_<PERSON>", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_338_Sup_Black", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_408_Sup", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_50BMG_Sup_Black", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_50BMG_Sup_Grey", "Paragon_50BMG_Sup_Green", "Paragon_50BMG_Sup_Tan", "Paragon_50BMG_Sup_Camo"]}, {"ClassName": "Paragon_Combat_Sup_Black", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Combat_Sup_Green", "Paragon_Combat_Sup_Tan"]}, {"ClassName": "Paragon_SMG_Sup_Black", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Universal_Sup_Black", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Universal_Sup_Green", "Paragon_Universal_Sup_Tan", "Paragon_Universal_Sup_Camo"]}, {"ClassName": "Paragon_Aero", "MaxPriceThreshold": 380000, "MinPriceThreshold": 320000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AR50", "MaxPriceThreshold": 480000, "MinPriceThreshold": 420000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CDX", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CSR", "MaxPriceThreshold": 128000, "MinPriceThreshold": 122000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CSR_Black", "MaxPriceThreshold": 128000, "MinPriceThreshold": 122000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_PTRD41", "MaxPriceThreshold": 18000, "MinPriceThreshold": 12000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AR15", "MaxPriceThreshold": 240000, "MinPriceThreshold": 233000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HK", "MaxPriceThreshold": 128000, "MinPriceThreshold": 122000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_HT", "MaxPriceThreshold": 128000, "MinPriceThreshold": 122000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MCX_Black", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_MCX_Tan"]}, {"ClassName": "Paragon_MDR_Tan", "MaxPriceThreshold": 115000, "MinPriceThreshold": 15000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_MDR_Black"]}, {"ClassName": "Paragon_MK14_Black", "MaxPriceThreshold": 120000, "MinPriceThreshold": 118000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MK14_White", "MaxPriceThreshold": 120000, "MinPriceThreshold": 118000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SA58", "MaxPriceThreshold": 140000, "MinPriceThreshold": 138000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Scar", "MaxPriceThreshold": 148000, "MinPriceThreshold": 142000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_MP7_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 18000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_MP7_Tan"]}, {"ClassName": "Paragon_MPX_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 18000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_<PERSON><PERSON><PERSON>_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 18000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Vector_Black", "MaxPriceThreshold": 20000, "MinPriceThreshold": 18000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Vector_Tan"]}, {"ClassName": "Paragon_AXMC", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Blaze_Black", "MaxPriceThreshold": 120000, "MinPriceThreshold": 118000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Delta_Black", "MaxPriceThreshold": 1200000, "MinPriceThreshold": 118000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Delta_Tan"]}, {"ClassName": "Paragon_DVL10", "MaxPriceThreshold": 150000, "MinPriceThreshold": 148000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DVL10S", "MaxPriceThreshold": 150000, "MinPriceThreshold": 148000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Kalamit", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_KR65_Black", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M100", "MaxPriceThreshold": 138000, "MinPriceThreshold": 132000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M100_Black", "MaxPriceThreshold": 138000, "MinPriceThreshold": 132000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M300", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M300_Grey", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M300_Black", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NTW", "MaxPriceThreshold": 118000, "MinPriceThreshold": 112000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Remington_Black", "MaxPriceThreshold": 228000, "MinPriceThreshold": 222000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["Paragon_Remington_Blue", "Paragon_Remington_Tan"]}, {"ClassName": "Paragon_Rogue", "MaxPriceThreshold": 128000, "MinPriceThreshold": 122000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SV98", "MaxPriceThreshold": 140000, "MinPriceThreshold": 140000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_XR80_Black", "MaxPriceThreshold": 980000, "MinPriceThreshold": 980000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TTS", "MaxPriceThreshold": 18000, "MinPriceThreshold": 12000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SRS", "MaxPriceThreshold": 18000, "MinPriceThreshold": 12000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_CDX_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_AXMC_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Delta_10Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_DVL10_10Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Kalamit_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_KR65_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M100_7Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_M300_7Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NTS_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_NTW_3Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Remington_10Rnd", "MaxPriceThreshold": 27000, "MinPriceThreshold": 26000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Rogue_7Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SV98_7Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_XR80_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_TTS_10Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_SRS_10Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Beowulf_20Rnd", "MaxPriceThreshold": 32000, "MinPriceThreshold": 32000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_300BLK_60Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_308_50Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_SA58_50Rnd", "MaxPriceThreshold": 27000, "MinPriceThreshold": 26000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_9x19_60Rnd", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_AR15_30Rnd", "MaxPriceThreshold": 19000, "MinPriceThreshold": 18000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_HK_25Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_HT_25Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_MCX_15Rnd", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_MK14_20Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_SA58_25Rnd", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Scar_5Rnd", "MaxPriceThreshold": 17000, "MinPriceThreshold": 16000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_MP7_30Rnd", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_MPX_30Rnd", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Scorpion_30Rnd", "MaxPriceThreshold": 9000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Paragon_Mag_Vector_30Rnd", "MaxPriceThreshold": 7000, "MinPriceThreshold": 6000, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}