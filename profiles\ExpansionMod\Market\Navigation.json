{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_NAVIGATION", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "compass", "MaxPriceThreshold": 1600, "MinPriceThreshold": 960, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "orienteeringcompass", "MaxPriceThreshold": 555, "MinPriceThreshold": 330, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "chern<PERSON><PERSON><PERSON>", "MaxPriceThreshold": 910, "MinPriceThreshold": 545, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "binoculars", "MaxPriceThreshold": 1530, "MinPriceThreshold": 915, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "gpsreceiver", "MaxPriceThreshold": 1425, "MinPriceThreshold": 855, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}, {"ClassName": "expansiongps", "MaxPriceThreshold": 1450, "MinPriceThreshold": 950, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": ["battery9v"], "Variants": []}]}