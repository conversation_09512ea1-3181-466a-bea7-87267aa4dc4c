{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_LOCKS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "combinationlock", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "combinationlock4", "MaxPriceThreshold": 750, "MinPriceThreshold": 450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansioncodelock", "MaxPriceThreshold": 3000, "MinPriceThreshold": 1500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}