=========================Vanilla++ Admin Tools Session Logs=========================
Logs Started: 2025-6-20_8-2-47
====================================================================================
8:2:47 | [PermissionManager] Perm (DeleteObjectAtCrosshair) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportToCrosshair) has been registered!
8:2:47 | [PermissionManager] Perm (FreeCamera) has been registered!
8:2:47 | [PermissionManager] Perm (RepairVehiclesAtCrosshair) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager:SpawnItem) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager:EditPreset) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager:SpawnPreset) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager:DeletePreset) has been registered!
8:2:47 | [PermissionManager] Perm (MenuItemManager:AddPreset) has been registered!
8:2:47 | [PermissionManager] Perm (MenuServerManager) has been registered!
8:2:47 | [PermissionManager] Perm (ServerManager:RestartServer) has been registered!
8:2:47 | [PermissionManager] Perm (ServerManager:LockServer) has been registered!
8:2:47 | [PermissionManager] Perm (ServerManager:KickAllPlayers) has been registered!
8:2:47 | [PermissionManager] Perm (ServerManager:LoadScripts) has been registered!
8:2:47 | [PermissionManager] Perm (MenuWeatherManager) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:ApplyWeather) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:ApplyTime) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:SavePreset) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:DeletePreset) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:ApplyPreset) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:ApplyTimePreset) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:SaveTimePreset) has been registered!
8:2:47 | [PermissionManager] Perm (WeatherManager:DeleteTimePreset) has been registered!
8:2:47 | [PermissionManager] Perm (MenuObjectManager) has been registered!
8:2:47 | [PermissionManager] Perm (MenuObjectManager:CreateNewSet) has been registered!
8:2:47 | [PermissionManager] Perm (MenuObjectManager:UpdateSet) has been registered!
8:2:47 | [PermissionManager] Perm (MenuObjectManager:DeleteSet) has been registered!
8:2:47 | [PermissionManager] Perm (MenuObjectManager:EditSet) has been registered!
8:2:47 | [PermissionManager] Perm (MenuPermissionsEditor) has been registered!
8:2:47 | [PermissionManager] Perm (PermissionsEditor:RemoveUser) has been registered!
8:2:47 | [PermissionManager] Perm (PermissionsEditor:AddUser) has been registered!
8:2:47 | [PermissionManager] Perm (PermissionsEditor:CreateUserGroup) has been registered!
8:2:47 | [PermissionManager] Perm (PermissionsEditor:DeleteUserGroup) has been registered!
8:2:47 | [PermissionManager] Perm (PermissionsEditor:ChangePermLevel) has been registered!
8:2:47 | [PermissionManager] Perm (MenuPlayerManager) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:GiveGodmode) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:BanPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:KickPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:HealPlayers) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:SetPlayerStats) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:KillPlayers) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:GodMode) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:SpectatePlayer) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:TeleportToPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:TeleportPlayerTo) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:SetPlayerInvisible) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:SendMessage) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:GiveUnlimitedAmmo) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:MakePlayerVomit) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:FreezePlayers) has been registered!
8:2:47 | [PermissionManager] Perm (PlayerManager:ChangeScale) has been registered!
8:2:47 | [PermissionManager] Perm (MenuBansManager) has been registered!
8:2:47 | [PermissionManager] Perm (BansManager:UnbanPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (BansManager:UpdateBanDuration) has been registered!
8:2:47 | [PermissionManager] Perm (BansManager:UpdateBanReason) has been registered!
8:2:47 | [PermissionManager] Perm (MenuWebHooks) has been registered!
8:2:47 | [PermissionManager] Perm (MenuWebHooks:Create) has been registered!
8:2:47 | [PermissionManager] Perm (MenuWebHooks:Edit) has been registered!
8:2:47 | [PermissionManager] Perm (MenuWebHooks:Delete) has been registered!
8:2:47 | [PermissionManager] Perm (MenuTeleportManager) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:ViewPlayerPositions) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:TPPlayers) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:TPSelf) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:DeletePreset) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:AddNewPreset) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:EditPreset) has been registered!
8:2:47 | [PermissionManager] Perm (TeleportManager:TeleportEntity) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:DeleteObjects) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:PlayerESP) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:RestPasscodeFence) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:RetriveCodeFromObj) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:PlayerMeshEsp) has been registered!
8:2:47 | [PermissionManager] Perm (EspToolsMenu:InstantBaseBuild) has been registered!
8:2:47 | [PermissionManager] Perm (MenuXMLEditor) has been registered!
8:2:47 | [PermissionManager] Perm (MenuCommandsConsole) has been registered!
8:2:47 | [PermissionManager] Adding Super Admin (76561199239979485)
8:2:47 | [PermissionManager] Adding Super Admin (76561197999250250)
8:2:47 | [PermissionManager] Adding Super Admin (76561198153347717)
8:2:47 | [PermissionManager] Loaded UserGroups.json
8:2:47 | [BansManager]:: Load(): Loading ban list Json File $profile:VPPAdminTools/BanList.json
8:2:47 | [PermissionManager] Perm (Chat:StripPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:KillPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:HealPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:BringPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:ReturnPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:GotoPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:UnbanPlayer) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:TeleportToTown) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:TeleportToPoint) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:GiveAmmo) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:SpawnInventory) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:SpawnOnGround) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:SpawnInHands) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:SpawnCar) has been registered!
8:2:47 | [PermissionManager] Perm (Chat:refuelCar) has been registered!
8:2:47 | [WeatherManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/WeatherManager/WeatherSettingsPresets.json
8:2:47 | [TimeManager] Loading Json File $profile:VPPAdminTools/ConfigurablePlugins/TimeManager/TimeSettingsPresets.json
8:2:47 | Building Sets Loaded: 0
8:2:47 | [SteamAPIManager] ERROR No steam API key configured, and or key is invalid.
8:3:42 | Player "BNeaveill99" (steamId=76561198153347717) connected to server!
8:3:54 | "BNeaveill99" (steamid=76561198153347717) toggled godmode
8:4:6 | "BNeaveill99" (steamid=76561198153347717) deleted object "GAZ_66" (pos=<9967.514648, 230.457916, 7123.979004>)
8:8:48 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (kamaz_4310RUS_kung)
8:12:31 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_WolfBarrel)
8:12:32 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_WolfBarrel)
8:13:9 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_WolfBarrel)
8:13:41 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_BearBarrel)
8:14:31 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:14:32 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:14:32 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:14:32 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:14:33 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:14:33 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Paragon_Wood_Crate)
8:15:54 | "BNeaveill99" (steamid=76561198153347717) deleted object "Renegade_WolfBarrel" (pos=<9964.010742, 230.180237, 7124.632324>)
8:15:57 | "BNeaveill99" (steamid=76561198153347717) deleted object "Renegade_WolfBarrel" (pos=<9964.199219, 230.144760, 7124.837891>)
8:16:41 | "BNeaveill99" (steamid=76561198153347717) deleted object "Renegade_BlackAdminBag" (pos=<9969.989258, 230.395981, 7129.258789>)
8:17:22 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_BlackAdminBag)
8:18:8 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:8 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:8 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:8 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:9 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:9 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:18:38 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (WoodenCrate)
8:19:7 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (Renegade_BlackAdminBag)
8:20:48 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (ExpansionAdminChest)
8:20:48 | "BNeaveill99" (steamid=76561198153347717) Spawned Item: (ExpansionAdminChest)
8:23:14 | "BNeaveill99" (steamid=76561198153347717) deleted object "WoodenCrate" (pos=<9971.486328, 230.203186, 7132.604980>)
8:23:17 | "BNeaveill99" (steamid=76561198153347717) deleted object "WoodenCrate" (pos=<9971.486328, 230.203186, 7132.604980>)
8:42:40 | Player "BNeaveill99" (steamId=76561198153347717) initiated disconnect process...
8:42:42 | Player "BNeaveill99" (steamId=76561198153347717) disconnected early from server (EXIT NOW).
