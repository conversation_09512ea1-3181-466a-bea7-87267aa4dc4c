{"m_Version": 12, "DisplayName": "#STR_EXPANSION_MARKET_CATEGORY_CONTAINERS", "Icon": "Deliver", "Color": "FBFCFEFF", "IsExchange": 0, "InitStockPercent": 75.0, "Items": [{"ClassName": "smallprotectorcase", "MaxPriceThreshold": 5670, "MinPriceThreshold": 3405, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "ammobox", "MaxPriceThreshold": 57420, "MinPriceThreshold": 34450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "barrelholes_blue", "MaxPriceThreshold": 57420, "MinPriceThreshold": 34450, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["barrelholes_green", "barrelholes_red", "barrelholes_yellow"]}, {"ClassName": "barrel_blue", "MaxPriceThreshold": 7500, "MinPriceThreshold": 4500, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": ["barrel_green", "barrel_red", "barrel_yellow"]}, {"ClassName": "seachest", "MaxPriceThreshold": 9040, "MinPriceThreshold": 5425, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "woodencrate", "MaxPriceThreshold": 4000, "MinPriceThreshold": 2000, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansiontoolbox", "MaxPriceThreshold": 5670, "MinPriceThreshold": 3405, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionsafemini", "MaxPriceThreshold": 9040, "MinPriceThreshold": 5425, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionsafemedium", "MaxPriceThreshold": 12000, "MinPriceThreshold": 6000, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "expansionsafelarge", "MaxPriceThreshold": 16000, "MinPriceThreshold": 8000, "SellPricePercent": -1.0, "MaxStockThreshold": 100, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}