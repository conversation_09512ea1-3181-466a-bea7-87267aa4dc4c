{"m_Version": 12, "DisplayName": "<PERSON><PERSON><PERSON>_Mags_And_Clips", "Icon": "Deliver", "Color": "FBFCFEFF", "InitStockPercent": 75, "Items": [{"ClassName": "GCGN_MP7_40Rnd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_VEPR_10rnd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "AA12_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNHK417_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SG550_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Tec9_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "VR80_Magazine_5rd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "VR80_Magazine_10rd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SN_Auto_Shotgun_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SN_M9_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SN_Mp443_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SN_Springfield_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SN_USP_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_Mag_FAL_50Rnd", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M249_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_Vityaz_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_HK416_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M110_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M200_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M700_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "GCGN_M82_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AK103_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AK12_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AK308_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PDW_9mm_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PDW_45ACP_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PL14HQP_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_RPK16_Black_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_RPK16_Green_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_RPK16_Tan_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_KAC_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Saint_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PMAG556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PMAG556_Grey", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PMAG556_OD", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_PMAG556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_BMAG556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_BMAG556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_BMAG300_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_BMAG300_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAR15_SPR_100RND_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG30556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG30556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG30300_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG30300_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAR15_SPR_30RND_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG40556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG40556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG40556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG40556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAR15_SPR_40RND_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG60556_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG60556_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG60300_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_CMAG60300_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAR15_SPR_60RND_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMDAR_30rndMagB", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMDAR_30rndMagT", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMDAR_60rndMagB", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMDAR_60rndMagT", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUVSSK_5rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SR3Vikhr_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Bren_30rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M60_100rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUPPSh_71rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFURPD_100rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUSten_32rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAlligator_5rdMag", "MaxPriceThreshold": 35500, "MinPriceThreshold": 33500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAPS_20rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUASh12_20rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAUG_30rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUDVL10M2_5rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUHoneyBadger_30rdMag_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUHoneyBadger_30rdMag_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUKH9_30rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUM1A_20rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUMk12_20rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUP90_50rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUSRSA2_10rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUThompsonMk2_30rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUUzi_32rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUUzi_72rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M1918A2_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_U100_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M76_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AWM_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Agram_32rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_AS50_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Fort12_12rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MP9_30rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_MPX_30rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Snafu_PKP_250RND_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUAX50_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUM24_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUM98B_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFURSASS_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUT5000_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUVECTOR_9mm_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUVECTOR_45acp_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SR25_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M300Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUKivaari_10rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUKivaari_25rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_Mar10_15rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Snafu_ScarH_25RND_Mag_BK", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Snafu_ScarH_25RND_Mag_DKMC", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Snafu_ScarH_25RND_Mag_OD", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>_ScarH_25RND_Mag_Snow", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "<PERSON><PERSON><PERSON>_ScarH_25RND_Mag_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_ScarH_100RND_Mag_Black", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_ScarH_100RND_Mag_Tan", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_G3A3_20rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUGevar_10rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUGM6Lynx_5rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUM96_5rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUPPSKN_30rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFUTac21_5rdMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_M14_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SKS_10Rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_SKS_30Rnd_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SNAFU_FRF2_Mag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SA58_DrumMag", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SA58_30RND_BK", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SA58_30RND_WBK", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "SA58_30RND_DT", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Modified_SA58_30RND_BK", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Modified_SA58_30RND_WBK", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}, {"ClassName": "Modified_SA58_30RND_DT", "MaxPriceThreshold": 1500, "MinPriceThreshold": 1500, "SellPricePercent": -1, "MaxStockThreshold": 1, "MinStockThreshold": 1, "QuantityPercent": -1, "SpawnAttachments": [], "Variants": []}]}